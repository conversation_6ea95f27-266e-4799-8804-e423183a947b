import { describe, it, expect, beforeAll } from 'vitest';
import { advancedContractValidator, SemanticVersionManager } from '@/integration/contract-validator';
import { 
  exampleMicroNote, 
  exampleUIElement, 
  exampleCodePatch, 
  exampleToolIO 
} from '../../examples/schema-validation-examples';

describe('Schema Validation', () => {
  beforeAll(() => {
    // Ensure validator is initialized
    expect(advancedContractValidator).toBeDefined();
  });

  describe('MicroNote Schema', () => {
    it('should validate a valid MicroNote', () => {
      const result = advancedContractValidator.validateMicroNote(exampleMicroNote);
      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should reject MicroNote with invalid UUID', () => {
      const invalidNote = {
        ...exampleMicroNote,
        id: 'invalid-uuid'
      };
      
      const result = advancedContractValidator.validateMicroNote(invalidNote);
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors![0]).toContain('format');
    });

    it('should reject MicroNote with missing required fields', () => {
      const incompleteNote = {
        id: '550e8400-e29b-41d4-a716-************',
        version: '1.0.0'
        // Missing content, timestamps, privacy, sync
      };
      
      const result = advancedContractValidator.validateMicroNote(incompleteNote);
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });

    it('should detect security issues in MicroNote content', () => {
      const maliciousNote = {
        ...exampleMicroNote,
        content: {
          ...exampleMicroNote.content,
          text: 'Malicious content with <script>alert("XSS")</script>'
        }
      };
      
      const result = advancedContractValidator.validateMicroNote(maliciousNote, {
        securityLevel: 'high'
      });
      
      expect(result.securityIssues).toBeDefined();
      expect(result.securityIssues!.length).toBeGreaterThan(0);
      expect(result.securityIssues![0].type).toBe('xss');
    });
  });

  describe('UIElement Schema', () => {
    it('should validate a valid UIElement', () => {
      const result = advancedContractValidator.validateUIElement(exampleUIElement);
      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should reject UIElement with invalid type', () => {
      const invalidElement = {
        ...exampleUIElement,
        type: 'invalid-type'
      };
      
      const result = advancedContractValidator.validateUIElement(invalidElement);
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
    });

    it('should validate UIElement color formats', () => {
      const elementWithColors = {
        ...exampleUIElement,
        styling: {
          ...exampleUIElement.styling,
          color: {
            background: '#ff0000',
            foreground: 'rgb(255, 255, 255)',
            border: 'rgba(0, 0, 0, 0.5)',
            accent: 'transparent'
          }
        }
      };
      
      const result = advancedContractValidator.validateUIElement(elementWithColors);
      expect(result.valid).toBe(true);
    });
  });

  describe('CodePatch Schema', () => {
    it('should validate a valid CodePatch', () => {
      const result = advancedContractValidator.validateCodePatch(exampleCodePatch);
      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should reject CodePatch with invalid operation', () => {
      const invalidPatch = {
        ...exampleCodePatch,
        operation: 'invalid-operation'
      };
      
      const result = advancedContractValidator.validateCodePatch(invalidPatch);
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
    });

    it('should validate CodePatch with different programming languages', () => {
      const languages = ['javascript', 'typescript', 'python', 'java', 'rust'];
      
      languages.forEach(language => {
        const patchWithLanguage = {
          ...exampleCodePatch,
          source: {
            ...exampleCodePatch.source,
            language
          }
        };
        
        const result = advancedContractValidator.validateCodePatch(patchWithLanguage);
        expect(result.valid).toBe(true);
      });
    });
  });

  describe('Tool I/O Schema', () => {
    it('should validate a valid Tool I/O definition', () => {
      const result = advancedContractValidator.validateToolIO(exampleToolIO);
      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
    });

    it('should reject Tool I/O with invalid tool name', () => {
      const invalidToolIO = {
        ...exampleToolIO,
        tool_name: '123-invalid-name' // Should start with letter
      };
      
      const result = advancedContractValidator.validateToolIO(invalidToolIO);
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
    });

    it('should validate Tool I/O authentication methods', () => {
      const authMethods = ['bearer', 'api_key', 'oauth2', 'mtls', 'hmac'];
      
      authMethods.forEach(method => {
        const toolIOWithAuth = {
          ...exampleToolIO,
          security: {
            ...exampleToolIO.security,
            authentication: {
              required: true,
              methods: [method]
            }
          }
        };
        
        const result = advancedContractValidator.validateToolIO(toolIOWithAuth);
        expect(result.valid).toBe(true);
      });
    });
  });

  describe('Performance Metrics', () => {
    it('should provide performance metrics for validation', () => {
      const result = advancedContractValidator.validateMicroNote(exampleMicroNote);
      
      expect(result.performanceMetrics).toBeDefined();
      expect(result.performanceMetrics!.validationTimeMs).toBeGreaterThan(0);
      expect(result.performanceMetrics!.schemaComplexity).toBeGreaterThan(0);
      expect(result.performanceMetrics!.dataSize).toBeGreaterThan(0);
    });

    it('should detect large data and provide suggestions', () => {
      const largeNote = {
        ...exampleMicroNote,
        content: {
          ...exampleMicroNote.content,
          text: 'x'.repeat(1024 * 1024 + 1) // > 1MB
        }
      };
      
      const result = advancedContractValidator.validateMicroNote(largeNote);
      expect(result.suggestions).toBeDefined();
      expect(result.suggestions!.some(s => s.includes('Large data size'))).toBe(true);
    });
  });
});

describe('Semantic Versioning', () => {
  describe('Version Parsing', () => {
    it('should parse valid semantic versions', () => {
      const validVersions = [
        '1.0.0',
        '2.1.3',
        '1.0.0-alpha',
        '1.0.0-alpha.1',
        '1.0.0+build.1'
      ];
      
      validVersions.forEach(version => {
        const parsed = SemanticVersionManager.parseVersion(version);
        expect(parsed).toBeDefined();
        expect(parsed!.major).toBeTypeOf('number');
        expect(parsed!.minor).toBeTypeOf('number');
        expect(parsed!.patch).toBeTypeOf('number');
      });
    });

    it('should reject invalid semantic versions', () => {
      const invalidVersions = [
        '1.0',
        '1.0.0.0',
        'v1.0.0',
        '1.0.0-',
        '1.0.0+'
      ];
      
      invalidVersions.forEach(version => {
        const parsed = SemanticVersionManager.parseVersion(version);
        expect(parsed).toBeNull();
      });
    });
  });

  describe('Version Comparison', () => {
    it('should compare versions correctly', () => {
      expect(SemanticVersionManager.compareVersions('1.0.0', '1.0.1')).toBeLessThan(0);
      expect(SemanticVersionManager.compareVersions('1.1.0', '1.0.0')).toBeGreaterThan(0);
      expect(SemanticVersionManager.compareVersions('1.0.0', '1.0.0')).toBe(0);
      expect(SemanticVersionManager.compareVersions('2.0.0', '1.9.9')).toBeGreaterThan(0);
    });

    it('should handle prerelease versions', () => {
      expect(SemanticVersionManager.compareVersions('1.0.0-alpha', '1.0.0')).toBeLessThan(0);
      expect(SemanticVersionManager.compareVersions('1.0.0-alpha', '1.0.0-beta')).toBeLessThan(0);
    });
  });

  describe('Version Compatibility', () => {
    it('should check backward compatibility', () => {
      const compatibility = SemanticVersionManager.isCompatible('1.2.0', '1.1.0');
      expect(compatibility.isCompatible).toBe(true);
      expect(compatibility.migrationRequired).toBe(false);
    });

    it('should detect breaking changes', () => {
      const compatibility = SemanticVersionManager.isCompatible('1.0.0', '2.0.0');
      expect(compatibility.isCompatible).toBe(false);
      expect(compatibility.migrationRequired).toBe(true);
      expect(compatibility.breakingChanges.length).toBeGreaterThan(0);
    });
  });

  describe('Version Generation', () => {
    it('should generate next versions correctly', () => {
      expect(SemanticVersionManager.getNextVersion('1.0.0', 'patch')).toBe('1.0.1');
      expect(SemanticVersionManager.getNextVersion('1.0.0', 'minor')).toBe('1.1.0');
      expect(SemanticVersionManager.getNextVersion('1.0.0', 'major')).toBe('2.0.0');
    });
  });
});