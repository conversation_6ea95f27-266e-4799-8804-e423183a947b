version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: Dockerfile
      target: builder
    ports:
      - "3000:3000"
      - "9464:9464"  # Prometheus metrics
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/custom_agents
      - REDIS_URL=redis://redis:6379
      - CHROMA_URL=http://chroma:8000
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
    volumes:
      - ../src:/app/src
      - ../package.json:/app/package.json
      - ../tsconfig.json:/app/tsconfig.json
    command: npm run dev
    depends_on:
      - postgres
      - redis
      - chroma
      - jaeger
    networks:
      - agent-network

  postgres:
    image: postgres:16-alpine
    environment:
      - POSTGRES_DB=custom_agents
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - agent-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agent-network

  chroma:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chroma_data:/chroma/chroma
    networks:
      - agent-network

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - agent-network

volumes:
  postgres_data:
  redis_data:
  chroma_data:

networks:
  agent-network:
    driver: bridge