/**
 * MCP Registry System
 * Manages servers, tools, and resources with descriptors
 */

import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { TraceLogger } from '../runtime/trace-logger';
import type { MCPServer, ServerCapabilities, ToolCapability, ResourceCapability, PromptCapability } from './client';

// Registry Types
export interface ServerDescriptor {
  id: string;
  name: string;
  description: string;
  version: string;
  endpoint: string;
  capabilities: ServerCapabilities;
  authentication: AuthenticationDescriptor;
  healthCheck?: HealthCheckDescriptor;
  metadata: ServerMetadata;
  registeredAt: Date;
  lastUpdated: Date;
  status: 'registered' | 'verified' | 'deprecated' | 'disabled';
}

export interface AuthenticationDescriptor {
  type: 'oauth2' | 'apikey' | 'none';
  description: string;
  documentation?: string;
  scopes?: string[];
  endpoints?: {
    authorization?: string;
    token?: string;
    userInfo?: string;
  };
}

export interface HealthCheckDescriptor {
  endpoint: string;
  method: 'GET' | 'POST';
  interval: number;
  timeout: number;
  expectedStatus: number;
  headers?: Record<string, string>;
}

export interface ServerMetadata {
  publisher: string;
  license: string;
  homepage?: string;
  repository?: string;
  documentation?: string;
  tags: string[];
  categories: string[];
  rateLimits?: RateLimitDescriptor;
  pricing?: PricingDescriptor;
  sla?: SLADescriptor;
}

export interface RateLimitDescriptor {
  requests: { limit: number; window: string };
  tokens?: { limit: number; window: string };
  concurrent?: number;
}

export interface PricingDescriptor {
  model: 'free' | 'subscription' | 'pay-per-use' | 'custom';
  currency?: string;
  rates?: { operation: string; cost: number; unit: string }[];
  tiers?: { name: string; description: string; features: string[] }[];
}

export interface SLADescriptor {
  availability: number; // percentage
  responseTime: number; // milliseconds
  support: 'community' | 'email' | 'priority' | 'enterprise';
}

export interface ToolDescriptor {
  serverId: string;
  name: string;
  description: string;
  inputSchema: any; // JSON Schema
  outputSchema?: any; // JSON Schema
  examples?: ToolExample[];
  documentation?: string;
  category: string;
  tags: string[];
  complexity: 'simple' | 'moderate' | 'complex';
  reliability: number; // 0-1 score
  performance: PerformanceMetrics;
  registeredAt: Date;
  lastUpdated: Date;
  status: 'active' | 'deprecated' | 'experimental';
}

export interface ToolExample {
  name: string;
  description: string;
  input: any;
  expectedOutput: any;
  notes?: string;
}

export interface PerformanceMetrics {
  averageLatency: number;
  successRate: number;
  errorRate: number;
  throughput: number;
  lastMeasured: Date;
}

export interface ResourceDescriptor {
  serverId: string;
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
  schema?: any; // JSON Schema for structured resources
  size?: number;
  checksum?: string;
  category: string;
  tags: string[];
  access: AccessDescriptor;
  caching: CachingDescriptor;
  annotations: Record<string, any>;
  registeredAt: Date;
  lastUpdated: Date;
  status: 'available' | 'unavailable' | 'deprecated';
}

export interface AccessDescriptor {
  type: 'public' | 'authenticated' | 'authorized';
  permissions?: string[];
  restrictions?: string[];
}

export interface CachingDescriptor {
  enabled: boolean;
  ttl?: number;
  strategy?: 'aggressive' | 'conservative' | 'none';
}

export interface PromptDescriptor {
  serverId: string;
  name: string;
  description: string;
  template: string;
  arguments: PromptArgumentDescriptor[];
  category: string;
  tags: string[];
  examples?: PromptExample[];
  effectiveness: EffectivenessMetrics;
  registeredAt: Date;
  lastUpdated: Date;
  status: 'active' | 'deprecated' | 'experimental';
}

export interface PromptArgumentDescriptor {
  name: string;
  description: string;
  type: string;
  required: boolean;
  default?: any;
  validation?: any; // JSON Schema
}

export interface PromptExample {
  name: string;
  description: string;
  arguments: Record<string, any>;
  expectedBehavior: string;
}

export interface EffectivenessMetrics {
  successRate: number;
  averageQuality: number;
  userSatisfaction: number;
  lastMeasured: Date;
}

// Search and Discovery Types
export interface SearchQuery {
  text?: string;
  category?: string;
  tags?: string[];
  type: 'servers' | 'tools' | 'resources' | 'prompts' | 'all';
  filters?: SearchFilters;
  sort?: SortOptions;
  limit?: number;
  offset?: number;
}

export interface SearchFilters {
  status?: string[];
  authentication?: string[];
  complexity?: string[];
  reliability?: { min?: number; max?: number };
  performance?: { latency?: number; successRate?: number };
  pricing?: string[];
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  query: SearchQuery;
  executionTime: number;
}

// Registry Configuration
export interface RegistryConfig {
  enableCaching: boolean;
  cacheSize: number;
  cacheTTL: number;
  enableMetrics: boolean;
  enableValidation: boolean;
  enableTracing: boolean;
  healthCheckInterval: number;
  cleanupInterval: number;
}

/**
 * MCP Registry - Central registry for MCP servers, tools, and resources
 */
export class MCPRegistry extends EventEmitter {
  private config: RegistryConfig;
  private tracer: TraceLogger;
  
  // Storage
  private servers = new Map<string, ServerDescriptor>();
  private tools = new Map<string, ToolDescriptor>();
  private resources = new Map<string, ResourceDescriptor>();
  private prompts = new Map<string, PromptDescriptor>();
  
  // Indexing for fast search
  private serversByCategory = new Map<string, Set<string>>();
  private toolsByCategory = new Map<string, Set<string>>();
  private resourcesByCategory = new Map<string, Set<string>>();
  private promptsByCategory = new Map<string, Set<string>>();
  
  private tagIndex = new Map<string, Set<string>>();
  
  // Caching
  private searchCache = new Map<string, { result: any; timestamp: number }>();
  
  // Health monitoring
  private healthCheckIntervals = new Map<string, NodeJS.Timeout>();

  constructor(config: Partial<RegistryConfig> = {}) {
    super();
    
    this.config = {
      enableCaching: true,
      cacheSize: 1000,
      cacheTTL: 300000, // 5 minutes
      enableMetrics: true,
      enableValidation: true,
      enableTracing: true,
      healthCheckInterval: 60000, // 1 minute
      cleanupInterval: 300000, // 5 minutes
      ...config,
    };
    
    this.tracer = new TraceLogger({
      serviceName: 'mcp-registry',
      enableConsoleSpans: this.config.enableTracing,
    });
    
    // Start cleanup interval
    if (this.config.cleanupInterval > 0) {
      setInterval(() => this.cleanup(), this.config.cleanupInterval);
    }
    
    logger.info('MCP Registry initialized', {
      caching: this.config.enableCaching,
      metrics: this.config.enableMetrics,
      validation: this.config.enableValidation,
    });
  }

  /**
   * Register MCP Server
   */
  async registerServer(descriptor: Omit<ServerDescriptor, 'registeredAt' | 'lastUpdated'>): Promise<void> {
    const span = this.tracer.startSpan('registry_register_server', {
      serverId: descriptor.id,
      serverName: descriptor.name,
    });

    try {
      if (this.config.enableValidation) {
        this.validateServerDescriptor(descriptor);
      }

      const serverDescriptor: ServerDescriptor = {
        ...descriptor,
        registeredAt: new Date(),
        lastUpdated: new Date(),
      };

      this.servers.set(descriptor.id, serverDescriptor);
      
      // Update indexes
      this.updateCategoryIndex(this.serversByCategory, descriptor.id, descriptor.metadata.categories);
      this.updateTagIndex(descriptor.id, descriptor.metadata.tags);
      
      // Start health monitoring if configured
      if (descriptor.healthCheck) {
        this.startHealthMonitoring(descriptor.id, descriptor.healthCheck);
      }
      
      // Clear related caches
      this.clearSearchCache();
      
      logger.info('Server registered successfully', {
        serverId: descriptor.id,
        name: descriptor.name,
        version: descriptor.version,
      });
      
      this.emit('server:registered', descriptor.id, serverDescriptor);
      
    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to register server', { serverId: descriptor.id, error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Register Tool
   */
  async registerTool(descriptor: Omit<ToolDescriptor, 'registeredAt' | 'lastUpdated'>): Promise<void> {
    const span = this.tracer.startSpan('registry_register_tool', {
      serverId: descriptor.serverId,
      toolName: descriptor.name,
    });

    try {
      if (this.config.enableValidation) {
        this.validateToolDescriptor(descriptor);
      }

      const toolDescriptor: ToolDescriptor = {
        ...descriptor,
        registeredAt: new Date(),
        lastUpdated: new Date(),
      };

      const toolKey = `${descriptor.serverId}:${descriptor.name}`;
      this.tools.set(toolKey, toolDescriptor);
      
      // Update indexes
      this.updateCategoryIndex(this.toolsByCategory, toolKey, [descriptor.category]);
      this.updateTagIndex(toolKey, descriptor.tags);
      
      // Clear related caches
      this.clearSearchCache();
      
      logger.info('Tool registered successfully', {
        serverId: descriptor.serverId,
        toolName: descriptor.name,
        category: descriptor.category,
      });
      
      this.emit('tool:registered', toolKey, toolDescriptor);
      
    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to register tool', { 
        serverId: descriptor.serverId, 
        toolName: descriptor.name, 
        error 
      });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Register Resource
   */
  async registerResource(descriptor: Omit<ResourceDescriptor, 'registeredAt' | 'lastUpdated'>): Promise<void> {
    const span = this.tracer.startSpan('registry_register_resource', {
      serverId: descriptor.serverId,
      resourceUri: descriptor.uri,
    });

    try {
      if (this.config.enableValidation) {
        this.validateResourceDescriptor(descriptor);
      }

      const resourceDescriptor: ResourceDescriptor = {
        ...descriptor,
        registeredAt: new Date(),
        lastUpdated: new Date(),
      };

      const resourceKey = `${descriptor.serverId}:${descriptor.uri}`;
      this.resources.set(resourceKey, resourceDescriptor);
      
      // Update indexes
      this.updateCategoryIndex(this.resourcesByCategory, resourceKey, [descriptor.category]);
      this.updateTagIndex(resourceKey, descriptor.tags);
      
      // Clear related caches
      this.clearSearchCache();
      
      logger.info('Resource registered successfully', {
        serverId: descriptor.serverId,
        uri: descriptor.uri,
        category: descriptor.category,
      });
      
      this.emit('resource:registered', resourceKey, resourceDescriptor);
      
    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to register resource', { 
        serverId: descriptor.serverId, 
        uri: descriptor.uri, 
        error 
      });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Register Prompt
   */
  async registerPrompt(descriptor: Omit<PromptDescriptor, 'registeredAt' | 'lastUpdated'>): Promise<void> {
    const span = this.tracer.startSpan('registry_register_prompt', {
      serverId: descriptor.serverId,
      promptName: descriptor.name,
    });

    try {
      if (this.config.enableValidation) {
        this.validatePromptDescriptor(descriptor);
      }

      const promptDescriptor: PromptDescriptor = {
        ...descriptor,
        registeredAt: new Date(),
        lastUpdated: new Date(),
      };

      const promptKey = `${descriptor.serverId}:${descriptor.name}`;
      this.prompts.set(promptKey, promptDescriptor);
      
      // Update indexes
      this.updateCategoryIndex(this.promptsByCategory, promptKey, [descriptor.category]);
      this.updateTagIndex(promptKey, descriptor.tags);
      
      // Clear related caches
      this.clearSearchCache();
      
      logger.info('Prompt registered successfully', {
        serverId: descriptor.serverId,
        promptName: descriptor.name,
        category: descriptor.category,
      });
      
      this.emit('prompt:registered', promptKey, promptDescriptor);
      
    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to register prompt', { 
        serverId: descriptor.serverId, 
        promptName: descriptor.name, 
        error 
      });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Search across all registry items
   */
  async search<T = any>(query: SearchQuery): Promise<SearchResult<T>> {
    const span = this.tracer.startSpan('registry_search', {
      queryType: query.type,
      hasText: !!query.text,
      hasFilters: !!query.filters,
    });

    const startTime = Date.now();

    try {
      // Check cache first
      const cacheKey = JSON.stringify(query);
      if (this.config.enableCaching) {
        const cached = this.searchCache.get(cacheKey);
        if (cached && (Date.now() - cached.timestamp) < this.config.cacheTTL) {
          span.setAttributes({ cached: true });
          return cached.result;
        }
      }

      let results: T[] = [];

      switch (query.type) {
        case 'servers':
          results = this.searchServers(query) as T[];
          break;
        case 'tools':
          results = this.searchTools(query) as T[];
          break;
        case 'resources':
          results = this.searchResources(query) as T[];
          break;
        case 'prompts':
          results = this.searchPrompts(query) as T[];
          break;
        case 'all':
          results = [
            ...this.searchServers(query),
            ...this.searchTools(query),
            ...this.searchResources(query),
            ...this.searchPrompts(query),
          ] as T[];
          break;
      }

      // Apply sorting
      if (query.sort) {
        results.sort((a: any, b: any) => {
          const aVal = a[query.sort!.field];
          const bVal = b[query.sort!.field];
          const direction = query.sort!.direction === 'asc' ? 1 : -1;
          
          if (aVal < bVal) return -1 * direction;
          if (aVal > bVal) return 1 * direction;
          return 0;
        });
      }

      // Apply pagination
      const total = results.length;
      const offset = query.offset || 0;
      const limit = query.limit || 50;
      results = results.slice(offset, offset + limit);

      const searchResult: SearchResult<T> = {
        items: results,
        total,
        query,
        executionTime: Date.now() - startTime,
      };

      // Cache result
      if (this.config.enableCaching) {
        this.searchCache.set(cacheKey, {
          result: searchResult,
          timestamp: Date.now(),
        });
        
        // Cleanup cache if too large
        if (this.searchCache.size > this.config.cacheSize) {
          const oldestKey = this.searchCache.keys().next().value;
          this.searchCache.delete(oldestKey);
        }
      }

      span.setAttributes({
        resultsCount: results.length,
        totalMatches: total,
        executionTime: searchResult.executionTime,
      });

      return searchResult;

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Search failed', { query, error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Get server by ID
   */
  getServer(serverId: string): ServerDescriptor | undefined {
    return this.servers.get(serverId);
  }

  /**
   * Get tool by server and name
   */
  getTool(serverId: string, toolName: string): ToolDescriptor | undefined {
    return this.tools.get(`${serverId}:${toolName}`);
  }

  /**
   * Get resource by server and URI
   */
  getResource(serverId: string, uri: string): ResourceDescriptor | undefined {
    return this.resources.get(`${serverId}:${uri}`);
  }

  /**
   * Get prompt by server and name
   */
  getPrompt(serverId: string, promptName: string): PromptDescriptor | undefined {
    return this.prompts.get(`${serverId}:${promptName}`);
  }

  /**
   * List all servers
   */
  listServers(): ServerDescriptor[] {
    return Array.from(this.servers.values());
  }

  /**
   * List tools for a server
   */
  listTools(serverId?: string): ToolDescriptor[] {
    const tools = Array.from(this.tools.values());
    return serverId ? tools.filter(tool => tool.serverId === serverId) : tools;
  }

  /**
   * List resources for a server
   */
  listResources(serverId?: string): ResourceDescriptor[] {
    const resources = Array.from(this.resources.values());
    return serverId ? resources.filter(resource => resource.serverId === serverId) : resources;
  }

  /**
   * List prompts for a server
   */
  listPrompts(serverId?: string): PromptDescriptor[] {
    const prompts = Array.from(this.prompts.values());
    return serverId ? prompts.filter(prompt => prompt.serverId === serverId) : prompts;
  }

  /**
   * Get registry statistics
   */
  getStatistics(): {
    servers: number;
    tools: number;
    resources: number;
    prompts: number;
    categories: number;
    tags: number;
    cacheSize: number;
  } {
    return {
      servers: this.servers.size,
      tools: this.tools.size,
      resources: this.resources.size,
      prompts: this.prompts.size,
      categories: this.serversByCategory.size + this.toolsByCategory.size + 
                 this.resourcesByCategory.size + this.promptsByCategory.size,
      tags: this.tagIndex.size,
      cacheSize: this.searchCache.size,
    };
  }

  // Private helper methods

  private searchServers(query: SearchQuery): ServerDescriptor[] {
    let servers = Array.from(this.servers.values());

    if (query.text) {
      const searchText = query.text.toLowerCase();
      servers = servers.filter(server =>
        server.name.toLowerCase().includes(searchText) ||
        server.description.toLowerCase().includes(searchText) ||
        server.metadata.tags.some(tag => tag.toLowerCase().includes(searchText))
      );
    }

    if (query.category) {
      servers = servers.filter(server =>
        server.metadata.categories.includes(query.category!)
      );
    }

    if (query.tags?.length) {
      servers = servers.filter(server =>
        query.tags!.some(tag => server.metadata.tags.includes(tag))
      );
    }

    if (query.filters) {
      servers = this.applyServerFilters(servers, query.filters);
    }

    return servers;
  }

  private searchTools(query: SearchQuery): ToolDescriptor[] {
    let tools = Array.from(this.tools.values());

    if (query.text) {
      const searchText = query.text.toLowerCase();
      tools = tools.filter(tool =>
        tool.name.toLowerCase().includes(searchText) ||
        tool.description.toLowerCase().includes(searchText) ||
        tool.tags.some(tag => tag.toLowerCase().includes(searchText))
      );
    }

    if (query.category) {
      tools = tools.filter(tool => tool.category === query.category);
    }

    if (query.tags?.length) {
      tools = tools.filter(tool =>
        query.tags!.some(tag => tool.tags.includes(tag))
      );
    }

    if (query.filters) {
      tools = this.applyToolFilters(tools, query.filters);
    }

    return tools;
  }

  private searchResources(query: SearchQuery): ResourceDescriptor[] {
    let resources = Array.from(this.resources.values());

    if (query.text) {
      const searchText = query.text.toLowerCase();
      resources = resources.filter(resource =>
        resource.name.toLowerCase().includes(searchText) ||
        (resource.description && resource.description.toLowerCase().includes(searchText)) ||
        resource.tags.some(tag => tag.toLowerCase().includes(searchText))
      );
    }

    if (query.category) {
      resources = resources.filter(resource => resource.category === query.category);
    }

    if (query.tags?.length) {
      resources = resources.filter(resource =>
        query.tags!.some(tag => resource.tags.includes(tag))
      );
    }

    if (query.filters) {
      resources = this.applyResourceFilters(resources, query.filters);
    }

    return resources;
  }

  private searchPrompts(query: SearchQuery): PromptDescriptor[] {
    let prompts = Array.from(this.prompts.values());

    if (query.text) {
      const searchText = query.text.toLowerCase();
      prompts = prompts.filter(prompt =>
        prompt.name.toLowerCase().includes(searchText) ||
        prompt.description.toLowerCase().includes(searchText) ||
        prompt.tags.some(tag => tag.toLowerCase().includes(searchText))
      );
    }

    if (query.category) {
      prompts = prompts.filter(prompt => prompt.category === query.category);
    }

    if (query.tags?.length) {
      prompts = prompts.filter(prompt =>
        query.tags!.some(tag => prompt.tags.includes(tag))
      );
    }

    if (query.filters) {
      prompts = this.applyPromptFilters(prompts, query.filters);
    }

    return prompts;
  }

  private applyServerFilters(servers: ServerDescriptor[], filters: SearchFilters): ServerDescriptor[] {
    if (filters.status?.length) {
      servers = servers.filter(server => filters.status!.includes(server.status));
    }

    if (filters.authentication?.length) {
      servers = servers.filter(server => filters.authentication!.includes(server.authentication.type));
    }

    if (filters.pricing?.length) {
      servers = servers.filter(server => 
        server.metadata.pricing && filters.pricing!.includes(server.metadata.pricing.model)
      );
    }

    return servers;
  }

  private applyToolFilters(tools: ToolDescriptor[], filters: SearchFilters): ToolDescriptor[] {
    if (filters.status?.length) {
      tools = tools.filter(tool => filters.status!.includes(tool.status));
    }

    if (filters.complexity?.length) {
      tools = tools.filter(tool => filters.complexity!.includes(tool.complexity));
    }

    if (filters.reliability) {
      const { min, max } = filters.reliability;
      tools = tools.filter(tool => {
        if (min !== undefined && tool.reliability < min) return false;
        if (max !== undefined && tool.reliability > max) return false;
        return true;
      });
    }

    if (filters.performance) {
      if (filters.performance.latency !== undefined) {
        tools = tools.filter(tool => tool.performance.averageLatency <= filters.performance!.latency!);
      }
      if (filters.performance.successRate !== undefined) {
        tools = tools.filter(tool => tool.performance.successRate >= filters.performance!.successRate!);
      }
    }

    return tools;
  }

  private applyResourceFilters(resources: ResourceDescriptor[], filters: SearchFilters): ResourceDescriptor[] {
    if (filters.status?.length) {
      resources = resources.filter(resource => filters.status!.includes(resource.status));
    }

    return resources;
  }

  private applyPromptFilters(prompts: PromptDescriptor[], filters: SearchFilters): PromptDescriptor[] {
    if (filters.status?.length) {
      prompts = prompts.filter(prompt => filters.status!.includes(prompt.status));
    }

    return prompts;
  }

  private updateCategoryIndex(index: Map<string, Set<string>>, itemId: string, categories: string[]): void {
    for (const category of categories) {
      if (!index.has(category)) {
        index.set(category, new Set());
      }
      index.get(category)!.add(itemId);
    }
  }

  private updateTagIndex(itemId: string, tags: string[]): void {
    for (const tag of tags) {
      if (!this.tagIndex.has(tag)) {
        this.tagIndex.set(tag, new Set());
      }
      this.tagIndex.get(tag)!.add(itemId);
    }
  }

  private clearSearchCache(): void {
    if (this.config.enableCaching) {
      this.searchCache.clear();
    }
  }

  private validateServerDescriptor(descriptor: any): void {
    if (!descriptor.id || typeof descriptor.id !== 'string') {
      throw new Error('Server descriptor must have a valid ID');
    }
    if (!descriptor.name || typeof descriptor.name !== 'string') {
      throw new Error('Server descriptor must have a valid name');
    }
    if (!descriptor.endpoint || typeof descriptor.endpoint !== 'string') {
      throw new Error('Server descriptor must have a valid endpoint');
    }
    // Add more validation as needed
  }

  private validateToolDescriptor(descriptor: any): void {
    if (!descriptor.serverId || typeof descriptor.serverId !== 'string') {
      throw new Error('Tool descriptor must have a valid serverId');
    }
    if (!descriptor.name || typeof descriptor.name !== 'string') {
      throw new Error('Tool descriptor must have a valid name');
    }
    if (!descriptor.inputSchema) {
      throw new Error('Tool descriptor must have an input schema');
    }
    // Add more validation as needed
  }

  private validateResourceDescriptor(descriptor: any): void {
    if (!descriptor.serverId || typeof descriptor.serverId !== 'string') {
      throw new Error('Resource descriptor must have a valid serverId');
    }
    if (!descriptor.uri || typeof descriptor.uri !== 'string') {
      throw new Error('Resource descriptor must have a valid URI');
    }
    // Add more validation as needed
  }

  private validatePromptDescriptor(descriptor: any): void {
    if (!descriptor.serverId || typeof descriptor.serverId !== 'string') {
      throw new Error('Prompt descriptor must have a valid serverId');
    }
    if (!descriptor.name || typeof descriptor.name !== 'string') {
      throw new Error('Prompt descriptor must have a valid name');
    }
    if (!descriptor.template || typeof descriptor.template !== 'string') {
      throw new Error('Prompt descriptor must have a valid template');
    }
    // Add more validation as needed
  }

  private startHealthMonitoring(serverId: string, healthCheck: HealthCheckDescriptor): void {
    if (this.healthCheckIntervals.has(serverId)) {
      clearInterval(this.healthCheckIntervals.get(serverId));
    }

    const interval = setInterval(async () => {
      try {
        const response = await fetch(healthCheck.endpoint, {
          method: healthCheck.method,
          headers: healthCheck.headers,
          signal: AbortSignal.timeout(healthCheck.timeout),
        });

        const isHealthy = response.status === healthCheck.expectedStatus;
        this.emit('server:health', serverId, isHealthy, response.status);

        if (!isHealthy) {
          logger.warn('Server health check failed', {
            serverId,
            expectedStatus: healthCheck.expectedStatus,
            actualStatus: response.status,
          });
        }
      } catch (error) {
        logger.error('Server health check error', { serverId, error });
        this.emit('server:health', serverId, false, 0);
      }
    }, healthCheck.interval);

    this.healthCheckIntervals.set(serverId, interval);
  }

  private cleanup(): void {
    // Clean up expired cache entries
    const now = Date.now();
    for (const [key, cached] of this.searchCache.entries()) {
      if (now - cached.timestamp > this.config.cacheTTL) {
        this.searchCache.delete(key);
      }
    }

    logger.debug('Registry cleanup completed', {
      cacheSize: this.searchCache.size,
    });
  }

  /**
   * Shutdown registry and cleanup resources
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down MCP registry');

    // Clear all health check intervals
    for (const interval of this.healthCheckIntervals.values()) {
      clearInterval(interval);
    }
    this.healthCheckIntervals.clear();

    // Clear all data
    this.servers.clear();
    this.tools.clear();
    this.resources.clear();
    this.prompts.clear();
    this.searchCache.clear();

    this.removeAllListeners();
  }
}