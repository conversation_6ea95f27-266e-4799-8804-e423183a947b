import Fastify, { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import websocket from '@fastify/websocket';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';
import { SessionManager } from './session-manager';
import { PolicyEnforcer } from './policy-enforcer';
import { TraceLogger } from './trace-logger';

export interface HostRuntimeConfig {
  host: string;
  port: number;
  cors: {
    origin: string[] | boolean;
    credentials: boolean;
  };
  rateLimit: {
    max: number;
    timeWindow: string;
  };
  websocket: {
    options: {
      maxPayload: number;
      idleTimeout: number;
    };
  };
  security: {
    helmet: boolean;
    apiKeyRequired: boolean;
  };
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: string;
    version: string;
  };
}

export interface StreamingResponse {
  id: string;
  event: string;
  data: any;
  timestamp: string;
}

export class HostRuntime {
  private server: FastifyInstance;
  private sessionManager: SessionManager;
  private policyEnforcer: PolicyEnforcer;
  private traceLogger: TraceLogger;
  private config: HostRuntimeConfig;

  constructor(config: HostRuntimeConfig) {
    this.config = config;
    this.server = Fastify({
      logger: false, // Use our custom logger
      requestIdHeader: 'x-request-id',
      requestIdLogLabel: 'requestId',
      genReqId: () => crypto.randomUUID(),
    });

    this.sessionManager = new SessionManager();
    this.policyEnforcer = new PolicyEnforcer();
    this.traceLogger = new TraceLogger();

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocketHandlers();
    this.setupErrorHandlers();
  }

  private async setupMiddleware(): Promise<void> {
    // Security middleware
    if (this.config.security.helmet) {
      await this.server.register(helmet, {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", 'data:', 'https:'],
          },
        },
      });
    }

    // CORS
    await this.server.register(cors, {
      origin: this.config.cors.origin,
      credentials: this.config.cors.credentials,
    });

    // Rate limiting
    await this.server.register(rateLimit, {
      max: this.config.rateLimit.max,
      timeWindow: this.config.rateLimit.timeWindow,
    });

    // WebSocket support
    await this.server.register(websocket, {
      options: this.config.websocket.options,
    });

    // Request logging and metrics
    this.server.addHook('onRequest', async (request: FastifyRequest) => {
      const startTime = Date.now();
      request.startTime = startTime;
      
      this.traceLogger.logRequest(request);
      applicationMetrics.incrementActiveConnections();
    });

    this.server.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
      const duration = Date.now() - (request.startTime || Date.now());
      
      applicationMetrics.recordHttpRequest(
        request.method,
        request.routerPath || request.url,
        reply.statusCode
      );
      
      applicationMetrics.recordHttpRequestDuration(
        duration / 1000,
        request.method,
        request.routerPath || request.url,
        reply.statusCode
      );
      
      applicationMetrics.decrementActiveConnections();
      
      this.traceLogger.logResponse(request, reply, duration);
    });

    // Authentication middleware
    this.server.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
      // Skip auth for health checks and public endpoints
      if (request.url.startsWith('/health') || request.url.startsWith('/metrics')) {
        return;
      }

      if (this.config.security.apiKeyRequired) {
        const apiKey = request.headers['x-api-key'] as string;
        if (!apiKey) {
          reply.status(401).send({
            success: false,
            error: {
              code: 'MISSING_API_KEY',
              message: 'API key is required',
            },
          });
          return;
        }

        // Validate API key (implement your validation logic)
        const isValid = await this.validateApiKey(apiKey);
        if (!isValid) {
          reply.status(401).send({
            success: false,
            error: {
              code: 'INVALID_API_KEY',
              message: 'Invalid API key',
            },
          });
          return;
        }
      }

      // Policy enforcement
      const policyResult = await this.policyEnforcer.enforce(request);
      if (!policyResult.allowed) {
        reply.status(403).send({
          success: false,
          error: {
            code: 'POLICY_VIOLATION',
            message: policyResult.reason,
          },
        });
        return;
      }
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.server.get('/health', async (request: FastifyRequest, reply: FastifyReply) => {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0',
      };

      reply.send({
        success: true,
        data: health,
      });
    });

    // Session management endpoints
    this.server.post('/sessions', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const session = await this.sessionManager.createSession(request.body as any);
        reply.status(201).send(this.formatResponse(session, request.id));
      } catch (error) {
        this.handleError(error, reply, request.id);
      }
    });

    this.server.get('/sessions/:sessionId', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { sessionId } = request.params as { sessionId: string };
        const session = await this.sessionManager.getSession(sessionId);
        
        if (!session) {
          reply.status(404).send(this.formatError('SESSION_NOT_FOUND', 'Session not found', request.id));
          return;
        }

        reply.send(this.formatResponse(session, request.id));
      } catch (error) {
        this.handleError(error, reply, request.id);
      }
    });

    this.server.delete('/sessions/:sessionId', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const { sessionId } = request.params as { sessionId: string };
        await this.sessionManager.destroySession(sessionId);
        reply.status(204).send();
      } catch (error) {
        this.handleError(error, reply, request.id);
      }
    });

    // Model execution endpoints
    this.server.post('/models/execute', async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        // This will be connected to MEL in Day 2
        const result = { message: 'Model execution endpoint - to be implemented with MEL' };
        reply.send(this.formatResponse(result, request.id));
      } catch (error) {
        this.handleError(error, reply, request.id);
      }
    });

    // Streaming endpoint for model tokens
    this.server.get('/models/stream/:sessionId', { websocket: true }, (connection, request) => {
      const { sessionId } = request.params as { sessionId: string };
      
      connection.socket.on('message', async (message) => {
        try {
          const data = JSON.parse(message.toString());
          await this.handleStreamingRequest(sessionId, data, connection);
        } catch (error) {
          logger.error('WebSocket message error', { error, sessionId });
          connection.socket.send(JSON.stringify({
            event: 'error',
            data: { message: 'Invalid message format' }
          }));
        }
      });

      connection.socket.on('close', () => {
        logger.info('WebSocket connection closed', { sessionId });
      });
    });

    // Metrics endpoint
    this.server.get('/metrics', async (request: FastifyRequest, reply: FastifyReply) => {
      // Return Prometheus-compatible metrics
      reply.type('text/plain').send('# Metrics endpoint - integrate with Prometheus exporter');
    });
  }

  private setupWebSocketHandlers(): void {
    // WebSocket connection handler for real-time communication
    this.server.register(async (fastify) => {
      fastify.get('/ws', { websocket: true }, (connection, request) => {
        logger.info('New WebSocket connection established');

        connection.socket.on('message', async (message) => {
          try {
            const data = JSON.parse(message.toString());
            await this.handleWebSocketMessage(data, connection);
          } catch (error) {
            logger.error('WebSocket error', { error });
            connection.socket.send(JSON.stringify({
              event: 'error',
              data: { message: 'Message processing failed' }
            }));
          }
        });

        connection.socket.on('close', () => {
          logger.info('WebSocket connection closed');
        });
      });
    });
  }

  private setupErrorHandlers(): void {
    this.server.setErrorHandler((error, request, reply) => {
      logger.error('Unhandled request error', {
        error: error.message,
        stack: error.stack,
        url: request.url,
        method: request.method,
      });

      applicationMetrics.recordError('unhandled_request_error', 'host_runtime');

      const statusCode = error.statusCode || 500;
      reply.status(statusCode).send(this.formatError(
        'INTERNAL_ERROR',
        'An internal error occurred',
        request.id
      ));
    });

    this.server.setNotFoundHandler((request, reply) => {
      reply.status(404).send(this.formatError(
        'NOT_FOUND',
        `Route ${request.method} ${request.url} not found`,
        request.id
      ));
    });
  }

  private async handleStreamingRequest(
    sessionId: string,
    data: any,
    connection: any
  ): Promise<void> {
    // Mock streaming implementation - will be replaced with actual model streaming
    const mockTokens = ['Hello', ' ', 'world', '!', ' ', 'This', ' ', 'is', ' ', 'a', ' ', 'streaming', ' ', 'response', '.'];
    
    for (let i = 0; i < mockTokens.length; i++) {
      const streamData: StreamingResponse = {
        id: crypto.randomUUID(),
        event: 'token',
        data: {
          token: mockTokens[i],
          index: i,
          sessionId,
        },
        timestamp: new Date().toISOString(),
      };

      connection.socket.send(JSON.stringify(streamData));
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Send completion event
    const completionData: StreamingResponse = {
      id: crypto.randomUUID(),
      event: 'completion',
      data: {
        sessionId,
        totalTokens: mockTokens.length,
      },
      timestamp: new Date().toISOString(),
    };

    connection.socket.send(JSON.stringify(completionData));
  }

  private async handleWebSocketMessage(data: any, connection: any): Promise<void> {
    // Handle general WebSocket messages
    logger.debug('Received WebSocket message', { data });
    
    connection.socket.send(JSON.stringify({
      event: 'ack',
      data: { received: true, timestamp: new Date().toISOString() }
    }));
  }

  private async validateApiKey(apiKey: string): Promise<boolean> {
    // Implement your API key validation logic here
    // For now, return true for demo purposes
    return apiKey.length > 0;
  }

  private formatResponse<T>(data: T, requestId: string): APIResponse<T> {
    return {
      success: true,
      data,
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    };
  }

  private formatError(code: string, message: string, requestId: string, details?: any): APIResponse {
    return {
      success: false,
      error: {
        code,
        message,
        details,
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    };
  }

  private handleError(error: any, reply: FastifyReply, requestId: string): void {
    logger.error('Request processing error', { error });
    
    const statusCode = error.statusCode || 500;
    const code = error.code || 'INTERNAL_ERROR';
    const message = error.message || 'An error occurred';

    reply.status(statusCode).send(this.formatError(code, message, requestId));
  }

  async start(): Promise<void> {
    try {
      await this.server.listen({
        host: this.config.host,
        port: this.config.port,
      });

      logger.info('Host Runtime started successfully', {
        host: this.config.host,
        port: this.config.port,
      });
    } catch (error) {
      logger.error('Failed to start Host Runtime', { error });
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      await this.server.close();
      logger.info('Host Runtime stopped successfully');
    } catch (error) {
      logger.error('Error stopping Host Runtime', { error });
      throw error;
    }
  }

  getServer(): FastifyInstance {
    return this.server;
  }
}

// Extend FastifyRequest interface for custom properties
declare module 'fastify' {
  interface FastifyRequest {
    startTime?: number;
  }
}