#!/bin/bash

set -e

echo "🔐 Running comprehensive security checks..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    echo "Checking dependencies..."
    
    REQUIRED_TOOLS=("npm" "docker" "git")
    for tool in "${REQUIRED_TOOLS[@]}"; do
        if command -v "$tool" &> /dev/null; then
            print_status "$tool is installed"
        else
            print_error "$tool is required but not installed"
            exit 1
        fi
    done
}

# Run npm audit
run_npm_audit() {
    echo "Running npm audit..."
    if npm audit --audit-level moderate; then
        print_status "npm audit passed"
    else
        print_warning "npm audit found vulnerabilities"
        echo "Run 'npm audit fix' to resolve issues"
    fi
}

# Check for hardcoded secrets
check_secrets() {
    echo "Checking for hardcoded secrets..."
    
    # Patterns to look for
    SECRET_PATTERNS=(
        "password\s*=\s*['\"][^'\"]+['\"]"
        "api[_-]?key\s*=\s*['\"][^'\"]+['\"]"
        "secret\s*=\s*['\"][^'\"]+['\"]"
        "token\s*=\s*['\"][^'\"]+['\"]"
        "private[_-]?key"
        "-----BEGIN"
    )
    
    found_secrets=false
    for pattern in "${SECRET_PATTERNS[@]}"; do
        if grep -r -i -E "$pattern" src/ --exclude-dir=node_modules 2>/dev/null; then
            found_secrets=true
        fi
    done
    
    if [ "$found_secrets" = true ]; then
        print_error "Potential secrets found in code"
        exit 1
    else
        print_status "No hardcoded secrets detected"
    fi
}

# Check file permissions
check_permissions() {
    echo "Checking file permissions..."
    
    # Check for overly permissive files
    if find . -type f -perm /o+w -not -path "./node_modules/*" -not -path "./.git/*" | grep -q .; then
        print_warning "Found world-writable files"
        find . -type f -perm /o+w -not -path "./node_modules/*" -not -path "./.git/*"
    else
        print_status "File permissions are secure"
    fi
}

# Validate Docker security
check_docker_security() {
    echo "Checking Dockerfile security..."
    
    if [ -f "Dockerfile" ]; then
        # Check if running as root
        if grep -q "USER root\|^USER 0" Dockerfile; then
            print_warning "Dockerfile runs as root user"
        else
            print_status "Dockerfile uses non-root user"
        fi
        
        # Check for ADD instead of COPY
        if grep -q "^ADD" Dockerfile; then
            print_warning "Consider using COPY instead of ADD"
        fi
        
        # Check for latest tags
        if grep -q ":latest" Dockerfile; then
            print_warning "Consider using specific version tags instead of 'latest'"
        fi
    fi
}

# Check environment variables
check_env_vars() {
    echo "Checking environment variable security..."
    
    if [ -f ".env" ]; then
        print_warning ".env file found - ensure it's in .gitignore"
        if git check-ignore .env >/dev/null 2>&1; then
            print_status ".env is properly ignored by git"
        else
            print_error ".env is not in .gitignore"
            exit 1
        fi
    fi
    
    # Check for .env.example
    if [ -f ".env.example" ]; then
        print_status ".env.example found for reference"
    else
        print_warning "Consider creating .env.example for documentation"
    fi
}

# Validate SSL/TLS configuration
check_tls_config() {
    echo "Checking TLS configuration..."
    
    if grep -r "ssl\|tls" config/ 2>/dev/null; then
        print_status "TLS configuration found"
    else
        print_warning "No TLS configuration detected"
    fi
}

# Main execution
main() {
    echo "Starting security audit for Custom Agent System..."
    echo "=================================================="
    
    check_dependencies
    run_npm_audit
    check_secrets
    check_permissions
    check_docker_security
    check_env_vars
    check_tls_config
    
    echo "=================================================="
    print_status "Security audit completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Review any warnings above"
    echo "2. Run 'npm run security:scan' for detailed scanning"
    echo "3. Consider running 'docker run --rm -v \$(pwd):/app returntocorp/semgrep --config=auto /app'"
}

# Run main function
main "$@"