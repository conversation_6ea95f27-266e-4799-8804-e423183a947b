import { EventEmitter } from 'events';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface StreamingToken {
  id: string;
  sessionId: string;
  token: string;
  index: number;
  timestamp: Date;
  metadata?: {
    probability?: number;
    logprob?: number;
    isComplete?: boolean;
    model?: string;
  };
}

export interface StreamingRequest {
  sessionId: string;
  prompt: string;
  model: string;
  parameters: {
    maxTokens: number;
    temperature: number;
    topP: number;
    stopSequences: string[];
  };
  streaming: {
    chunkSize: number;
    delay: number; // ms between tokens
    includeMetadata: boolean;
  };
}

export interface StreamingResponse {
  id: string;
  sessionId: string;
  event: StreamingEventType;
  data: any;
  timestamp: Date;
}

export enum StreamingEventType {
  START = 'stream_start',
  TOKEN = 'token',
  CHUNK = 'chunk',
  METADATA = 'metadata',
  ERROR = 'error',
  COMPLETE = 'stream_complete',
  ABORT = 'stream_abort',
}

export interface StreamingStats {
  totalTokens: number;
  tokensPerSecond: number;
  streamDuration: number;
  averageTokenLatency: number;
  errorCount: number;
}

export interface StreamingSession {
  id: string;
  sessionId: string;
  request: StreamingRequest;
  startTime: Date;
  status: StreamingStatus;
  stats: StreamingStats;
  controller?: AbortController;
}

export enum StreamingStatus {
  PENDING = 'pending',
  STREAMING = 'streaming',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ABORTED = 'aborted',
  ERROR = 'error',
}

export class StreamingService extends EventEmitter {
  private activeStreams: Map<string, StreamingSession> = new Map();
  private mockResponses: Map<string, string[]> = new Map();

  constructor() {
    super();
    this.initializeMockResponses();
    logger.info('StreamingService initialized');
  }

  private initializeMockResponses(): void {
    // Initialize mock responses for different scenarios
    this.mockResponses.set('greeting', [
      'Hello', '!', ' ', 'How', ' ', 'can', ' ', 'I', ' ', 'help', ' ', 'you', ' ', 'today', '?'
    ]);

    this.mockResponses.set('coding', [
      'Here', "'", 's', ' ', 'a', ' ', 'simple', ' ', 'Python', ' ', 'function', ':\n\n',
      '```python\n',
      'def', ' ', 'fibonacci', '(', 'n', '):\n',
      '    ', 'if', ' ', 'n', ' ', '<=', ' ', '1', ':\n',
      '        ', 'return', ' ', 'n', '\n',
      '    ', 'return', ' ', 'fibonacci', '(', 'n', '-', '1', ')', ' ', '+', ' ',
      'fibonacci', '(', 'n', '-', '2', ')', '\n',
      '```\n\n',
      'This', ' ', 'function', ' ', 'calculates', ' ', 'the', ' ', 'nth', ' ',
      'Fibonacci', ' ', 'number', ' ', 'recursively', '.'
    ]);

    this.mockResponses.set('explanation', [
      'Let', ' ', 'me', ' ', 'explain', ' ', 'this', ' ', 'concept', ' ', 'step', ' ', 'by', ' ', 'step', ':\n\n',
      '1', '.', ' ', 'First', ',', ' ', 'we', ' ', 'need', ' ', 'to', ' ', 'understand', ' ',
      'the', ' ', 'basic', ' ', 'principles', '.\n',
      '2', '.', ' ', 'Then', ',', ' ', 'we', ' ', 'apply', ' ', 'these', ' ', 'principles', ' ',
      'to', ' ', 'our', ' ', 'specific', ' ', 'use', ' ', 'case', '.\n',
      '3', '.', ' ', 'Finally', ',', ' ', 'we', ' ', 'validate', ' ', 'our', ' ', 'approach', ' ',
      'and', ' ', 'make', ' ', 'adjustments', ' ', 'as', ' ', 'needed', '.'
    ]);

    this.mockResponses.set('default', [
      'This', ' ', 'is', ' ', 'a', ' ', 'mock', ' ', 'streaming', ' ', 'response', '.',
      ' ', 'Each', ' ', 'token', ' ', 'is', ' ', 'generated', ' ', 'with', ' ', 'realistic', ' ',
      'timing', ' ', 'and', ' ', 'metadata', '.', ' ', 'The', ' ', 'service', ' ', 'simulates', ' ',
      'actual', ' ', 'model', ' ', 'behavior', ' ', 'for', ' ', 'testing', ' ', 'purposes', '.'
    ]);
  }

  async startStream(request: StreamingRequest): Promise<string> {
    const streamId = crypto.randomUUID();
    const controller = new AbortController();

    const session: StreamingSession = {
      id: streamId,
      sessionId: request.sessionId,
      request,
      startTime: new Date(),
      status: StreamingStatus.PENDING,
      stats: {
        totalTokens: 0,
        tokensPerSecond: 0,
        streamDuration: 0,
        averageTokenLatency: 0,
        errorCount: 0,
      },
      controller,
    };

    this.activeStreams.set(streamId, session);

    // Start streaming asynchronously
    this.processStream(session).catch(error => {
      logger.error('Error in stream processing', { error, streamId });
      session.status = StreamingStatus.ERROR;
      this.emit('stream_error', streamId, error);
    });

    applicationMetrics.recordCounter('streams_started_total', 1, {
      session_id: request.sessionId,
      model: request.model,
    });

    logger.info('Stream started', { streamId, sessionId: request.sessionId });

    return streamId;
  }

  private async processStream(session: StreamingSession): Promise<void> {
    try {
      session.status = StreamingStatus.STREAMING;

      // Emit stream start event
      this.emitStreamEvent(session, StreamingEventType.START, {
        streamId: session.id,
        sessionId: session.sessionId,
        model: session.request.model,
      });

      // Select appropriate mock response based on prompt content
      const tokens = this.selectMockResponse(session.request.prompt);
      const tokenLatencies: number[] = [];

      for (let i = 0; i < tokens.length; i++) {
        // Check if stream was aborted
        if (session.controller?.signal.aborted) {
          session.status = StreamingStatus.ABORTED;
          this.emitStreamEvent(session, StreamingEventType.ABORT, {
            reason: 'Client requested abort',
          });
          return;
        }

        const tokenStartTime = Date.now();

        // Create token with metadata
        const token: StreamingToken = {
          id: crypto.randomUUID(),
          sessionId: session.sessionId,
          token: tokens[i],
          index: i,
          timestamp: new Date(),
          metadata: session.request.streaming.includeMetadata ? {
            probability: Math.random() * 0.3 + 0.7, // Mock probability 0.7-1.0
            logprob: Math.log(Math.random() * 0.3 + 0.7),
            isComplete: i === tokens.length - 1,
            model: session.request.model,
          } : undefined,
        };

        // Emit token event
        this.emitStreamEvent(session, StreamingEventType.TOKEN, token);

        // Update session stats
        session.stats.totalTokens++;
        const tokenLatency = Date.now() - tokenStartTime;
        tokenLatencies.push(tokenLatency);
        session.stats.averageTokenLatency = 
          tokenLatencies.reduce((sum, lat) => sum + lat, 0) / tokenLatencies.length;

        // Add realistic delay between tokens
        const delay = this.calculateTokenDelay(session.request, i, tokens.length);
        await this.delay(delay);

        // Emit metadata periodically
        if (session.request.streaming.includeMetadata && i % 10 === 0) {
          this.emitStreamEvent(session, StreamingEventType.METADATA, {
            tokensGenerated: i + 1,
            estimatedCompletion: this.estimateCompletion(i, tokens.length, tokenLatencies),
            currentLatency: tokenLatency,
          });
        }
      }

      // Calculate final stats
      const endTime = Date.now();
      const duration = endTime - session.startTime.getTime();
      session.stats.streamDuration = duration;
      session.stats.tokensPerSecond = (session.stats.totalTokens / duration) * 1000;

      session.status = StreamingStatus.COMPLETED;

      // Emit completion event
      this.emitStreamEvent(session, StreamingEventType.COMPLETE, {
        stats: session.stats,
        totalTokens: session.stats.totalTokens,
        duration: session.stats.streamDuration,
        tokensPerSecond: session.stats.tokensPerSecond,
      });

      applicationMetrics.recordCounter('streams_completed_total', 1, {
        session_id: session.sessionId,
        model: session.request.model,
      });

      applicationMetrics.recordHistogram('stream_duration_seconds', duration / 1000, {
        session_id: session.sessionId,
        model: session.request.model,
      });

      logger.info('Stream completed', {
        streamId: session.id,
        sessionId: session.sessionId,
        totalTokens: session.stats.totalTokens,
        duration,
        tokensPerSecond: session.stats.tokensPerSecond,
      });

    } catch (error) {
      session.status = StreamingStatus.ERROR;
      session.stats.errorCount++;
      
      this.emitStreamEvent(session, StreamingEventType.ERROR, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });

      applicationMetrics.recordCounter('streams_error_total', 1, {
        session_id: session.sessionId,
        model: session.request.model,
        error_type: error instanceof Error ? error.constructor.name : 'UnknownError',
      });

      throw error;
    }
  }

  private selectMockResponse(prompt: string): string[] {
    const lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.includes('hello') || lowerPrompt.includes('hi')) {
      return this.mockResponses.get('greeting')!;
    } else if (lowerPrompt.includes('code') || lowerPrompt.includes('function') || lowerPrompt.includes('python')) {
      return this.mockResponses.get('coding')!;
    } else if (lowerPrompt.includes('explain') || lowerPrompt.includes('how') || lowerPrompt.includes('what')) {
      return this.mockResponses.get('explanation')!;
    }

    return this.mockResponses.get('default')!;
  }

  private calculateTokenDelay(request: StreamingRequest, tokenIndex: number, totalTokens: number): number {
    const baseDelay = request.streaming.delay;
    
    // Add some variance to make it more realistic
    const variance = Math.random() * 0.4 + 0.8; // 0.8 to 1.2 multiplier
    
    // Slightly longer delays for punctuation and end of sentences
    const currentToken = tokenIndex < totalTokens ? 'token' : '';
    const punctuationMultiplier = /[.!?;:]/.test(currentToken) ? 1.5 : 1;
    
    return Math.floor(baseDelay * variance * punctuationMultiplier);
  }

  private estimateCompletion(currentIndex: number, totalTokens: number, latencies: number[]): number {
    if (latencies.length === 0) return 0;
    
    const avgLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    const remainingTokens = totalTokens - currentIndex - 1;
    
    return remainingTokens * avgLatency;
  }

  private emitStreamEvent(session: StreamingSession, event: StreamingEventType, data: any): void {
    const streamingResponse: StreamingResponse = {
      id: crypto.randomUUID(),
      sessionId: session.sessionId,
      event,
      data,
      timestamp: new Date(),
    };

    this.emit('stream_event', session.id, streamingResponse);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async abortStream(streamId: string): Promise<boolean> {
    const session = this.activeStreams.get(streamId);
    
    if (!session) {
      return false;
    }

    if (session.controller && !session.controller.signal.aborted) {
      session.controller.abort();
      session.status = StreamingStatus.ABORTED;
      
      applicationMetrics.recordCounter('streams_aborted_total', 1, {
        session_id: session.sessionId,
        model: session.request.model,
      });

      logger.info('Stream aborted', { streamId, sessionId: session.sessionId });
      return true;
    }

    return false;
  }

  async pauseStream(streamId: string): Promise<boolean> {
    const session = this.activeStreams.get(streamId);
    
    if (!session || session.status !== StreamingStatus.STREAMING) {
      return false;
    }

    session.status = StreamingStatus.PAUSED;
    logger.info('Stream paused', { streamId, sessionId: session.sessionId });
    
    return true;
  }

  async resumeStream(streamId: string): Promise<boolean> {
    const session = this.activeStreams.get(streamId);
    
    if (!session || session.status !== StreamingStatus.PAUSED) {
      return false;
    }

    session.status = StreamingStatus.STREAMING;
    logger.info('Stream resumed', { streamId, sessionId: session.sessionId });
    
    return true;
  }

  getStreamStatus(streamId: string): StreamingSession | null {
    return this.activeStreams.get(streamId) || null;
  }

  getActiveStreams(): StreamingSession[] {
    return Array.from(this.activeStreams.values());
  }

  async getStreamingStats(): Promise<{ total: number; active: number; completed: number; errors: number }> {
    const sessions = Array.from(this.activeStreams.values());
    
    return {
      total: sessions.length,
      active: sessions.filter(s => s.status === StreamingStatus.STREAMING).length,
      completed: sessions.filter(s => s.status === StreamingStatus.COMPLETED).length,
      errors: sessions.filter(s => s.status === StreamingStatus.ERROR).length,
    };
  }

  cleanup(): void {
    // Clean up completed or errored streams
    const completedStreams: string[] = [];
    
    for (const [streamId, session] of this.activeStreams.entries()) {
      if (session.status === StreamingStatus.COMPLETED || 
          session.status === StreamingStatus.ERROR ||
          session.status === StreamingStatus.ABORTED) {
        completedStreams.push(streamId);
      }
    }

    completedStreams.forEach(streamId => {
      this.activeStreams.delete(streamId);
    });

    if (completedStreams.length > 0) {
      logger.debug('Cleaned up completed streams', { count: completedStreams.length });
    }
  }
}