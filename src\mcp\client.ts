/**
 * MCP (Model Context Protocol) Client Implementation
 * Supports OAuth 2.1/OIDC authentication with PKCE
 */

import { EventEmitter } from 'events';
import { WebSocket } from 'ws';
import { randomBytes, createHash } from 'crypto';
import { URL } from 'url';
import { logger } from '../utils/logger';
import { TraceLogger } from '../runtime/trace-logger';

// OAuth/OIDC Types
export interface OAuthConfig {
  clientId: string;
  clientSecret?: string; // Optional for public clients using PKCE
  authorizationEndpoint: string;
  tokenEndpoint: string;
  userInfoEndpoint?: string;
  scope: string[];
  redirectUri: string;
  usePKCE: boolean;
  audience?: string;
  responseType: 'code' | 'token';
  grantType: 'authorization_code' | 'client_credentials';
}

export interface TokenResponse {
  access_token: string;
  token_type: 'Bearer';
  expires_in: number;
  refresh_token?: string;
  id_token?: string;
  scope?: string;
}

export interface UserInfo {
  sub: string;
  name?: string;
  email?: string;
  picture?: string;
  [key: string]: any;
}

// MCP Protocol Types
export interface MCPMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

export interface MCPServer {
  id: string;
  name: string;
  description: string;
  version: string;
  endpoint: string;
  capabilities: ServerCapabilities;
  authentication: AuthenticationMethod;
  status: 'connected' | 'disconnected' | 'error' | 'authenticating';
  lastActivity: Date;
  metadata?: Record<string, any>;
}

export interface ServerCapabilities {
  tools?: ToolCapability[];
  resources?: ResourceCapability[];
  prompts?: PromptCapability[];
  notifications?: NotificationCapability[];
  experimental?: Record<string, any>;
}

export interface ToolCapability {
  name: string;
  description: string;
  inputSchema: any; // JSON Schema
  outputSchema?: any; // JSON Schema
  required?: boolean;
  experimental?: boolean;
}

export interface ResourceCapability {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
  annotations?: Record<string, any>;
}

export interface PromptCapability {
  name: string;
  description: string;
  arguments?: PromptArgument[];
}

export interface PromptArgument {
  name: string;
  description: string;
  required?: boolean;
  type?: string;
}

export interface NotificationCapability {
  method: string;
  description: string;
  schema?: any; // JSON Schema
}

export interface AuthenticationMethod {
  type: 'oauth2' | 'apikey' | 'none';
  config?: OAuthConfig | { headerName: string; queryParam?: string };
}

// MCP Client Configuration
export interface MCPClientConfig {
  clientId: string;
  userAgent: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableTracing: boolean;
  secureStorage?: SecureTokenStorage;
  validateCertificates: boolean;
}

export interface SecureTokenStorage {
  store(key: string, value: any): Promise<void>;
  retrieve(key: string): Promise<any>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
}

// PKCE Helper Functions
function generateCodeVerifier(): string {
  return randomBytes(32).toString('base64url');
}

function generateCodeChallenge(verifier: string): string {
  return createHash('sha256').update(verifier).digest('base64url');
}

// Simple in-memory token storage (replace with secure storage in production)
class InMemoryTokenStorage implements SecureTokenStorage {
  private storage = new Map<string, any>();

  async store(key: string, value: any): Promise<void> {
    this.storage.set(key, value);
  }

  async retrieve(key: string): Promise<any> {
    return this.storage.get(key);
  }

  async delete(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}

/**
 * MCP Client with OAuth/OIDC Support
 */
export class MCPClient extends EventEmitter {
  private config: MCPClientConfig;
  private tracer: TraceLogger;
  private tokenStorage: SecureTokenStorage;
  private servers = new Map<string, MCPServer>();
  private connections = new Map<string, WebSocket>();
  private pendingRequests = new Map<string, { resolve: Function; reject: Function; timeout: NodeJS.Timeout }>();
  private requestCounter = 0;

  constructor(config: MCPClientConfig) {
    super();
    this.config = {
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      enableTracing: true,
      validateCertificates: true,
      ...config,
    };
    
    this.tracer = new TraceLogger({
      serviceName: 'mcp-client',
      enableConsoleSpans: this.config.enableTracing,
    });
    
    this.tokenStorage = config.secureStorage || new InMemoryTokenStorage();
    
    logger.info('MCP Client initialized', {
      clientId: this.config.clientId,
      enableTracing: this.config.enableTracing,
    });
  }

  /**
   * OAuth 2.1/OIDC Authentication Flow
   */
  async authenticate(serverId: string, oauthConfig: OAuthConfig): Promise<TokenResponse> {
    const span = this.tracer.startSpan('mcp_oauth_authentication', {
      serverId,
      clientId: oauthConfig.clientId,
    });

    try {
      logger.info('Starting OAuth authentication', { serverId });

      if (oauthConfig.grantType === 'client_credentials') {
        return await this.authenticateClientCredentials(oauthConfig);
      }

      if (oauthConfig.grantType === 'authorization_code') {
        return await this.authenticateAuthorizationCode(oauthConfig);
      }

      throw new Error(`Unsupported grant type: ${oauthConfig.grantType}`);
    } catch (error) {
      span.recordException(error as Error);
      span.setStatus({ code: 2, message: (error as Error).message });
      logger.error('OAuth authentication failed', { serverId, error });
      throw error;
    } finally {
      span.end();
    }
  }

  private async authenticateClientCredentials(config: OAuthConfig): Promise<TokenResponse> {
    const body = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: config.clientId,
      scope: config.scope.join(' '),
    });

    if (config.clientSecret) {
      body.append('client_secret', config.clientSecret);
    }

    if (config.audience) {
      body.append('audience', config.audience);
    }

    const response = await fetch(config.tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': this.config.userAgent,
      },
      body: body.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token request failed: ${response.status} ${errorText}`);
    }

    const tokenResponse: TokenResponse = await response.json();
    
    // Store token securely
    const storageKey = `token_${config.clientId}`;
    await this.tokenStorage.store(storageKey, {
      ...tokenResponse,
      expires_at: Date.now() + (tokenResponse.expires_in * 1000),
    });

    logger.info('Client credentials authentication successful', {
      clientId: config.clientId,
      expiresIn: tokenResponse.expires_in,
    });

    return tokenResponse;
  }

  private async authenticateAuthorizationCode(config: OAuthConfig): Promise<TokenResponse> {
    if (!config.usePKCE) {
      throw new Error('Authorization Code flow requires PKCE for security');
    }

    // Generate PKCE parameters
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = generateCodeChallenge(codeVerifier);
    const state = randomBytes(16).toString('hex');

    // Build authorization URL
    const authUrl = new URL(config.authorizationEndpoint);
    authUrl.searchParams.set('response_type', config.responseType);
    authUrl.searchParams.set('client_id', config.clientId);
    authUrl.searchParams.set('redirect_uri', config.redirectUri);
    authUrl.searchParams.set('scope', config.scope.join(' '));
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');

    // Store PKCE parameters for later exchange
    await this.tokenStorage.store(`pkce_${state}`, {
      codeVerifier,
      redirectUri: config.redirectUri,
      tokenEndpoint: config.tokenEndpoint,
      clientId: config.clientId,
    });

    logger.info('Authorization URL generated', {
      authUrl: authUrl.toString(),
      state,
    });

    // In a real implementation, you would redirect the user to authUrl
    // and handle the callback to exchange the authorization code
    throw new Error('Authorization Code flow requires manual user interaction - redirect to: ' + authUrl.toString());
  }

  /**
   * Exchange authorization code for tokens (called from redirect handler)
   */
  async exchangeAuthorizationCode(code: string, state: string): Promise<TokenResponse> {
    const span = this.tracer.startSpan('mcp_oauth_code_exchange', { state });

    try {
      // Retrieve PKCE parameters
      const pkceData = await this.tokenStorage.retrieve(`pkce_${state}`);
      if (!pkceData) {
        throw new Error('Invalid or expired state parameter');
      }

      const body = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: pkceData.clientId,
        code,
        redirect_uri: pkceData.redirectUri,
        code_verifier: pkceData.codeVerifier,
      });

      const response = await fetch(pkceData.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': this.config.userAgent,
        },
        body: body.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
      }

      const tokenResponse: TokenResponse = await response.json();
      
      // Store token securely
      const storageKey = `token_${pkceData.clientId}`;
      await this.tokenStorage.store(storageKey, {
        ...tokenResponse,
        expires_at: Date.now() + (tokenResponse.expires_in * 1000),
      });

      // Clean up PKCE data
      await this.tokenStorage.delete(`pkce_${state}`);

      logger.info('Authorization code exchange successful', {
        clientId: pkceData.clientId,
        expiresIn: tokenResponse.expires_in,
      });

      return tokenResponse;
    } catch (error) {
      span.recordException(error as Error);
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string, config: OAuthConfig): Promise<TokenResponse> {
    const span = this.tracer.startSpan('mcp_oauth_refresh', {
      clientId: config.clientId,
    });

    try {
      const body = new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: config.clientId,
      });

      if (config.clientSecret) {
        body.append('client_secret', config.clientSecret);
      }

      const response = await fetch(config.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': this.config.userAgent,
        },
        body: body.toString(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token refresh failed: ${response.status} ${errorText}`);
      }

      const tokenResponse: TokenResponse = await response.json();
      
      // Store refreshed token
      const storageKey = `token_${config.clientId}`;
      await this.tokenStorage.store(storageKey, {
        ...tokenResponse,
        expires_at: Date.now() + (tokenResponse.expires_in * 1000),
      });

      logger.info('Token refresh successful', {
        clientId: config.clientId,
        expiresIn: tokenResponse.expires_in,
      });

      return tokenResponse;
    } catch (error) {
      span.recordException(error as Error);
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Get user information using access token
   */
  async getUserInfo(accessToken: string, userInfoEndpoint: string): Promise<UserInfo> {
    const span = this.tracer.startSpan('mcp_oauth_userinfo');

    try {
      const response = await fetch(userInfoEndpoint, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': this.config.userAgent,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`UserInfo request failed: ${response.status} ${errorText}`);
      }

      const userInfo: UserInfo = await response.json();
      
      logger.info('UserInfo retrieved successfully', {
        sub: userInfo.sub,
        email: userInfo.email,
      });

      return userInfo;
    } catch (error) {
      span.recordException(error as Error);
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Connect to MCP server
   */
  async connect(server: MCPServer): Promise<void> {
    const span = this.tracer.startSpan('mcp_server_connect', {
      serverId: server.id,
      endpoint: server.endpoint,
    });

    try {
      logger.info('Connecting to MCP server', { serverId: server.id });

      // Authenticate if required
      let accessToken: string | undefined;
      if (server.authentication.type === 'oauth2') {
        const oauthConfig = server.authentication.config as OAuthConfig;
        const tokenData = await this.tokenStorage.retrieve(`token_${oauthConfig.clientId}`);
        
        if (!tokenData || tokenData.expires_at <= Date.now()) {
          if (tokenData?.refresh_token) {
            const refreshedToken = await this.refreshToken(tokenData.refresh_token, oauthConfig);
            accessToken = refreshedToken.access_token;
          } else {
            await this.authenticate(server.id, oauthConfig);
            const newTokenData = await this.tokenStorage.retrieve(`token_${oauthConfig.clientId}`);
            accessToken = newTokenData?.access_token;
          }
        } else {
          accessToken = tokenData.access_token;
        }
      }

      // Establish WebSocket connection
      const wsUrl = new URL(server.endpoint);
      const headers: Record<string, string> = {
        'User-Agent': this.config.userAgent,
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      } else if (server.authentication.type === 'apikey') {
        const apiKeyConfig = server.authentication.config as { headerName: string; queryParam?: string };
        // API key handling would be implemented here
      }

      const ws = new WebSocket(wsUrl.toString(), {
        headers,
        timeout: this.config.timeout,
        rejectUnauthorized: this.config.validateCertificates,
      });

      // Setup WebSocket handlers
      ws.on('open', () => {
        logger.info('WebSocket connection established', { serverId: server.id });
        server.status = 'connected';
        server.lastActivity = new Date();
        this.emit('server:connected', server.id);
      });

      ws.on('message', (data) => {
        this.handleMessage(server.id, data);
      });

      ws.on('error', (error) => {
        logger.error('WebSocket error', { serverId: server.id, error });
        server.status = 'error';
        this.emit('server:error', server.id, error);
      });

      ws.on('close', (code, reason) => {
        logger.info('WebSocket connection closed', { 
          serverId: server.id, 
          code, 
          reason: reason.toString() 
        });
        server.status = 'disconnected';
        this.connections.delete(server.id);
        this.emit('server:disconnected', server.id, code, reason.toString());
      });

      this.connections.set(server.id, ws);
      this.servers.set(server.id, server);

      // Send initial handshake
      await this.sendHandshake(server.id);

    } catch (error) {
      span.recordException(error as Error);
      server.status = 'error';
      logger.error('Failed to connect to MCP server', { serverId: server.id, error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Send MCP handshake
   */
  private async sendHandshake(serverId: string): Promise<void> {
    const handshakeMessage: MCPMessage = {
      jsonrpc: '2.0',
      id: this.generateRequestId(),
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: { listChanged: true },
          resources: { listChanged: true, subscribe: true },
          prompts: { listChanged: true },
        },
        clientInfo: {
          name: 'custom-agent-system',
          version: '2.0.0',
        },
      },
    };

    await this.sendMessage(serverId, handshakeMessage);
  }

  /**
   * Send message to MCP server
   */
  async sendMessage(serverId: string, message: MCPMessage): Promise<any> {
    const ws = this.connections.get(serverId);
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      throw new Error(`Server ${serverId} is not connected`);
    }

    const span = this.tracer.startSpan('mcp_send_message', {
      serverId,
      method: message.method,
      messageId: message.id,
    });

    try {
      return new Promise((resolve, reject) => {
        if (message.id) {
          // Request with expected response
          const timeout = setTimeout(() => {
            this.pendingRequests.delete(message.id as string);
            reject(new Error(`Request timeout for message ${message.id}`));
          }, this.config.timeout);

          this.pendingRequests.set(message.id as string, {
            resolve,
            reject,
            timeout,
          });
        }

        ws.send(JSON.stringify(message), (error) => {
          if (error) {
            if (message.id) {
              const pending = this.pendingRequests.get(message.id as string);
              if (pending) {
                clearTimeout(pending.timeout);
                this.pendingRequests.delete(message.id as string);
                pending.reject(error);
              }
            }
            span.recordException(error);
            reject(error);
          } else if (!message.id) {
            // Notification (no response expected)
            resolve(undefined);
          }
        });

        logger.debug('MCP message sent', {
          serverId,
          method: message.method,
          messageId: message.id,
        });
      });
    } catch (error) {
      span.recordException(error as Error);
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Handle incoming message from MCP server
   */
  private handleMessage(serverId: string, data: Buffer): void {
    try {
      const message: MCPMessage = JSON.parse(data.toString());
      
      logger.debug('MCP message received', {
        serverId,
        method: message.method,
        messageId: message.id,
        hasError: !!message.error,
      });

      const server = this.servers.get(serverId);
      if (server) {
        server.lastActivity = new Date();
      }

      // Handle response to pending request
      if (message.id && this.pendingRequests.has(message.id as string)) {
        const pending = this.pendingRequests.get(message.id as string)!;
        clearTimeout(pending.timeout);
        this.pendingRequests.delete(message.id as string);

        if (message.error) {
          pending.reject(new Error(`MCP Error: ${message.error.message}`));
        } else {
          pending.resolve(message.result);
        }
        return;
      }

      // Handle notifications and method calls
      if (message.method) {
        this.emit('message', serverId, message);
        this.emit(`method:${message.method}`, serverId, message);
      }

    } catch (error) {
      logger.error('Failed to parse MCP message', { serverId, error });
      this.emit('error', serverId, error);
    }
  }

  /**
   * Disconnect from MCP server
   */
  async disconnect(serverId: string): Promise<void> {
    const ws = this.connections.get(serverId);
    if (ws) {
      ws.close(1000, 'Client disconnect');
      this.connections.delete(serverId);
    }

    const server = this.servers.get(serverId);
    if (server) {
      server.status = 'disconnected';
    }

    logger.info('Disconnected from MCP server', { serverId });
  }

  /**
   * Disconnect from all servers
   */
  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.servers.keys()).map(serverId =>
      this.disconnect(serverId)
    );
    await Promise.all(disconnectPromises);
  }

  /**
   * Get server information
   */
  getServer(serverId: string): MCPServer | undefined {
    return this.servers.get(serverId);
  }

  /**
   * List all servers
   */
  listServers(): MCPServer[] {
    return Array.from(this.servers.values());
  }

  /**
   * Check if server is connected
   */
  isConnected(serverId: string): boolean {
    const server = this.servers.get(serverId);
    return server?.status === 'connected' || false;
  }

  private generateRequestId(): string {
    return `req_${++this.requestCounter}_${Date.now()}`;
  }

  /**
   * Cleanup resources
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down MCP client');
    
    await this.disconnectAll();
    
    // Clear pending requests
    for (const [id, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Client shutdown'));
    }
    this.pendingRequests.clear();

    await this.tokenStorage.clear();
    this.removeAllListeners();
  }
}