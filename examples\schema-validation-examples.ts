import { advancedContractValidator } from '@/integration/contract-validator';
import { logger } from '@/utils/logger';

// Example MicroNote data
const exampleMicroNote = {
  id: '550e8400-e29b-41d4-a716-************',
  version: '1.0.0',
  content: {
    text: 'This is a sample micro-note with important information.',
    format: 'markdown',
    language: 'en',
    attachments: []
  },
  metadata: {
    title: 'Sample Note',
    tags: ['important', 'sample'],
    priority: 'high',
    category: 'idea',
    context: {
      project_id: '123e4567-e89b-12d3-a456-426614174000',
      agent_id: '987fcdeb-51a2-43d2-b123-456789abcdef'
    }
  },
  timestamps: {
    created: '2024-01-15T10:30:00Z',
    updated: '2024-01-15T10:30:00Z'
  },
  privacy: {
    classification: 'internal',
    encryption: {
      enabled: true,
      algorithm: 'aes-256-gcm'
    }
  },
  sync: {
    vector_clock: {
      'node1': 1,
      'node2': 0
    },
    hash: 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'
  }
};

// Example UIElement data
const exampleUIElement = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  version: '1.0.0',
  type: 'button',
  properties: {
    name: 'submit-button',
    label: 'Submit Form',
    value: null,
    validation: {
      required: false
    }
  },
  layout: {
    size: {
      width: '120px',
      height: '40px'
    },
    margin: {
      top: 10,
      right: 10,
      bottom: 10,
      left: 10
    }
  },
  styling: {
    theme: 'light',
    variant: 'primary',
    size: 'md'
  },
  behavior: {
    events: {
      onClick: {
        action: 'submit',
        target: 'form-1'
      }
    },
    state: {
      disabled: false,
      loading: false
    }
  }
};

// Example CodePatch data
const exampleCodePatch = {
  id: '456e7890-e12b-34c5-d678-901234567890',
  version: '1.0.0',
  patch_type: 'unified',
  operation: 'update',
  source: {
    file_path: 'src/components/Button.tsx',
    language: 'typescript',
    checksum: 'b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9'
  },
  target: {
    file_path: 'src/components/Button.tsx',
    checksum: 'c7a5fdbd8edfc29b2647d8daf6b194f7b6e8c5234e7b9c7e5f3a2b1d0c9e8f7a'
  },
  diff: {
    format: 'unified',
    content: '@@ -1,4 +1,5 @@\n import React from \'react\';\n+import { ButtonProps } from \'./types\';\n \n-export const Button = ({ children, onClick }) => {\n+export const Button: React.FC<ButtonProps> = ({ children, onClick }) => {\n   return <button onClick={onClick}>{children}</button>;',
    context_lines: 3,
    statistics: {
      lines_added: 2,
      lines_removed: 1,
      lines_modified: 1,
      total_changes: 4
    }
  },
  metadata: {
    title: 'Add TypeScript types to Button component',
    description: 'Enhance Button component with proper TypeScript types',
    category: 'refactor',
    priority: 'normal'
  },
  timestamps: {
    created: '2024-01-15T14:20:00Z',
    updated: '2024-01-15T14:20:00Z'
  }
};

// Example Tool I/O data
const exampleToolIO = {
  id: '789e0123-e45f-67g8-h901-234567890123',
  version: '1.0.0',
  tool_name: 'file_reader',
  mcp_version: '2.0',
  input_schema: {
    schema: {
      type: 'object',
      properties: {
        file_path: {
          type: 'string',
          description: 'Path to the file to read'
        },
        encoding: {
          type: 'string',
          enum: ['utf-8', 'ascii', 'binary'],
          default: 'utf-8'
        }
      },
      required: ['file_path']
    },
    format: 'json',
    max_size: 1048576
  },
  output_schema: {
    schema: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: 'File content'
        },
        size: {
          type: 'integer',
          description: 'File size in bytes'
        },
        encoding: {
          type: 'string',
          description: 'Detected encoding'
        }
      },
      required: ['content', 'size']
    },
    format: 'json'
  },
  error_schema: {
    schema: {
      type: 'object',
      properties: {
        error_code: {
          type: 'string',
          enum: ['FILE_NOT_FOUND', 'PERMISSION_DENIED', 'INVALID_ENCODING']
        },
        message: {
          type: 'string'
        }
      },
      required: ['error_code', 'message']
    },
    error_codes: [
      {
        code: 'FILE_NOT_FOUND',
        message: 'The specified file does not exist',
        http_status: 404,
        recoverable: false
      }
    ]
  },
  capabilities: {
    async_execution: false,
    cancellation: false,
    progress_reporting: false,
    caching: {
      supported: true,
      ttl_seconds: 300
    }
  },
  security: {
    authentication: {
      required: true,
      methods: ['bearer']
    },
    data_classification: 'internal',
    audit_logging: true
  },
  metadata: {
    title: 'File Reader Tool',
    description: 'Reads file content from the filesystem',
    category: 'file_system',
    tags: ['file', 'read', 'filesystem']
  },
  timestamps: {
    created: '2024-01-15T16:45:00Z',
    updated: '2024-01-15T16:45:00Z'
  }
};

// Example usage function
export async function demonstrateSchemaValidation(): Promise<void> {
  logger.info('Starting schema validation demonstrations...');

  // Validate MicroNote
  console.log('\n=== MicroNote Validation ===');
  const microNoteResult = advancedContractValidator.validateMicroNote(exampleMicroNote, {
    securityLevel: 'high',
    strict: true
  });
  
  console.log('Valid:', microNoteResult.valid);
  if (microNoteResult.errors) {
    console.log('Errors:', microNoteResult.errors);
  }
  if (microNoteResult.securityIssues) {
    console.log('Security Issues:', microNoteResult.securityIssues);
  }
  console.log('Performance:', microNoteResult.performanceMetrics);

  // Validate UIElement
  console.log('\n=== UIElement Validation ===');
  const uiElementResult = advancedContractValidator.validateUIElement(exampleUIElement, {
    securityLevel: 'medium'
  });
  
  console.log('Valid:', uiElementResult.valid);
  if (uiElementResult.suggestions) {
    console.log('Suggestions:', uiElementResult.suggestions);
  }

  // Validate CodePatch
  console.log('\n=== CodePatch Validation ===');
  const codePatchResult = advancedContractValidator.validateCodePatch(exampleCodePatch, {
    securityLevel: 'critical',
    strict: true
  });
  
  console.log('Valid:', codePatchResult.valid);
  console.log('Version:', codePatchResult.version);
  console.log('Checksum:', codePatchResult.schemaChecksum);

  // Validate Tool I/O
  console.log('\n=== Tool I/O Validation ===');
  const toolIOResult = advancedContractValidator.validateToolIO(exampleToolIO, {
    securityLevel: 'high'
  });
  
  console.log('Valid:', toolIOResult.valid);
  if (toolIOResult.errors) {
    console.log('Errors:', toolIOResult.errors);
  }

  // Demonstrate version management
  console.log('\n=== Version Management ===');
  const schemas = advancedContractValidator.listSchemas();
  console.log('Available schemas:', schemas);

  schemas.forEach(schemaId => {
    const versions = advancedContractValidator.getSchemaVersions(schemaId);
    console.log(`${schemaId} versions:`, versions.map(v => v.version));
  });

  // Test invalid data
  console.log('\n=== Invalid Data Testing ===');
  const invalidMicroNote = {
    id: 'invalid-uuid',
    // Missing required fields
  };

  const invalidResult = advancedContractValidator.validateMicroNote(invalidMicroNote, {
    securityLevel: 'low'
  });
  
  console.log('Invalid data validation result:', {
    valid: invalidResult.valid,
    errors: invalidResult.errors
  });

  // Test security validation
  console.log('\n=== Security Validation Testing ===');
  const maliciousData = {
    id: '550e8400-e29b-41d4-a716-************',
    version: '1.0.0',
    content: {
      text: 'Normal text with <script>alert("XSS")</script> injection',
      format: 'markdown'
    },
    metadata: {
      title: 'Test with SQL: SELECT * FROM users; DROP TABLE users;--'
    },
    timestamps: {
      created: '2024-01-15T10:30:00Z',
      updated: '2024-01-15T10:30:00Z'
    },
    privacy: {
      classification: 'internal'
    },
    sync: {
      vector_clock: {},
      hash: 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'
    }
  };

  const securityResult = advancedContractValidator.validateMicroNote(maliciousData, {
    securityLevel: 'critical'
  });

  console.log('Security validation result:');
  console.log('Valid:', securityResult.valid);
  console.log('Security Issues:', securityResult.securityIssues);

  logger.info('Schema validation demonstrations completed');
}

// Export examples for testing
export {
  exampleMicroNote,
  exampleUIElement,
  exampleCodePatch,
  exampleToolIO
};