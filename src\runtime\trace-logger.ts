import { FastifyRequest, FastifyReply } from 'fastify';
import { trace, context, SpanStatusCode, SpanKind } from '@opentelemetry/api';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  sessionId?: string;
  userId?: string;
  requestId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

export interface RequestTrace {
  context: TraceContext;
  request: {
    method: string;
    url: string;
    headers: Record<string, any>;
    query: Record<string, any>;
    body?: any;
    ip: string;
    userAgent?: string;
  };
  response?: {
    statusCode: number;
    headers: Record<string, any>;
    size?: number;
  };
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  metadata?: Record<string, any>;
}

export interface ModelExecutionTrace {
  context: TraceContext;
  model: {
    provider: string;
    name: string;
    version?: string;
  };
  request: {
    prompt: string;
    parameters: Record<string, any>;
    maxTokens: number;
    temperature: number;
  };
  response?: {
    content: string;
    tokensUsed: number;
    finishReason: string;
    latency: number;
  };
  streaming?: {
    enabled: boolean;
    tokensStreamed: number;
    chunks: number;
    streamDuration: number;
  };
  costs?: {
    inputTokens: number;
    outputTokens: number;
    totalCost: number;
    currency: string;
  };
  error?: {
    name: string;
    message: string;
    code?: string;
    retryCount?: number;
  };
}

export interface PolicyEnforcementTrace {
  context: TraceContext;
  policy: {
    name: string;
    type: string;
    version: string;
  };
  evaluation: {
    decision: 'allow' | 'deny' | 'warn';
    reason: string;
    conditions: Array<{
      name: string;
      result: boolean;
      value?: any;
    }>;
    executionTime: number;
  };
  request: {
    path: string;
    method: string;
    sessionId?: string;
    userId?: string;
  };
}

export interface DatabaseTrace {
  context: TraceContext;
  operation: {
    type: 'query' | 'insert' | 'update' | 'delete' | 'transaction';
    table?: string;
    database: string;
  };
  query: {
    sql?: string;
    parameters?: any[];
    rowsAffected?: number;
    executionTime: number;
  };
  error?: {
    name: string;
    message: string;
    code?: string;
  };
}

export enum TraceLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

export interface TracingConfig {
  enabled: boolean;
  level: TraceLevel;
  sampleRate: number; // 0.0 to 1.0
  includeRequestBody: boolean;
  includeResponseBody: boolean;
  maxBodySize: number;
  sensitiveHeaders: string[];
  sanitizeData: boolean;
}

export class TraceLogger {
  private tracer = trace.getTracer('custom-agent-system', '1.0.0');
  private config: TracingConfig;
  private activeTraces: Map<string, TraceContext> = new Map();

  constructor(config?: Partial<TracingConfig>) {
    this.config = {
      enabled: true,
      level: TraceLevel.INFO,
      sampleRate: 1.0,
      includeRequestBody: false,
      includeResponseBody: false,
      maxBodySize: 1024 * 10, // 10KB
      sensitiveHeaders: ['authorization', 'cookie', 'x-api-key', 'x-auth-token'],
      sanitizeData: true,
      ...config,
    };

    logger.info('TraceLogger initialized', { config: this.config });
  }

  logRequest(request: FastifyRequest): TraceContext {
    if (!this.config.enabled || !this.shouldSample()) {
      return this.createMinimalContext(request.id);
    }

    const startTime = Date.now();
    const span = this.tracer.startSpan(`HTTP ${request.method} ${request.routerPath || request.url}`, {
      kind: SpanKind.SERVER,
      attributes: {
        'http.method': request.method,
        'http.url': request.url,
        'http.route': request.routerPath || request.url,
        'http.user_agent': request.headers['user-agent'] || '',
        'http.remote_addr': request.ip,
        'request.id': request.id,
      },
    });

    const traceContext: TraceContext = {
      traceId: span.spanContext().traceId,
      spanId: span.spanContext().spanId,
      requestId: request.id,
      startTime,
    };

    // Extract session and user information
    const sessionId = this.extractSessionId(request);
    const userId = this.extractUserId(request);

    if (sessionId) {
      traceContext.sessionId = sessionId;
      span.setAttributes({ 'session.id': sessionId });
    }

    if (userId) {
      traceContext.userId = userId;
      span.setAttributes({ 'user.id': userId });
    }

    const requestTrace: RequestTrace = {
      context: traceContext,
      request: {
        method: request.method,
        url: request.url,
        headers: this.sanitizeHeaders(request.headers),
        query: request.query as Record<string, any>,
        body: this.shouldIncludeBody(request) ? this.sanitizeBody(request.body) : undefined,
        ip: request.ip,
        userAgent: request.headers['user-agent'],
      },
    };

    this.activeTraces.set(request.id, traceContext);

    // Store span in context for child spans
    context.with(trace.setSpan(context.active(), span), () => {
      this.logTrace('request_started', requestTrace);
    });

    span.addEvent('request_started', {
      timestamp: startTime,
    });

    return traceContext;
  }

  logResponse(request: FastifyRequest, reply: FastifyReply, duration: number): void {
    if (!this.config.enabled) {
      return;
    }

    const traceContext = this.activeTraces.get(request.id);
    if (!traceContext) {
      return;
    }

    const endTime = Date.now();
    traceContext.endTime = endTime;
    traceContext.duration = duration;

    const requestTrace: RequestTrace = {
      context: traceContext,
      request: {
        method: request.method,
        url: request.url,
        headers: this.sanitizeHeaders(request.headers),
        query: request.query as Record<string, any>,
        ip: request.ip,
        userAgent: request.headers['user-agent'],
      },
      response: {
        statusCode: reply.statusCode,
        headers: this.sanitizeHeaders(reply.getHeaders()),
        size: reply.raw.getHeader('content-length') ? 
              parseInt(reply.raw.getHeader('content-length') as string, 10) : undefined,
      },
    };

    // Get the span from context
    const span = trace.getActiveSpan();
    if (span) {
      span.setAttributes({
        'http.status_code': reply.statusCode,
        'http.response.size': requestTrace.response?.size || 0,
        'request.duration_ms': duration,
      });

      span.addEvent('request_completed', {
        timestamp: endTime,
        attributes: {
          'response.status_code': reply.statusCode,
          'response.duration_ms': duration,
        },
      });

      // Set span status based on response code
      if (reply.statusCode >= 400) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: `HTTP ${reply.statusCode}`,
        });
      } else {
        span.setStatus({ code: SpanStatusCode.OK });
      }

      span.end();
    }

    this.logTrace('request_completed', requestTrace);
    this.activeTraces.delete(request.id);

    // Record metrics
    applicationMetrics.recordHistogram('request_trace_duration_ms', duration, {
      method: request.method,
      status_code: reply.statusCode.toString(),
    });
  }

  logModelExecution(trace: ModelExecutionTrace): void {
    if (!this.config.enabled) {
      return;
    }

    const span = this.tracer.startSpan('model_execution', {
      kind: SpanKind.CLIENT,
      attributes: {
        'model.provider': trace.model.provider,
        'model.name': trace.model.name,
        'model.version': trace.model.version || 'unknown',
        'request.max_tokens': trace.request.maxTokens,
        'request.temperature': trace.request.temperature,
        'session.id': trace.context.sessionId || '',
        'user.id': trace.context.userId || '',
      },
    });

    if (trace.response) {
      span.setAttributes({
        'response.tokens_used': trace.response.tokensUsed,
        'response.finish_reason': trace.response.finishReason,
        'response.latency_ms': trace.response.latency,
      });
    }

    if (trace.streaming) {
      span.setAttributes({
        'streaming.enabled': trace.streaming.enabled,
        'streaming.tokens_streamed': trace.streaming.tokensStreamed,
        'streaming.chunks': trace.streaming.chunks,
        'streaming.duration_ms': trace.streaming.streamDuration,
      });
    }

    if (trace.costs) {
      span.setAttributes({
        'cost.input_tokens': trace.costs.inputTokens,
        'cost.output_tokens': trace.costs.outputTokens,
        'cost.total': trace.costs.totalCost,
        'cost.currency': trace.costs.currency,
      });
    }

    if (trace.error) {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: trace.error.message,
      });
      span.recordException(new Error(trace.error.message));
    } else {
      span.setStatus({ code: SpanStatusCode.OK });
    }

    span.end();

    this.logTrace('model_execution', trace);

    // Record metrics
    if (trace.response) {
      applicationMetrics.recordHistogram('model_execution_latency_ms', trace.response.latency, {
        provider: trace.model.provider,
        model: trace.model.name,
      });

      applicationMetrics.recordHistogram('model_tokens_used', trace.response.tokensUsed, {
        provider: trace.model.provider,
        model: trace.model.name,
      });
    }
  }

  logPolicyEnforcement(trace: PolicyEnforcementTrace): void {
    if (!this.config.enabled) {
      return;
    }

    const span = this.tracer.startSpan('policy_enforcement', {
      kind: SpanKind.INTERNAL,
      attributes: {
        'policy.name': trace.policy.name,
        'policy.type': trace.policy.type,
        'policy.version': trace.policy.version,
        'evaluation.decision': trace.evaluation.decision,
        'evaluation.reason': trace.evaluation.reason,
        'evaluation.execution_time_ms': trace.evaluation.executionTime,
        'request.path': trace.request.path,
        'request.method': trace.request.method,
        'session.id': trace.request.sessionId || '',
        'user.id': trace.request.userId || '',
      },
    });

    // Add conditions as events
    trace.evaluation.conditions.forEach((condition, index) => {
      span.addEvent(`condition_${index}`, {
        attributes: {
          'condition.name': condition.name,
          'condition.result': condition.result,
          'condition.value': condition.value?.toString() || '',
        },
      });
    });

    if (trace.evaluation.decision === 'deny') {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: `Policy violation: ${trace.evaluation.reason}`,
      });
    } else {
      span.setStatus({ code: SpanStatusCode.OK });
    }

    span.end();

    this.logTrace('policy_enforcement', trace);

    // Record metrics
    applicationMetrics.recordCounter('policy_evaluations_total', 1, {
      policy_name: trace.policy.name,
      decision: trace.evaluation.decision,
    });

    applicationMetrics.recordHistogram('policy_evaluation_duration_ms', trace.evaluation.executionTime, {
      policy_name: trace.policy.name,
    });
  }

  logDatabaseOperation(trace: DatabaseTrace): void {
    if (!this.config.enabled) {
      return;
    }

    const span = this.tracer.startSpan('database_operation', {
      kind: SpanKind.CLIENT,
      attributes: {
        'db.operation': trace.operation.type,
        'db.name': trace.operation.database,
        'db.table': trace.operation.table || '',
        'db.statement': trace.query.sql || '',
        'db.rows_affected': trace.query.rowsAffected || 0,
        'db.execution_time_ms': trace.query.executionTime,
      },
    });

    if (trace.error) {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: trace.error.message,
      });
      span.recordException(new Error(trace.error.message));
    } else {
      span.setStatus({ code: SpanStatusCode.OK });
    }

    span.end();

    this.logTrace('database_operation', trace);

    // Record metrics
    applicationMetrics.recordHistogram('database_operation_duration_ms', trace.query.executionTime, {
      operation: trace.operation.type,
      database: trace.operation.database,
    });
  }

  logError(error: Error, context?: Partial<TraceContext>): void {
    if (!this.config.enabled) {
      return;
    }

    const span = this.tracer.startSpan('error', {
      kind: SpanKind.INTERNAL,
      attributes: {
        'error.name': error.name,
        'error.message': error.message,
        'session.id': context?.sessionId || '',
        'user.id': context?.userId || '',
        'request.id': context?.requestId || '',
      },
    });

    span.setStatus({
      code: SpanStatusCode.ERROR,
      message: error.message,
    });

    span.recordException(error);
    span.end();

    this.logTrace('error', {
      context: context || this.createMinimalContext(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    });

    applicationMetrics.recordCounter('errors_total', 1, {
      error_type: error.name,
    });
  }

  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  private shouldIncludeBody(request: FastifyRequest): boolean {
    if (!this.config.includeRequestBody) {
      return false;
    }

    const contentLength = request.headers['content-length'];
    if (contentLength && parseInt(contentLength, 10) > this.config.maxBodySize) {
      return false;
    }

    return true;
  }

  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    if (!this.config.sanitizeData) {
      return headers;
    }

    const sanitized = { ...headers };
    
    this.config.sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    });

    return sanitized;
  }

  private sanitizeBody(body: any): any {
    if (!this.config.sanitizeData || !body) {
      return body;
    }

    // Basic sanitization - remove common sensitive fields
    const sensitiveFields = ['password', 'token', 'key', 'secret', 'credential'];
    const sanitized = JSON.parse(JSON.stringify(body));

    const sanitizeObject = (obj: any): void => {
      if (typeof obj === 'object' && obj !== null) {
        Object.keys(obj).forEach(key => {
          if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
            obj[key] = '[REDACTED]';
          } else if (typeof obj[key] === 'object') {
            sanitizeObject(obj[key]);
          }
        });
      }
    };

    sanitizeObject(sanitized);
    return sanitized;
  }

  private extractSessionId(request: FastifyRequest): string | undefined {
    return request.headers['x-session-id'] as string ||
           (request.query as any)?.sessionId ||
           (request.body as any)?.sessionId;
  }

  private extractUserId(request: FastifyRequest): string | undefined {
    return request.headers['x-user-id'] as string ||
           (request.query as any)?.userId ||
           (request.body as any)?.userId;
  }

  private createMinimalContext(requestId?: string): TraceContext {
    return {
      traceId: crypto.randomUUID(),
      spanId: crypto.randomUUID(),
      requestId: requestId || crypto.randomUUID(),
      startTime: Date.now(),
    };
  }

  private logTrace(event: string, data: any): void {
    const logLevel = this.config.level;
    
    switch (logLevel) {
      case TraceLevel.DEBUG:
        logger.debug(`Trace: ${event}`, data);
        break;
      case TraceLevel.INFO:
        logger.info(`Trace: ${event}`, { context: data.context });
        break;
      case TraceLevel.WARN:
        logger.warn(`Trace: ${event}`, { context: data.context });
        break;
      case TraceLevel.ERROR:
        if (data.error) {
          logger.error(`Trace: ${event}`, data);
        }
        break;
    }
  }

  getActiveTraces(): TraceContext[] {
    return Array.from(this.activeTraces.values());
  }

  updateConfig(newConfig: Partial<TracingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('TraceLogger config updated', { config: this.config });
  }
}