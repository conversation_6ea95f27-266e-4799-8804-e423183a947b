export { ContractValidator, contractValidator } from './validator';
export { AdvancedContractValidator, advancedContractValidator } from './advanced-validator';
export { SemanticVersionManager, SchemaVersionRegistry, schemaVersionRegistry } from './semantic-versioning';
export type { 
  ValidationResult, 
  SchemaRegistry,
  AdvancedValidationResult,
  ValidationOptions,
  SecurityIssue,
  PerformanceMetrics,
  SemanticVersion,
  VersionCompatibility,
  SchemaVersionInfo
} from './validator';