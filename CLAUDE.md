# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Custom Agent System v2.0 - a model-agnostic, MCP-first, local-first modular agent platform designed with 2025 industry best practices. The project is currently in planning phase as documented in the PID.md file.

## Development Commands

Based on the VS Code workspace configuration, the following commands are available:

### Python Environment Setup
```bash
# Setup virtual environment
python -m venv venv

# Install dependencies  
./venv/Scripts/python.exe -m pip install -r requirements.txt
```

### Code Quality
```bash
# Format code
./venv/Scripts/python.exe -m black .

# Lint code
./venv/Scripts/python.exe -m flake8 .
```

### Testing
```bash
# Run all tests
./venv/Scripts/python.exe -m pytest tests/ -v

# Run tests with specific options
./venv/Scripts/python.exe -m pytest tests/ -v --tb=short
```

### Application
```bash
# Start the agent system
./venv/Scripts/python.exe main.py

# Start with specific agent debugging
./venv/Scripts/python.exe main.py --agent <agent_name> --debug
```

### Documentation
```bash
# Generate documentation
./venv/Scripts/python.exe -m pydoc -w .
```

### Cleanup
```powershell
# Clean build artifacts
Remove-Item -Recurse -Force -ErrorAction SilentlyContinue __pycache__, *.pyc, *.pyo, .pytest_cache, .coverage, htmlcov, dist, build, *.egg-info
```

## Architecture Overview

The system implements a multi-layered architecture as outlined in the PID:

1. **User Interface Layer**: Diff Approval Interface, Capability Explorer, Retrieval Tuner, Micro-Notes Inbox
2. **Agent Orchestration Layer**: Multi-Agent Coordinator, Reasoning Engine, Ethics Monitor, Explanation Generator  
3. **Capability Integration**: Enhanced MCP Client, Contract Validator, Security Gateway, Observability Hub
4. **Data & Storage Layer**: CRDT Sync Engine, Vector Store (Chroma+), Analytics (DuckDB), Audit Log Store

## Key Design Principles

- **AI-Native Design**: Modular agent architecture with explainable AI and multi-modal capabilities
- **Contract-First Development**: OpenAPI 3.1 specifications with comprehensive schema validation
- **Local-First Excellence**: Offline-capable with user-controlled data and seamless sync
- **Zero-Trust Security**: Identity verification, micro-segmentation, and continuous monitoring
- **Cloud-Native Operations**: Kubernetes-native with GitOps deployment

## Technology Stack

- **Runtime**: Node.js 20 LTS with TypeScript 5.2+
- **Backend Framework**: Fastify 4.x with OpenAPI integration
- **Database**: PostgreSQL 16 with CRDT extensions
- **Vector Store**: Chroma 0.4+ with OpenAI embeddings
- **Analytics**: DuckDB 0.9+ with Parquet support
- **Frontend**: React 18 with Concurrent Features
- **Testing**: Vitest with Testing Library

## Development Guidelines

- Code style: 4 spaces for indentation, 120 character line limit
- Python formatting: Black formatter with flake8 linting
- Testing: pytest with 90%+ unit test coverage target
- Documentation: Auto-generated with pydoc
- Environment: Python virtual environment in ./venv/

## Project Status

Currently in planning phase. The actual implementation will follow the detailed roadmap outlined in PID.md with phases covering:
- Phase 0: Foundation & Contracts 
- Phase 1: Enhanced MCP Integration
- Phase 2: Local-First Data Architecture
- Phase 3: Security & Compliance Excellence
- Phase 4: Advanced Observability
- Phase 5: Multi-Agent Orchestration