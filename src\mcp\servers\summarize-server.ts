/**
 * Summarize MCP Server Example
 * Provides text summarization capabilities with multiple strategies and models
 */

import { EventEmitter } from 'events';
import { WebSocketServer, WebSocket } from 'ws';
import { createHash } from 'crypto';
import { logger } from '../../utils/logger';
import { TraceLogger } from '../../runtime/trace-logger';

// MCP Types
export interface MCPMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

// Summarization Types
export interface SummarizeRequest {
  text: string;
  strategy?: 'extractive' | 'abstractive' | 'hybrid';
  model?: string;
  max_length?: number;
  min_length?: number;
  temperature?: number;
  focus?: 'general' | 'key_points' | 'action_items' | 'technical' | 'narrative';
  format?: 'paragraph' | 'bullet_points' | 'structured';
  language?: string;
}

export interface SummarizeResponse {
  summary: string;
  strategy: string;
  model: string;
  metadata: SummaryMetadata;
  execution_time: number;
}

export interface SummaryMetadata {
  original_length: number;
  summary_length: number;
  compression_ratio: number;
  key_phrases: string[];
  confidence_score: number;
  language_detected?: string;
}

export interface BatchSummarizeRequest {
  documents: DocumentToSummarize[];
  strategy?: string;
  model?: string;
  max_length?: number;
  preserve_order?: boolean;
}

export interface DocumentToSummarize {
  id: string;
  text: string;
  title?: string;
  metadata?: any;
}

export interface BatchSummarizeResponse {
  summaries: DocumentSummary[];
  strategy: string;
  model: string;
  execution_time: number;
  total_documents: number;
}

export interface DocumentSummary {
  id: string;
  summary: string;
  title?: string;
  metadata: SummaryMetadata;
}

export interface AnalyzeTextRequest {
  text: string;
  analysis_types?: ('sentiment' | 'topics' | 'entities' | 'readability' | 'structure')[];
}

export interface AnalyzeTextResponse {
  analysis: TextAnalysis;
  execution_time: number;
}

export interface TextAnalysis {
  sentiment?: {
    polarity: number; // -1 to 1
    subjectivity: number; // 0 to 1
    label: 'positive' | 'negative' | 'neutral';
  };
  topics?: {
    topic: string;
    confidence: number;
  }[];
  entities?: {
    text: string;
    type: string;
    confidence: number;
  }[];
  readability?: {
    flesch_reading_ease: number;
    flesch_kincaid_grade: number;
    automated_readability_index: number;
    reading_level: string;
  };
  structure?: {
    sentences: number;
    paragraphs: number;
    words: number;
    characters: number;
    avg_sentence_length: number;
    avg_word_length: number;
  };
}

// Server Configuration
export interface SummarizeServerConfig {
  port: number;
  host: string;
  models: SummarizationModel[];
  maxTextLength: number;
  enableCaching: boolean;
  cacheSize: number;
  enableTracing: boolean;
  rateLimits: {
    requestsPerMinute: number;
    charactersPerMinute: number;
  };
}

export interface SummarizationModel {
  id: string;
  name: string;
  description: string;
  strategies: string[];
  maxInputLength: number;
  maxOutputLength: number;
  costPerCharacter: number;
  provider: 'openai' | 'local' | 'huggingface' | 'mock';
  config?: any;
}

// Mock Summarization Engine
class MockSummarizationEngine {
  private models: Map<string, SummarizationModel> = new Map();

  constructor(models: SummarizationModel[]) {
    for (const model of models) {
      this.models.set(model.id, model);
    }
  }

  async summarizeText(
    text: string,
    strategy: string,
    modelId: string,
    options: Partial<SummarizeRequest>
  ): Promise<{ summary: string; metadata: SummaryMetadata }> {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }

    if (text.length > model.maxInputLength) {
      throw new Error(`Text too long: ${text.length} characters exceeds limit of ${model.maxInputLength}`);
    }

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, Math.min(text.length / 100, 2000)));

    let summary: string;
    const sentences = this.extractSentences(text);
    const keyPhrases = this.extractKeyPhrases(text);

    switch (strategy) {
      case 'extractive':
        summary = this.extractiveSummarization(sentences, options.max_length || 200);
        break;
      case 'abstractive':
        summary = this.abstractiveSummarization(text, options.max_length || 200, options.focus);
        break;
      case 'hybrid':
        const extractive = this.extractiveSummarization(sentences, Math.floor((options.max_length || 200) * 0.7));
        const abstractive = this.abstractiveSummarization(text, Math.floor((options.max_length || 200) * 0.3), options.focus);
        summary = `${extractive} ${abstractive}`;
        break;
      default:
        summary = this.extractiveSummarization(sentences, options.max_length || 200);
    }

    // Apply formatting
    if (options.format === 'bullet_points') {
      summary = this.formatAsBulletPoints(summary);
    } else if (options.format === 'structured') {
      summary = this.formatAsStructured(summary, options.focus);
    }

    const metadata: SummaryMetadata = {
      original_length: text.length,
      summary_length: summary.length,
      compression_ratio: summary.length / text.length,
      key_phrases: keyPhrases,
      confidence_score: this.calculateConfidenceScore(text, summary),
      language_detected: this.detectLanguage(text),
    };

    return { summary, metadata };
  }

  async analyzeText(text: string, analysisTypes: string[]): Promise<TextAnalysis> {
    const analysis: TextAnalysis = {};

    if (analysisTypes.includes('sentiment')) {
      analysis.sentiment = this.analyzeSentiment(text);
    }

    if (analysisTypes.includes('topics')) {
      analysis.topics = this.extractTopics(text);
    }

    if (analysisTypes.includes('entities')) {
      analysis.entities = this.extractEntities(text);
    }

    if (analysisTypes.includes('readability')) {
      analysis.readability = this.analyzeReadability(text);
    }

    if (analysisTypes.includes('structure')) {
      analysis.structure = this.analyzeStructure(text);
    }

    return analysis;
  }

  private extractSentences(text: string): string[] {
    return text.split(/[.!?]+/).filter(s => s.trim().length > 0).map(s => s.trim());
  }

  private extractKeyPhrases(text: string): string[] {
    // Simple keyword extraction based on frequency and length
    const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
    const frequency: { [key: string]: number } = {};
    
    for (const word of words) {
      frequency[word] = (frequency[word] || 0) + 1;
    }

    return Object.entries(frequency)
      .filter(([word, freq]) => freq > 1)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private extractiveSummarization(sentences: string[], maxLength: number): string {
    // Score sentences based on word frequency and position
    const wordFreq: { [key: string]: number } = {};
    const allWords = sentences.join(' ').toLowerCase().match(/\b\w+\b/g) || [];
    
    for (const word of allWords) {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    }

    const sentenceScores = sentences.map((sentence, index) => {
      const words = sentence.toLowerCase().match(/\b\w+\b/g) || [];
      const score = words.reduce((sum, word) => sum + (wordFreq[word] || 0), 0) / words.length;
      const positionScore = index < sentences.length * 0.3 ? 1.2 : 1.0; // Boost early sentences
      return { sentence, score: score * positionScore, index };
    });

    sentenceScores.sort((a, b) => b.score - a.score);

    let summary = '';
    let currentLength = 0;
    
    for (const { sentence } of sentenceScores) {
      if (currentLength + sentence.length <= maxLength) {
        summary += sentence + '. ';
        currentLength += sentence.length + 2;
      }
    }

    return summary.trim();
  }

  private abstractiveSummarization(text: string, maxLength: number, focus?: string): string {
    // Mock abstractive summarization with focus-based templates
    const keyPhrases = this.extractKeyPhrases(text);
    const sentences = this.extractSentences(text);
    
    let template: string;
    switch (focus) {
      case 'key_points':
        template = `The main points are: ${keyPhrases.slice(0, 3).join(', ')}.`;
        break;
      case 'action_items':
        template = `Key actions include: ${keyPhrases.slice(0, 2).join(' and ')}.`;
        break;
      case 'technical':
        template = `Technical summary: ${keyPhrases.slice(0, 4).join(', ')} are discussed.`;
        break;
      case 'narrative':
        template = `This text describes ${keyPhrases[0]} and its relationship to ${keyPhrases[1] || 'related concepts'}.`;
        break;
      default:
        template = `Summary: ${sentences[0]?.substring(0, maxLength - 20) || 'Content overview'}.`;
    }

    return template.substring(0, maxLength);
  }

  private formatAsBulletPoints(summary: string): string {
    const sentences = summary.split(/[.!?]+/).filter(s => s.trim().length > 0);
    return sentences.map(s => `• ${s.trim()}`).join('\n');
  }

  private formatAsStructured(summary: string, focus?: string): string {
    const sentences = summary.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    switch (focus) {
      case 'action_items':
        return `**Action Items:**\n${sentences.map(s => `- ${s.trim()}`).join('\n')}`;
      case 'key_points':
        return `**Key Points:**\n${sentences.map(s => `1. ${s.trim()}`).join('\n')}`;
      default:
        return `**Summary:**\n${summary}`;
    }
  }

  private calculateConfidenceScore(original: string, summary: string): number {
    // Simple confidence based on compression ratio and key phrase preservation
    const ratio = summary.length / original.length;
    const optimalRatio = 0.3; // 30% compression is considered optimal
    const ratioScore = 1 - Math.abs(ratio - optimalRatio) / optimalRatio;
    
    return Math.max(0.1, Math.min(1.0, ratioScore));
  }

  private detectLanguage(text: string): string {
    // Simple language detection based on common words
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const englishCount = words.filter(word => englishWords.includes(word)).length;
    
    return englishCount > words.length * 0.1 ? 'en' : 'unknown';
  }

  private analyzeSentiment(text: string): TextAnalysis['sentiment'] {
    // Simple sentiment analysis based on positive/negative word counts
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'positive', 'success'];
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'negative', 'failure', 'problem', 'issue'];
    
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;
    
    const polarity = (positiveCount - negativeCount) / Math.max(words.length, 1);
    const subjectivity = (positiveCount + negativeCount) / Math.max(words.length, 1);
    
    let label: 'positive' | 'negative' | 'neutral';
    if (polarity > 0.1) label = 'positive';
    else if (polarity < -0.1) label = 'negative';
    else label = 'neutral';
    
    return { polarity, subjectivity, label };
  }

  private extractTopics(text: string): TextAnalysis['topics'] {
    const keyPhrases = this.extractKeyPhrases(text);
    return keyPhrases.slice(0, 5).map(phrase => ({
      topic: phrase,
      confidence: Math.random() * 0.5 + 0.5 // Mock confidence between 0.5-1.0
    }));
  }

  private extractEntities(text: string): TextAnalysis['entities'] {
    // Simple entity extraction based on capitalization patterns
    const entities = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
    return entities.slice(0, 10).map(entity => ({
      text: entity,
      type: entity.length > 10 ? 'ORGANIZATION' : 'PERSON',
      confidence: Math.random() * 0.4 + 0.6
    }));
  }

  private analyzeReadability(text: string): TextAnalysis['readability'] {
    const sentences = this.extractSentences(text);
    const words = text.match(/\b\w+\b/g) || [];
    const syllables = words.reduce((sum, word) => sum + this.countSyllables(word), 0);
    
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    
    // Flesch Reading Ease
    const fleschReadingEase = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
    
    // Flesch-Kincaid Grade Level
    const fleschKincaidGrade = (0.39 * avgSentenceLength) + (11.8 * avgSyllablesPerWord) - 15.59;
    
    // Automated Readability Index
    const avgWordsPerSentence = words.length / sentences.length;
    const avgCharsPerWord = text.replace(/\s/g, '').length / words.length;
    const automatedReadabilityIndex = (4.71 * avgCharsPerWord) + (0.5 * avgWordsPerSentence) - 21.43;
    
    let readingLevel: string;
    if (fleschReadingEase >= 90) readingLevel = 'Very Easy';
    else if (fleschReadingEase >= 80) readingLevel = 'Easy';
    else if (fleschReadingEase >= 70) readingLevel = 'Fairly Easy';
    else if (fleschReadingEase >= 60) readingLevel = 'Standard';
    else if (fleschReadingEase >= 50) readingLevel = 'Fairly Difficult';
    else if (fleschReadingEase >= 30) readingLevel = 'Difficult';
    else readingLevel = 'Very Difficult';
    
    return {
      flesch_reading_ease: Math.round(fleschReadingEase * 100) / 100,
      flesch_kincaid_grade: Math.round(fleschKincaidGrade * 100) / 100,
      automated_readability_index: Math.round(automatedReadabilityIndex * 100) / 100,
      reading_level: readingLevel
    };
  }

  private analyzeStructure(text: string): TextAnalysis['structure'] {
    const sentences = this.extractSentences(text);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const words = text.match(/\b\w+\b/g) || [];
    const characters = text.length;
    
    return {
      sentences: sentences.length,
      paragraphs: paragraphs.length,
      words: words.length,
      characters,
      avg_sentence_length: Math.round((words.length / sentences.length) * 100) / 100,
      avg_word_length: Math.round((characters / words.length) * 100) / 100
    };
  }

  private countSyllables(word: string): number {
    // Simple syllable counting
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;

    for (const char of word.toLowerCase()) {
      const isVowel = vowels.includes(char);
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    // Adjust for silent 'e'
    if (word.toLowerCase().endsWith('e')) {
      count--;
    }

    return Math.max(1, count);
  }
}

/**
 * Summarize MCP Server
 */
export class SummarizeServer extends EventEmitter {
  private config: SummarizeServerConfig;
  private tracer: TraceLogger;
  private wss: WebSocketServer | null = null;
  private clients = new Map<WebSocket, ClientInfo>();
  private summarizationEngine: MockSummarizationEngine;
  private cache = new Map<string, any>();
  private rateLimitCounters = new Map<string, RateLimitCounter>();

  constructor(config: Partial<SummarizeServerConfig>) {
    super();

    this.config = {
      port: 8082,
      host: '0.0.0.0',
      models: this.getDefaultModels(),
      maxTextLength: 50000,
      enableCaching: true,
      cacheSize: 500,
      enableTracing: true,
      rateLimits: {
        requestsPerMinute: 50,
        charactersPerMinute: 100000,
      },
      ...config,
    };

    this.tracer = new TraceLogger({
      serviceName: 'mcp-summarize-server',
      enableConsoleSpans: this.config.enableTracing,
    });

    this.summarizationEngine = new MockSummarizationEngine(this.config.models);

    logger.info('Summarize MCP server initialized', {
      port: this.config.port,
      models: this.config.models.length,
      caching: this.config.enableCaching,
    });
  }

  async start(): Promise<void> {
    const span = this.tracer.startSpan('start_summarize_server');

    try {
      this.wss = new WebSocketServer({
        port: this.config.port,
        host: this.config.host,
      });

      this.wss.on('connection', (ws, request) => {
        this.handleConnection(ws, request);
      });

      this.wss.on('error', (error) => {
        logger.error('WebSocket server error', { error });
        this.emit('error', error);
      });

      // Start cleanup interval
      setInterval(() => this.cleanup(), 60000); // 1 minute

      span.setAttributes({
        port: this.config.port,
        host: this.config.host,
      });

      logger.info('Summarize MCP server started', {
        port: this.config.port,
        host: this.config.host,
      });

      this.emit('started');

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to start summarize server', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  async stop(): Promise<void> {
    logger.info('Stopping summarize MCP server');

    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    // Close all client connections
    for (const ws of this.clients.keys()) {
      ws.close(1000, 'Server shutdown');
    }
    this.clients.clear();

    this.cache.clear();
    this.rateLimitCounters.clear();

    this.emit('stopped');
    logger.info('Summarize MCP server stopped');
  }

  private handleConnection(ws: WebSocket, request: any): void {
    const clientInfo: ClientInfo = {
      id: Math.random().toString(36).substr(2, 9),
      connectedAt: new Date(),
      lastActivity: new Date(),
      requestCount: 0,
      ipAddress: request.socket.remoteAddress,
      userAgent: request.headers['user-agent'],
    };

    this.clients.set(ws, clientInfo);

    logger.info('Client connected', {
      clientId: clientInfo.id,
      ipAddress: clientInfo.ipAddress,
    });

    ws.on('message', async (data) => {
      await this.handleMessage(ws, clientInfo, data);
    });

    ws.on('close', (code, reason) => {
      this.clients.delete(ws);
      logger.info('Client disconnected', {
        clientId: clientInfo.id,
        code,
        reason: reason.toString(),
      });
    });

    ws.on('error', (error) => {
      logger.error('Client connection error', {
        clientId: clientInfo.id,
        error,
      });
    });

    // Send initial server capabilities
    this.sendMessage(ws, {
      jsonrpc: '2.0',
      method: 'initialized',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: [
            {
              name: 'summarize_text',
              description: 'Summarize text using various strategies',
              inputSchema: {
                type: 'object',
                properties: {
                  text: { type: 'string' },
                  strategy: { type: 'string', enum: ['extractive', 'abstractive', 'hybrid'] },
                  model: { type: 'string', enum: this.config.models.map(m => m.id) },
                  max_length: { type: 'number', minimum: 50, maximum: 1000 },
                  min_length: { type: 'number', minimum: 10, maximum: 500 },
                  focus: { type: 'string', enum: ['general', 'key_points', 'action_items', 'technical', 'narrative'] },
                  format: { type: 'string', enum: ['paragraph', 'bullet_points', 'structured'] }
                },
                required: ['text']
              }
            },
            {
              name: 'batch_summarize',
              description: 'Summarize multiple documents',
              inputSchema: {
                type: 'object',
                properties: {
                  documents: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        text: { type: 'string' },
                        title: { type: 'string' },
                        metadata: { type: 'object' }
                      },
                      required: ['id', 'text']
                    }
                  },
                  strategy: { type: 'string', enum: ['extractive', 'abstractive', 'hybrid'] },
                  model: { type: 'string', enum: this.config.models.map(m => m.id) },
                  max_length: { type: 'number', minimum: 50, maximum: 1000 },
                  preserve_order: { type: 'boolean' }
                },
                required: ['documents']
              }
            },
            {
              name: 'analyze_text',
              description: 'Analyze text for sentiment, topics, entities, and readability',
              inputSchema: {
                type: 'object',
                properties: {
                  text: { type: 'string' },
                  analysis_types: {
                    type: 'array',
                    items: { type: 'string', enum: ['sentiment', 'topics', 'entities', 'readability', 'structure'] }
                  }
                },
                required: ['text']
              }
            }
          ],
          resources: [
            {
              uri: 'summarize://models',
              name: 'Available Models',
              description: 'List of available summarization models'
            }
          ]
        },
        serverInfo: {
          name: 'Summarize MCP Server',
          version: '1.0.0'
        }
      }
    });
  }

  private async handleMessage(ws: WebSocket, clientInfo: ClientInfo, data: Buffer): Promise<void> {
    const span = this.tracer.startSpan('handle_message', {
      clientId: clientInfo.id,
    });

    try {
      const message: MCPMessage = JSON.parse(data.toString());
      clientInfo.lastActivity = new Date();
      clientInfo.requestCount++;

      logger.debug('Received message', {
        clientId: clientInfo.id,
        method: message.method,
        id: message.id,
      });

      // Check rate limits
      await this.checkRateLimit(clientInfo);

      let response: MCPMessage;

      switch (message.method) {
        case 'tools/call':
          response = await this.handleToolCall(message, clientInfo);
          break;
        case 'resources/read':
          response = await this.handleResourceRead(message, clientInfo);
          break;
        case 'ping':
          response = {
            jsonrpc: '2.0',
            id: message.id,
            result: { pong: true, timestamp: new Date().toISOString() }
          };
          break;
        default:
          response = {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32601,
              message: `Method not found: ${message.method}`
            }
          };
      }

      await this.sendMessage(ws, response);

      span.setAttributes({
        method: message.method,
        success: !response.error,
      });

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Message handling error', { clientId: clientInfo.id, error });

      const errorResponse: MCPMessage = {
        jsonrpc: '2.0',
        id: (JSON.parse(data.toString()) as MCPMessage).id,
        error: {
          code: -32603,
          message: 'Internal error',
          data: (error as Error).message
        }
      };

      await this.sendMessage(ws, errorResponse);
    } finally {
      span.end();
    }
  }

  private async handleToolCall(message: MCPMessage, clientInfo: ClientInfo): Promise<MCPMessage> {
    const { name, arguments: args } = message.params;

    switch (name) {
      case 'summarize_text':
        return await this.handleSummarizeText(message.id, args, clientInfo);
      case 'batch_summarize':
        return await this.handleBatchSummarize(message.id, args, clientInfo);
      case 'analyze_text':
        return await this.handleAnalyzeText(message.id, args, clientInfo);
      default:
        return {
          jsonrpc: '2.0',
          id: message.id,
          error: {
            code: -32601,
            message: `Tool not found: ${name}`
          }
        };
    }
  }

  private async handleSummarizeText(
    messageId: any,
    request: SummarizeRequest,
    clientInfo: ClientInfo
  ): Promise<MCPMessage> {
    const span = this.tracer.startSpan('summarize_text', {
      clientId: clientInfo.id,
      strategy: request.strategy || 'extractive',
      model: request.model || 'default',
    });

    const startTime = Date.now();

    try {
      // Validate request
      if (request.text.length > this.config.maxTextLength) {
        throw new Error(`Text length ${request.text.length} exceeds limit of ${this.config.maxTextLength}`);
      }

      const strategy = request.strategy || 'extractive';
      const model = request.model || this.config.models[0].id;

      // Check cache
      let result: SummarizeResponse;
      const cacheKey = this.getCacheKey('summarize', {
        text: request.text,
        strategy,
        model,
        max_length: request.max_length,
        focus: request.focus,
        format: request.format
      });

      if (this.config.enableCaching && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        result = {
          ...cached,
          execution_time: Date.now() - startTime
        };
        span.setAttributes({ cached: true });
      } else {
        // Generate summary
        const { summary, metadata } = await this.summarizationEngine.summarizeText(
          request.text,
          strategy,
          model,
          request
        );

        result = {
          summary,
          strategy,
          model,
          metadata,
          execution_time: Date.now() - startTime
        };

        // Cache result
        if (this.config.enableCaching) {
          this.cache.set(cacheKey, result);
          if (this.cache.size > this.config.cacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
          }
        }

        span.setAttributes({
          cached: false,
          textLength: request.text.length,
          summaryLength: summary.length,
          compressionRatio: metadata.compression_ratio,
          strategy,
          model,
        });
      }

      return {
        jsonrpc: '2.0',
        id: messageId,
        result
      };

    } catch (error) {
      span.recordException(error as Error);
      return {
        jsonrpc: '2.0',
        id: messageId,
        error: {
          code: -32000,
          message: `Text summarization failed: ${(error as Error).message}`
        }
      };
    } finally {
      span.end();
    }
  }

  private async handleBatchSummarize(
    messageId: any,
    request: BatchSummarizeRequest,
    clientInfo: ClientInfo
  ): Promise<MCPMessage> {
    const span = this.tracer.startSpan('batch_summarize', {
      clientId: clientInfo.id,
      documentsCount: request.documents.length,
    });

    const startTime = Date.now();

    try {
      const strategy = request.strategy || 'extractive';
      const model = request.model || this.config.models[0].id;
      const maxLength = request.max_length || 200;

      const summaries: DocumentSummary[] = [];

      // Process documents in parallel (with concurrency limit)
      const concurrencyLimit = 5;
      const chunks = [];
      for (let i = 0; i < request.documents.length; i += concurrencyLimit) {
        chunks.push(request.documents.slice(i, i + concurrencyLimit));
      }

      for (const chunk of chunks) {
        const chunkPromises = chunk.map(async (doc) => {
          const { summary, metadata } = await this.summarizationEngine.summarizeText(
            doc.text,
            strategy,
            model,
            { max_length: maxLength }
          );

          return {
            id: doc.id,
            summary,
            title: doc.title,
            metadata
          };
        });

        const chunkResults = await Promise.all(chunkPromises);
        summaries.push(...chunkResults);
      }

      // Preserve order if requested
      if (request.preserve_order) {
        summaries.sort((a, b) => {
          const aIndex = request.documents.findIndex(doc => doc.id === a.id);
          const bIndex = request.documents.findIndex(doc => doc.id === b.id);
          return aIndex - bIndex;
        });
      }

      const result: BatchSummarizeResponse = {
        summaries,
        strategy,
        model,
        execution_time: Date.now() - startTime,
        total_documents: request.documents.length
      };

      span.setAttributes({
        documentsProcessed: summaries.length,
        executionTime: result.execution_time,
      });

      return {
        jsonrpc: '2.0',
        id: messageId,
        result
      };

    } catch (error) {
      span.recordException(error as Error);
      return {
        jsonrpc: '2.0',
        id: messageId,
        error: {
          code: -32000,
          message: `Batch summarization failed: ${(error as Error).message}`
        }
      };
    } finally {
      span.end();
    }
  }

  private async handleAnalyzeText(
    messageId: any,
    request: AnalyzeTextRequest,
    clientInfo: ClientInfo
  ): Promise<MCPMessage> {
    const span = this.tracer.startSpan('analyze_text', {
      clientId: clientInfo.id,
      analysisTypes: request.analysis_types?.join(',') || 'all',
    });

    const startTime = Date.now();

    try {
      const analysisTypes = request.analysis_types || ['sentiment', 'topics', 'entities', 'readability', 'structure'];

      const analysis = await this.summarizationEngine.analyzeText(request.text, analysisTypes);

      const result: AnalyzeTextResponse = {
        analysis,
        execution_time: Date.now() - startTime
      };

      span.setAttributes({
        textLength: request.text.length,
        analysisTypesCount: analysisTypes.length,
        executionTime: result.execution_time,
      });

      return {
        jsonrpc: '2.0',
        id: messageId,
        result
      };

    } catch (error) {
      span.recordException(error as Error);
      return {
        jsonrpc: '2.0',
        id: messageId,
        error: {
          code: -32000,
          message: `Text analysis failed: ${(error as Error).message}`
        }
      };
    } finally {
      span.end();
    }
  }

  private async handleResourceRead(message: MCPMessage, clientInfo: ClientInfo): Promise<MCPMessage> {
    const { uri } = message.params;

    if (uri === 'summarize://models') {
      return {
        jsonrpc: '2.0',
        id: message.id,
        result: {
          contents: [
            {
              uri,
              mimeType: 'application/json',
              text: JSON.stringify({
                models: this.config.models.map(model => ({
                  id: model.id,
                  name: model.name,
                  description: model.description,
                  strategies: model.strategies,
                  maxInputLength: model.maxInputLength,
                  maxOutputLength: model.maxOutputLength,
                  provider: model.provider
                }))
              }, null, 2)
            }
          ]
        }
      };
    }

    return {
      jsonrpc: '2.0',
      id: message.id,
      error: {
        code: -32601,
        message: `Resource not found: ${uri}`
      }
    };
  }

  private async sendMessage(ws: WebSocket, message: MCPMessage): Promise<void> {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private async checkRateLimit(clientInfo: ClientInfo): Promise<void> {
    const key = `${clientInfo.id}:${clientInfo.ipAddress}`;
    const now = Date.now();

    let counter = this.rateLimitCounters.get(key);
    if (!counter) {
      counter = {
        requests: 0,
        characters: 0,
        windowStart: now
      };
      this.rateLimitCounters.set(key, counter);
    }

    // Reset window if minute has passed
    if (now - counter.windowStart >= 60000) {
      counter.requests = 0;
      counter.characters = 0;
      counter.windowStart = now;
    }

    counter.requests++;

    if (counter.requests > this.config.rateLimits.requestsPerMinute) {
      throw new Error('Rate limit exceeded: too many requests');
    }
  }

  private getCacheKey(operation: string, params: any): string {
    return createHash('sha256')
      .update(`${operation}:${JSON.stringify(params)}`)
      .digest('hex')
      .substring(0, 16);
  }

  private getDefaultModels(): SummarizationModel[] {
    return [
      {
        id: 'gpt-4-turbo-summarize',
        name: 'GPT-4 Turbo Summarization',
        description: 'Advanced summarization with GPT-4 Turbo',
        strategies: ['extractive', 'abstractive', 'hybrid'],
        maxInputLength: 100000,
        maxOutputLength: 4000,
        costPerCharacter: 0.00001,
        provider: 'mock',
      },
      {
        id: 'claude-3-summarize',
        name: 'Claude 3 Summarization',
        description: 'High-quality summarization with Claude 3',
        strategies: ['extractive', 'abstractive', 'hybrid'],
        maxInputLength: 150000,
        maxOutputLength: 4000,
        costPerCharacter: 0.000008,
        provider: 'mock',
      },
      {
        id: 'local-summarize-v1',
        name: 'Local Summarization v1',
        description: 'Local summarization model for offline use',
        strategies: ['extractive'],
        maxInputLength: 10000,
        maxOutputLength: 1000,
        costPerCharacter: 0,
        provider: 'mock',
      }
    ];
  }

  private cleanup(): void {
    const now = Date.now();

    // Clean up old rate limit counters
    for (const [key, counter] of this.rateLimitCounters.entries()) {
      if (now - counter.windowStart > 300000) { // 5 minutes
        this.rateLimitCounters.delete(key);
      }
    }

    logger.debug('Summarize server cleanup completed', {
      rateLimitCounters: this.rateLimitCounters.size,
      cacheSize: this.cache.size,
      activeClients: this.clients.size,
    });
  }

  getServerInfo(): {
    status: string;
    clients: number;
    models: number;
    cacheSize: number;
    uptime: number;
  } {
    return {
      status: this.wss ? 'running' : 'stopped',
      clients: this.clients.size,
      models: this.config.models.length,
      cacheSize: this.cache.size,
      uptime: process.uptime(),
    };
  }
}

interface ClientInfo {
  id: string;
  connectedAt: Date;
  lastActivity: Date;
  requestCount: number;
  ipAddress?: string;
  userAgent?: string;
}

interface RateLimitCounter {
  requests: number;
  characters: number;
  windowStart: number;
}
