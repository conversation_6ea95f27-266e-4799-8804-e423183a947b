/**
 * Embeddings MCP Server Example
 * Provides text embedding capabilities with multiple embedding models
 */

import { EventEmitter } from 'events';
import { WebSocketServer, WebSocket } from 'ws';
import { createHash } from 'crypto';
import { logger } from '../../utils/logger';
import { TraceLogger } from '../../runtime/trace-logger';

// MCP Types
export interface MCPMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

// Embedding Types
export interface EmbeddingRequest {
  text: string | string[];
  model?: string;
  dimensions?: number;
  normalize?: boolean;
  encoding_format?: 'float' | 'base64';
  truncate?: 'start' | 'end' | 'none';
  batch_size?: number;
}

export interface EmbeddingResponse {
  object: 'list';
  data: EmbeddingData[];
  model: string;
  usage: EmbeddingUsage;
}

export interface EmbeddingData {
  object: 'embedding';
  index: number;
  embedding: number[] | string;
}

export interface EmbeddingUsage {
  prompt_tokens: number;
  total_tokens: number;
}

export interface SimilarityRequest {
  query: string;
  documents: string[];
  model?: string;
  threshold?: number;
  top_k?: number;
}

export interface SimilarityResponse {
  similarities: SimilarityResult[];
  model: string;
  execution_time: number;
}

export interface SimilarityResult {
  index: number;
  document: string;
  similarity: number;
}

export interface ClusterRequest {
  texts: string[];
  model?: string;
  num_clusters?: number;
  method?: 'kmeans' | 'hierarchical' | 'dbscan';
  min_cluster_size?: number;
}

export interface ClusterResponse {
  clusters: ClusterResult[];
  model: string;
  method: string;
  execution_time: number;
}

export interface ClusterResult {
  cluster_id: number;
  documents: ClusterDocument[];
  centroid?: number[];
  coherence_score?: number;
}

export interface ClusterDocument {
  index: number;
  text: string;
  distance_to_centroid?: number;
}

// Server Configuration
export interface EmbeddingsServerConfig {
  port: number;
  host: string;
  models: EmbeddingModel[];
  maxBatchSize: number;
  maxTextLength: number;
  enableCaching: boolean;
  cacheSize: number;
  enableTracing: boolean;
  rateLimits: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface EmbeddingModel {
  id: string;
  name: string;
  description: string;
  dimensions: number;
  maxTokens: number;
  costPerToken: number;
  provider: 'openai' | 'local' | 'huggingface' | 'mock';
  config?: any;
}

// Mock Embedding Engine (for demonstration)
class MockEmbeddingEngine {
  private models: Map<string, EmbeddingModel> = new Map();

  constructor(models: EmbeddingModel[]) {
    for (const model of models) {
      this.models.set(model.id, model);
    }
  }

  async generateEmbeddings(
    texts: string[],
    modelId: string,
    dimensions?: number
  ): Promise<{ embeddings: number[][]; tokenCount: number }> {
    const model = this.models.get(modelId);
    if (!model) {
      throw new Error(`Model ${modelId} not found`);
    }

    const embeddingDimensions = dimensions || model.dimensions;
    const embeddings: number[][] = [];
    let totalTokens = 0;

    for (const text of texts) {
      // Simulate token counting (roughly 4 chars per token)
      const tokens = Math.ceil(text.length / 4);
      totalTokens += tokens;

      if (tokens > model.maxTokens) {
        throw new Error(`Text too long: ${tokens} tokens exceeds limit of ${model.maxTokens}`);
      }

      // Generate deterministic mock embedding based on text content
      const embedding = this.generateMockEmbedding(text, embeddingDimensions);
      embeddings.push(embedding);
    }

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, texts.length * 10));

    return { embeddings, tokenCount: totalTokens };
  }

  private generateMockEmbedding(text: string, dimensions: number): number[] {
    // Create deterministic embedding based on text content
    const hash = createHash('sha256').update(text).digest();
    const embedding: number[] = [];

    for (let i = 0; i < dimensions; i++) {
      // Use hash bytes to generate pseudo-random but deterministic values
      const byteIndex = i % hash.length;
      const value = (hash[byteIndex] / 255.0) * 2 - 1; // Normalize to [-1, 1]
      embedding.push(value);
    }

    // Normalize the vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    // Calculate cosine similarity
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  async clusterTexts(
    texts: string[],
    modelId: string,
    numClusters: number,
    method: string
  ): Promise<ClusterResult[]> {
    // Generate embeddings for all texts
    const { embeddings } = await this.generateEmbeddings(texts, modelId);

    // Simple K-means clustering implementation
    if (method === 'kmeans') {
      return this.kMeansClustering(texts, embeddings, numClusters);
    }

    throw new Error(`Clustering method ${method} not implemented`);
  }

  private kMeansClustering(
    texts: string[],
    embeddings: number[][],
    k: number
  ): ClusterResult[] {
    const dimensions = embeddings[0].length;
    const maxIterations = 100;

    // Initialize centroids randomly
    let centroids: number[][] = [];
    for (let i = 0; i < k; i++) {
      const centroid: number[] = [];
      for (let j = 0; j < dimensions; j++) {
        centroid.push(Math.random() * 2 - 1);
      }
      centroids.push(centroid);
    }

    let assignments: number[] = new Array(embeddings.length).fill(0);

    // K-means iterations
    for (let iter = 0; iter < maxIterations; iter++) {
      const newAssignments: number[] = [];

      // Assign each point to nearest centroid
      for (let i = 0; i < embeddings.length; i++) {
        let bestCluster = 0;
        let bestDistance = Infinity;

        for (let j = 0; j < k; j++) {
          const distance = this.euclideanDistance(embeddings[i], centroids[j]);
          if (distance < bestDistance) {
            bestDistance = distance;
            bestCluster = j;
          }
        }

        newAssignments.push(bestCluster);
      }

      // Check for convergence
      if (assignments.every((val, idx) => val === newAssignments[idx])) {
        break;
      }

      assignments = newAssignments;

      // Update centroids
      const newCentroids: number[][] = [];
      for (let j = 0; j < k; j++) {
        const clusterPoints = embeddings.filter((_, idx) => assignments[idx] === j);
        
        if (clusterPoints.length === 0) {
          newCentroids.push(centroids[j]); // Keep old centroid if no points
          continue;
        }

        const centroid: number[] = new Array(dimensions).fill(0);
        for (const point of clusterPoints) {
          for (let d = 0; d < dimensions; d++) {
            centroid[d] += point[d];
          }
        }
        for (let d = 0; d < dimensions; d++) {
          centroid[d] /= clusterPoints.length;
        }
        newCentroids.push(centroid);
      }

      centroids = newCentroids;
    }

    // Build cluster results
    const clusters: ClusterResult[] = [];
    for (let j = 0; j < k; j++) {
      const clusterDocuments: ClusterDocument[] = [];
      let coherenceSum = 0;
      let documentCount = 0;

      for (let i = 0; i < texts.length; i++) {
        if (assignments[i] === j) {
          const distance = this.euclideanDistance(embeddings[i], centroids[j]);
          clusterDocuments.push({
            index: i,
            text: texts[i],
            distance_to_centroid: distance,
          });
          coherenceSum += distance;
          documentCount++;
        }
      }

      clusters.push({
        cluster_id: j,
        documents: clusterDocuments,
        centroid: centroids[j],
        coherence_score: documentCount > 0 ? coherenceSum / documentCount : 0,
      });
    }

    return clusters;
  }

  private euclideanDistance(a: number[], b: number[]): number {
    let sum = 0;
    for (let i = 0; i < a.length; i++) {
      sum += (a[i] - b[i]) ** 2;
    }
    return Math.sqrt(sum);
  }
}

/**
 * Embeddings MCP Server
 */
export class EmbeddingsServer extends EventEmitter {
  private config: EmbeddingsServerConfig;
  private tracer: TraceLogger;
  private wss: WebSocketServer | null = null;
  private clients = new Map<WebSocket, ClientInfo>();
  private embeddingEngine: MockEmbeddingEngine;
  private cache = new Map<string, any>();
  private rateLimitCounters = new Map<string, RateLimitCounter>();

  constructor(config: Partial<EmbeddingsServerConfig>) {
    super();

    this.config = {
      port: 8081,
      host: '0.0.0.0',
      models: this.getDefaultModels(),
      maxBatchSize: 100,
      maxTextLength: 8192,
      enableCaching: true,
      cacheSize: 1000,
      enableTracing: true,
      rateLimits: {
        requestsPerMinute: 100,
        tokensPerMinute: 10000,
      },
      ...config,
    };

    this.tracer = new TraceLogger({
      serviceName: 'mcp-embeddings-server',
      enableConsoleSpans: this.config.enableTracing,
    });

    this.embeddingEngine = new MockEmbeddingEngine(this.config.models);

    logger.info('Embeddings MCP server initialized', {
      port: this.config.port,
      models: this.config.models.length,
      caching: this.config.enableCaching,
    });
  }

  async start(): Promise<void> {
    const span = this.tracer.startSpan('start_embeddings_server');

    try {
      this.wss = new WebSocketServer({
        port: this.config.port,
        host: this.config.host,
      });

      this.wss.on('connection', (ws, request) => {
        this.handleConnection(ws, request);
      });

      this.wss.on('error', (error) => {
        logger.error('WebSocket server error', { error });
        this.emit('error', error);
      });

      // Start cleanup interval
      setInterval(() => this.cleanup(), 60000); // 1 minute

      span.setAttributes({
        port: this.config.port,
        host: this.config.host,
      });

      logger.info('Embeddings MCP server started', {
        port: this.config.port,
        host: this.config.host,
      });

      this.emit('started');

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to start embeddings server', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  async stop(): Promise<void> {
    logger.info('Stopping embeddings MCP server');

    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    // Close all client connections
    for (const ws of this.clients.keys()) {
      ws.close(1000, 'Server shutdown');
    }
    this.clients.clear();

    this.cache.clear();
    this.rateLimitCounters.clear();

    this.emit('stopped');
    logger.info('Embeddings MCP server stopped');
  }

  private handleConnection(ws: WebSocket, request: any): void {
    const clientInfo: ClientInfo = {
      id: Math.random().toString(36).substr(2, 9),
      connectedAt: new Date(),
      lastActivity: new Date(),
      requestCount: 0,
      ipAddress: request.socket.remoteAddress,
      userAgent: request.headers['user-agent'],
    };

    this.clients.set(ws, clientInfo);

    logger.info('Client connected', {
      clientId: clientInfo.id,
      ipAddress: clientInfo.ipAddress,
    });

    ws.on('message', async (data) => {
      await this.handleMessage(ws, clientInfo, data);
    });

    ws.on('close', (code, reason) => {
      this.clients.delete(ws);
      logger.info('Client disconnected', {
        clientId: clientInfo.id,
        code,
        reason: reason.toString(),
      });
    });

    ws.on('error', (error) => {
      logger.error('Client connection error', {
        clientId: clientInfo.id,
        error,
      });
    });

    // Send initial server capabilities
    this.sendMessage(ws, {
      jsonrpc: '2.0',
      method: 'initialized',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: [
            {
              name: 'generate_embeddings',
              description: 'Generate embeddings for text(s)',
              inputSchema: {
                type: 'object',
                properties: {
                  text: {
                    oneOf: [
                      { type: 'string' },
                      { type: 'array', items: { type: 'string' } }
                    ]
                  },
                  model: { type: 'string', enum: this.config.models.map(m => m.id) },
                  dimensions: { type: 'number', minimum: 1 },
                  normalize: { type: 'boolean' },
                  encoding_format: { type: 'string', enum: ['float', 'base64'] }
                },
                required: ['text']
              }
            },
            {
              name: 'calculate_similarity',
              description: 'Calculate similarity between query and documents',
              inputSchema: {
                type: 'object',
                properties: {
                  query: { type: 'string' },
                  documents: { type: 'array', items: { type: 'string' } },
                  model: { type: 'string', enum: this.config.models.map(m => m.id) },
                  threshold: { type: 'number', minimum: 0, maximum: 1 },
                  top_k: { type: 'number', minimum: 1 }
                },
                required: ['query', 'documents']
              }
            },
            {
              name: 'cluster_texts',
              description: 'Cluster texts using embeddings',
              inputSchema: {
                type: 'object',
                properties: {
                  texts: { type: 'array', items: { type: 'string' } },
                  model: { type: 'string', enum: this.config.models.map(m => m.id) },
                  num_clusters: { type: 'number', minimum: 1 },
                  method: { type: 'string', enum: ['kmeans', 'hierarchical', 'dbscan'] },
                  min_cluster_size: { type: 'number', minimum: 1 }
                },
                required: ['texts']
              }
            }
          ],
          resources: [
            {
              uri: 'embeddings://models',
              name: 'Available Models',
              description: 'List of available embedding models'
            }
          ]
        },
        serverInfo: {
          name: 'Embeddings MCP Server',
          version: '1.0.0'
        }
      }
    });
  }

  private async handleMessage(ws: WebSocket, clientInfo: ClientInfo, data: Buffer): Promise<void> {
    const span = this.tracer.startSpan('handle_message', {
      clientId: clientInfo.id,
    });

    try {
      const message: MCPMessage = JSON.parse(data.toString());
      clientInfo.lastActivity = new Date();
      clientInfo.requestCount++;

      logger.debug('Received message', {
        clientId: clientInfo.id,
        method: message.method,
        id: message.id,
      });

      // Check rate limits
      await this.checkRateLimit(clientInfo);

      let response: MCPMessage;

      switch (message.method) {
        case 'tools/call':
          response = await this.handleToolCall(message, clientInfo);
          break;
        case 'resources/read':
          response = await this.handleResourceRead(message, clientInfo);
          break;
        case 'ping':
          response = {
            jsonrpc: '2.0',
            id: message.id,
            result: { pong: true, timestamp: new Date().toISOString() }
          };
          break;
        default:
          response = {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32601,
              message: `Method not found: ${message.method}`
            }
          };
      }

      await this.sendMessage(ws, response);

      span.setAttributes({
        method: message.method,
        success: !response.error,
      });

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Message handling error', { clientId: clientInfo.id, error });

      const errorResponse: MCPMessage = {
        jsonrpc: '2.0',
        id: (JSON.parse(data.toString()) as MCPMessage).id,
        error: {
          code: -32603,
          message: 'Internal error',
          data: (error as Error).message
        }
      };

      await this.sendMessage(ws, errorResponse);
    } finally {
      span.end();
    }
  }

  private async handleToolCall(message: MCPMessage, clientInfo: ClientInfo): Promise<MCPMessage> {
    const { name, arguments: args } = message.params;

    switch (name) {
      case 'generate_embeddings':
        return await this.handleGenerateEmbeddings(message.id, args, clientInfo);
      case 'calculate_similarity':
        return await this.handleCalculateSimilarity(message.id, args, clientInfo);
      case 'cluster_texts':
        return await this.handleClusterTexts(message.id, args, clientInfo);
      default:
        return {
          jsonrpc: '2.0',
          id: message.id,
          error: {
            code: -32601,
            message: `Tool not found: ${name}`
          }
        };
    }
  }

  private async handleGenerateEmbeddings(
    messageId: any,
    request: EmbeddingRequest,
    clientInfo: ClientInfo
  ): Promise<MCPMessage> {
    const span = this.tracer.startSpan('generate_embeddings', {
      clientId: clientInfo.id,
      model: request.model || 'default',
    });

    try {
      // Validate request
      const texts = Array.isArray(request.text) ? request.text : [request.text];
      
      if (texts.length > this.config.maxBatchSize) {
        throw new Error(`Batch size ${texts.length} exceeds limit of ${this.config.maxBatchSize}`);
      }

      for (const text of texts) {
        if (text.length > this.config.maxTextLength) {
          throw new Error(`Text length ${text.length} exceeds limit of ${this.config.maxTextLength}`);
        }
      }

      const model = request.model || this.config.models[0].id;

      // Check cache
      let result: EmbeddingResponse;
      const cacheKey = this.getCacheKey('embeddings', { texts, model, dimensions: request.dimensions });
      
      if (this.config.enableCaching && this.cache.has(cacheKey)) {
        result = this.cache.get(cacheKey);
        span.setAttributes({ cached: true });
      } else {
        // Generate embeddings
        const { embeddings, tokenCount } = await this.embeddingEngine.generateEmbeddings(
          texts,
          model,
          request.dimensions
        );

        // Format response
        const data: EmbeddingData[] = embeddings.map((embedding, index) => ({
          object: 'embedding',
          index,
          embedding: request.encoding_format === 'base64' 
            ? Buffer.from(new Float32Array(embedding).buffer).toString('base64')
            : embedding
        }));

        result = {
          object: 'list',
          data,
          model,
          usage: {
            prompt_tokens: tokenCount,
            total_tokens: tokenCount
          }
        };

        // Cache result
        if (this.config.enableCaching) {
          this.cache.set(cacheKey, result);
          if (this.cache.size > this.config.cacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
          }
        }

        span.setAttributes({
          cached: false,
          textsCount: texts.length,
          tokenCount,
          model,
        });
      }

      return {
        jsonrpc: '2.0',
        id: messageId,
        result
      };

    } catch (error) {
      span.recordException(error as Error);
      return {
        jsonrpc: '2.0',
        id: messageId,
        error: {
          code: -32000,
          message: `Embedding generation failed: ${(error as Error).message}`
        }
      };
    } finally {
      span.end();
    }
  }

  private async handleCalculateSimilarity(
    messageId: any,
    request: SimilarityRequest,
    clientInfo: ClientInfo
  ): Promise<MCPMessage> {
    const span = this.tracer.startSpan('calculate_similarity', {
      clientId: clientInfo.id,
      documentsCount: request.documents.length,
    });

    const startTime = Date.now();

    try {
      const model = request.model || this.config.models[0].id;
      const threshold = request.threshold || 0.0;
      const topK = request.top_k || request.documents.length;

      // Generate embeddings for query and documents
      const allTexts = [request.query, ...request.documents];
      const { embeddings } = await this.embeddingEngine.generateEmbeddings(allTexts, model);

      const queryEmbedding = embeddings[0];
      const documentEmbeddings = embeddings.slice(1);

      // Calculate similarities
      const similarities: SimilarityResult[] = [];
      
      for (let i = 0; i < documentEmbeddings.length; i++) {
        const similarity = this.embeddingEngine.calculateSimilarity(
          queryEmbedding,
          documentEmbeddings[i]
        );

        if (similarity >= threshold) {
          similarities.push({
            index: i,
            document: request.documents[i],
            similarity
          });
        }
      }

      // Sort by similarity and take top K
      similarities.sort((a, b) => b.similarity - a.similarity);
      const topSimilarities = similarities.slice(0, topK);

      const result: SimilarityResponse = {
        similarities: topSimilarities,
        model,
        execution_time: Date.now() - startTime
      };

      span.setAttributes({
        similaritiesFound: topSimilarities.length,
        executionTime: result.execution_time,
      });

      return {
        jsonrpc: '2.0',
        id: messageId,
        result
      };

    } catch (error) {
      span.recordException(error as Error);
      return {
        jsonrpc: '2.0',
        id: messageId,
        error: {
          code: -32000,
          message: `Similarity calculation failed: ${(error as Error).message}`
        }
      };
    } finally {
      span.end();
    }
  }

  private async handleClusterTexts(
    messageId: any,
    request: ClusterRequest,
    clientInfo: ClientInfo
  ): Promise<MCPMessage> {
    const span = this.tracer.startSpan('cluster_texts', {
      clientId: clientInfo.id,
      textsCount: request.texts.length,
      method: request.method || 'kmeans',
    });

    const startTime = Date.now();

    try {
      const model = request.model || this.config.models[0].id;
      const numClusters = request.num_clusters || Math.min(5, Math.ceil(request.texts.length / 10));
      const method = request.method || 'kmeans';

      if (request.texts.length < numClusters) {
        throw new Error(`Number of texts (${request.texts.length}) must be >= number of clusters (${numClusters})`);
      }

      // Perform clustering
      const clusters = await this.embeddingEngine.clusterTexts(
        request.texts,
        model,
        numClusters,
        method
      );

      const result: ClusterResponse = {
        clusters,
        model,
        method,
        execution_time: Date.now() - startTime
      };

      span.setAttributes({
        clustersCreated: clusters.length,
        executionTime: result.execution_time,
      });

      return {
        jsonrpc: '2.0',
        id: messageId,
        result
      };

    } catch (error) {
      span.recordException(error as Error);
      return {
        jsonrpc: '2.0',
        id: messageId,
        error: {
          code: -32000,
          message: `Text clustering failed: ${(error as Error).message}`
        }
      };
    } finally {
      span.end();
    }
  }

  private async handleResourceRead(message: MCPMessage, clientInfo: ClientInfo): Promise<MCPMessage> {
    const { uri } = message.params;

    if (uri === 'embeddings://models') {
      return {
        jsonrpc: '2.0',
        id: message.id,
        result: {
          contents: [
            {
              uri,
              mimeType: 'application/json',
              text: JSON.stringify({
                models: this.config.models.map(model => ({
                  id: model.id,
                  name: model.name,
                  description: model.description,
                  dimensions: model.dimensions,
                  maxTokens: model.maxTokens,
                  provider: model.provider
                }))
              }, null, 2)
            }
          ]
        }
      };
    }

    return {
      jsonrpc: '2.0',
      id: message.id,
      error: {
        code: -32601,
        message: `Resource not found: ${uri}`
      }
    };
  }

  private async sendMessage(ws: WebSocket, message: MCPMessage): Promise<void> {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private async checkRateLimit(clientInfo: ClientInfo): Promise<void> {
    const key = `${clientInfo.id}:${clientInfo.ipAddress}`;
    const now = Date.now();
    
    let counter = this.rateLimitCounters.get(key);
    if (!counter) {
      counter = {
        requests: 0,
        tokens: 0,
        windowStart: now
      };
      this.rateLimitCounters.set(key, counter);
    }

    // Reset window if minute has passed
    if (now - counter.windowStart >= 60000) {
      counter.requests = 0;
      counter.tokens = 0;
      counter.windowStart = now;
    }

    counter.requests++;

    if (counter.requests > this.config.rateLimits.requestsPerMinute) {
      throw new Error('Rate limit exceeded: too many requests');
    }
  }

  private getCacheKey(operation: string, params: any): string {
    return createHash('sha256')
      .update(`${operation}:${JSON.stringify(params)}`)
      .digest('hex')
      .substring(0, 16);
  }

  private getDefaultModels(): EmbeddingModel[] {
    return [
      {
        id: 'text-embedding-3-small',
        name: 'OpenAI Text Embedding 3 Small',
        description: 'High-performance text embedding model with 1536 dimensions',
        dimensions: 1536,
        maxTokens: 8191,
        costPerToken: 0.00002,
        provider: 'mock',
      },
      {
        id: 'text-embedding-3-large',
        name: 'OpenAI Text Embedding 3 Large',
        description: 'Large text embedding model with 3072 dimensions',
        dimensions: 3072,
        maxTokens: 8191,
        costPerToken: 0.00013,
        provider: 'mock',
      },
      {
        id: 'local-embeddings-v1',
        name: 'Local Embeddings v1',
        description: 'Local embedding model for offline use',
        dimensions: 768,
        maxTokens: 512,
        costPerToken: 0,
        provider: 'mock',
      }
    ];
  }

  private cleanup(): void {
    const now = Date.now();

    // Clean up old rate limit counters
    for (const [key, counter] of this.rateLimitCounters.entries()) {
      if (now - counter.windowStart > 300000) { // 5 minutes
        this.rateLimitCounters.delete(key);
      }
    }

    logger.debug('Embeddings server cleanup completed', {
      rateLimitCounters: this.rateLimitCounters.size,
      cacheSize: this.cache.size,
      activeClients: this.clients.size,
    });
  }

  getServerInfo(): {
    status: string;
    clients: number;
    models: number;
    cacheSize: number;
    uptime: number;
  } {
    return {
      status: this.wss ? 'running' : 'stopped',
      clients: this.clients.size,
      models: this.config.models.length,
      cacheSize: this.cache.size,
      uptime: process.uptime(),
    };
  }
}

interface ClientInfo {
  id: string;
  connectedAt: Date;
  lastActivity: Date;
  requestCount: number;
  ipAddress?: string;
  userAgent?: string;
}

interface RateLimitCounter {
  requests: number;
  tokens: number;
  windowStart: number;
}