{"env": {"es2023": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking", "plugin:security/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "security"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "security/detect-object-injection": "error", "security/detect-non-literal-fs-filename": "warn", "security/detect-unsafe-regex": "error", "no-console": "warn", "prefer-const": "error", "no-var": "error"}, "ignorePatterns": ["dist/", "node_modules/", "coverage/", "*.js"]}