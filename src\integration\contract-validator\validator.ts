import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { readFileSync } from 'fs';
import { join } from 'path';
import { logger } from '@/utils/logger';
import { schemaVersionRegistry } from './semantic-versioning';

export interface ValidationResult {
  valid: boolean;
  errors?: string[];
  warnings?: string[];
}

export interface SchemaRegistry {
  registerSchema(schemaId: string, schema: object): void;
  validateContract(data: unknown, schemaId: string): ValidationResult;
  getSchema(schemaId: string): object | undefined;
  listSchemas(): string[];
}

export class ContractValidator implements SchemaRegistry {
  private ajv: Ajv;
  private schemas: Map<string, object> = new Map();

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      verbose: true,
      strict: true,
      validateSchema: true,
      addUsedSchema: false,
    });

    addFormats(this.ajv);
    this.loadBuiltInSchemas();
  }

  private loadBuiltInSchemas(): void {
    try {
      const schemaDir = join(process.cwd(), 'schemas', 'contracts');
      
      const schemas = [
        { id: 'agent', file: 'agent.schema.json', version: '1.0.0' },
        { id: 'mcp', file: 'mcp.schema.json', version: '1.0.0' },
        { id: 'micronote', file: 'micronote.schema.json', version: '1.0.0' },
        { id: 'uielement', file: 'uielement.schema.json', version: '1.0.0' },
        { id: 'codepatch', file: 'codepatch.schema.json', version: '1.0.0' },
        { id: 'tool-io', file: 'tool-io.schema.json', version: '1.0.0' },
      ];

      schemas.forEach(({ id, file, version }) => {
        const schema = JSON.parse(readFileSync(join(schemaDir, file), 'utf-8'));
        this.registerSchema(id, schema);
        
        // Register version information
        schemaVersionRegistry.registerSchemaVersion({
          schemaId: id,
          version,
          checksum: this.calculateChecksum(schema),
          createdAt: new Date(),
        });
      });

      logger.info('Built-in schemas with versioning loaded successfully');
    } catch (error) {
      logger.error('Failed to load built-in schemas', { error });
      throw new Error('Schema initialization failed');
    }
  }

  private calculateChecksum(schema: object): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(JSON.stringify(schema)).digest('hex');
  }

  registerSchema(schemaId: string, schema: object): void {
    try {
      this.ajv.addSchema(schema, schemaId);
      this.schemas.set(schemaId, schema);
      logger.debug(`Schema registered: ${schemaId}`);
    } catch (error) {
      logger.error(`Failed to register schema: ${schemaId}`, { error });
      throw new Error(`Schema registration failed: ${schemaId}`);
    }
  }

  validateContract(data: unknown, schemaId: string): ValidationResult {
    const startTime = process.hrtime.bigint();
    
    try {
      const validate = this.ajv.getSchema(schemaId);
      if (!validate) {
        return {
          valid: false,
          errors: [`Schema not found: ${schemaId}`],
        };
      }

      const valid = validate(data);
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds

      logger.debug('Contract validation completed', {
        schemaId,
        valid,
        duration: `${duration.toFixed(2)}ms`,
      });

      if (valid) {
        return { valid: true };
      }

      const errors = validate.errors?.map(error => {
        const path = error.instancePath ? `${error.instancePath}: ` : '';
        return `${path}${error.message}`;
      }) || ['Unknown validation error'];

      return {
        valid: false,
        errors,
      };
    } catch (error) {
      logger.error('Contract validation error', { schemaId, error });
      return {
        valid: false,
        errors: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`],
      };
    }
  }

  getSchema(schemaId: string): object | undefined {
    return this.schemas.get(schemaId);
  }

  listSchemas(): string[] {
    return Array.from(this.schemas.keys());
  }

  validateAgentContract(agentData: unknown): ValidationResult {
    return this.validateContract(agentData, 'agent');
  }

  validateMCPContract(mcpData: unknown): ValidationResult {
    return this.validateContract(mcpData, 'mcp');
  }
}

export const contractValidator = new ContractValidator();