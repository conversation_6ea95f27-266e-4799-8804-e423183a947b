import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface SessionConfig {
  maxSessions: number;
  sessionTimeout: number; // in milliseconds
  cleanupInterval: number; // in milliseconds
  persistSessions: boolean;
}

export interface SessionMetadata {
  userId?: string;
  clientId: string;
  userAgent?: string;
  ipAddress?: string;
  createdAt: Date;
  lastAccessedAt: Date;
  expiresAt: Date;
  tags?: Record<string, string>;
}

export interface SessionContext {
  conversationHistory: ConversationMessage[];
  modelPreferences: ModelPreferences;
  customData: Record<string, any>;
  permissions: SessionPermissions;
  resourceUsage: ResourceUsage;
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    tokens?: number;
    model?: string;
    latency?: number;
  };
}

export interface ModelPreferences {
  preferredProvider: string;
  modelName: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  stopSequences: string[];
  customParameters?: Record<string, any>;
}

export interface SessionPermissions {
  allowedModels: string[];
  allowedProviders: string[];
  maxRequestsPerMinute: number;
  maxTokensPerRequest: number;
  maxTokensPerDay: number;
  allowStreaming: boolean;
  allowFileUpload: boolean;
}

export interface ResourceUsage {
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  tokensUsedToday: number;
  requestsThisMinute: number;
  lastRequestTime?: Date;
  averageLatency: number;
}

export interface Session {
  id: string;
  metadata: SessionMetadata;
  context: SessionContext;
  status: SessionStatus;
}

export enum SessionStatus {
  ACTIVE = 'active',
  IDLE = 'idle',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  TERMINATED = 'terminated',
}

export interface CreateSessionRequest {
  clientId: string;
  userId?: string;
  preferences?: Partial<ModelPreferences>;
  permissions?: Partial<SessionPermissions>;
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    tags?: Record<string, string>;
  };
}

export interface UpdateSessionRequest {
  preferences?: Partial<ModelPreferences>;
  customData?: Record<string, any>;
  metadata?: {
    tags?: Record<string, string>;
  };
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
  averageSessionDuration: number;
  totalTokensProcessed: number;
  totalCost: number;
}

export class SessionManager {
  private sessions: Map<string, Session> = new Map();
  private config: SessionConfig;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config?: Partial<SessionConfig>) {
    this.config = {
      maxSessions: 10000,
      sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
      cleanupInterval: 60 * 1000, // 1 minute
      persistSessions: false,
      ...config,
    };

    this.startCleanupTimer();
    logger.info('SessionManager initialized', { config: this.config });
  }

  async createSession(request: CreateSessionRequest): Promise<Session> {
    // Check session limits
    if (this.sessions.size >= this.config.maxSessions) {
      // Try to clean up expired sessions first
      await this.cleanupExpiredSessions();
      
      if (this.sessions.size >= this.config.maxSessions) {
        throw new Error('Maximum number of sessions reached');
      }
    }

    const sessionId = crypto.randomUUID();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.config.sessionTimeout);

    const defaultPreferences: ModelPreferences = {
      preferredProvider: 'ollama',
      modelName: 'llama2',
      temperature: 0.7,
      maxTokens: 2048,
      topP: 0.9,
      frequencyPenalty: 0,
      presencePenalty: 0,
      stopSequences: [],
    };

    const defaultPermissions: SessionPermissions = {
      allowedModels: ['llama2', 'codellama', 'mistral'],
      allowedProviders: ['ollama', 'openai', 'anthropic'],
      maxRequestsPerMinute: 60,
      maxTokensPerRequest: 4096,
      maxTokensPerDay: 100000,
      allowStreaming: true,
      allowFileUpload: false,
    };

    const session: Session = {
      id: sessionId,
      metadata: {
        userId: request.userId,
        clientId: request.clientId,
        userAgent: request.metadata?.userAgent,
        ipAddress: request.metadata?.ipAddress,
        createdAt: now,
        lastAccessedAt: now,
        expiresAt,
        tags: request.metadata?.tags || {},
      },
      context: {
        conversationHistory: [],
        modelPreferences: { ...defaultPreferences, ...request.preferences },
        customData: {},
        permissions: { ...defaultPermissions, ...request.permissions },
        resourceUsage: {
          totalRequests: 0,
          totalTokens: 0,
          totalCost: 0,
          tokensUsedToday: 0,
          requestsThisMinute: 0,
          averageLatency: 0,
        },
      },
      status: SessionStatus.ACTIVE,
    };

    this.sessions.set(sessionId, session);
    
    applicationMetrics.recordCounter('sessions_created_total', 1, {
      client_id: request.clientId,
      user_id: request.userId || 'anonymous',
    });

    logger.info('Session created', {
      sessionId,
      clientId: request.clientId,
      userId: request.userId,
    });

    return session;
  }

  async getSession(sessionId: string): Promise<Session | null> {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      return null;
    }

    // Check if session is expired
    if (this.isSessionExpired(session)) {
      session.status = SessionStatus.EXPIRED;
      await this.destroySession(sessionId);
      return null;
    }

    // Update last accessed time
    session.metadata.lastAccessedAt = new Date();
    session.status = SessionStatus.ACTIVE;

    return session;
  }

  async updateSession(sessionId: string, updates: UpdateSessionRequest): Promise<Session | null> {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      return null;
    }

    // Update preferences
    if (updates.preferences) {
      session.context.modelPreferences = {
        ...session.context.modelPreferences,
        ...updates.preferences,
      };
    }

    // Update custom data
    if (updates.customData) {
      session.context.customData = {
        ...session.context.customData,
        ...updates.customData,
      };
    }

    // Update metadata tags
    if (updates.metadata?.tags) {
      session.metadata.tags = {
        ...session.metadata.tags,
        ...updates.metadata.tags,
      };
    }

    logger.debug('Session updated', { sessionId, updates });
    
    return session;
  }

  async destroySession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    
    if (session) {
      session.status = SessionStatus.TERMINATED;
      this.sessions.delete(sessionId);
      
      applicationMetrics.recordCounter('sessions_destroyed_total', 1, {
        client_id: session.metadata.clientId,
        reason: 'manual',
      });

      logger.info('Session destroyed', { sessionId });
    }
  }

  async addMessage(sessionId: string, message: Omit<ConversationMessage, 'id' | 'timestamp'>): Promise<void> {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new Error('Session not found');
    }

    const conversationMessage: ConversationMessage = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      ...message,
    };

    session.context.conversationHistory.push(conversationMessage);
    
    // Update resource usage
    if (message.metadata?.tokens) {
      session.context.resourceUsage.totalTokens += message.metadata.tokens;
      session.context.resourceUsage.tokensUsedToday += message.metadata.tokens;
    }

    if (message.metadata?.latency) {
      const currentLatency = session.context.resourceUsage.averageLatency;
      const totalRequests = session.context.resourceUsage.totalRequests;
      session.context.resourceUsage.averageLatency = 
        (currentLatency * totalRequests + message.metadata.latency) / (totalRequests + 1);
    }

    session.context.resourceUsage.totalRequests++;
    session.context.resourceUsage.lastRequestTime = new Date();

    logger.debug('Message added to session', {
      sessionId,
      messageId: conversationMessage.id,
      role: message.role,
      tokens: message.metadata?.tokens,
    });
  }

  async getConversationHistory(sessionId: string, limit?: number): Promise<ConversationMessage[]> {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      return [];
    }

    const history = session.context.conversationHistory;
    
    if (limit && limit > 0) {
      return history.slice(-limit);
    }

    return history;
  }

  async checkRateLimit(sessionId: string): Promise<boolean> {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      return false;
    }

    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    
    // Reset counter if last request was more than a minute ago
    if (!session.context.resourceUsage.lastRequestTime || 
        session.context.resourceUsage.lastRequestTime < oneMinuteAgo) {
      session.context.resourceUsage.requestsThisMinute = 0;
    }

    const withinLimit = session.context.resourceUsage.requestsThisMinute < 
                       session.context.permissions.maxRequestsPerMinute;

    if (withinLimit) {
      session.context.resourceUsage.requestsThisMinute++;
    }

    return withinLimit;
  }

  async getSessionStats(): Promise<SessionStats> {
    const sessions = Array.from(this.sessions.values());
    const activeSessions = sessions.filter(s => s.status === SessionStatus.ACTIVE).length;
    const expiredSessions = sessions.filter(s => s.status === SessionStatus.EXPIRED).length;
    
    const totalTokens = sessions.reduce((sum, s) => sum + s.context.resourceUsage.totalTokens, 0);
    const totalCost = sessions.reduce((sum, s) => sum + s.context.resourceUsage.totalCost, 0);
    
    const sessionDurations = sessions.map(s => 
      s.metadata.lastAccessedAt.getTime() - s.metadata.createdAt.getTime()
    );
    const averageSessionDuration = sessionDurations.length > 0 
      ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length
      : 0;

    return {
      totalSessions: sessions.length,
      activeSessions,
      expiredSessions,
      averageSessionDuration,
      totalTokensProcessed: totalTokens,
      totalCost,
    };
  }

  private isSessionExpired(session: Session): boolean {
    return new Date() > session.metadata.expiresAt;
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const expiredSessions: string[] = [];
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (this.isSessionExpired(session)) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      const session = this.sessions.get(sessionId);
      if (session) {
        session.status = SessionStatus.EXPIRED;
        this.sessions.delete(sessionId);
        
        applicationMetrics.recordCounter('sessions_destroyed_total', 1, {
          client_id: session.metadata.clientId,
          reason: 'expired',
        });
      }
    }

    if (expiredSessions.length > 0) {
      logger.info('Cleaned up expired sessions', { count: expiredSessions.length });
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredSessions().catch(error => {
        logger.error('Error during session cleanup', { error });
      });
    }, this.config.cleanupInterval);
  }

  async shutdown(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    // If persistence is enabled, save sessions to storage
    if (this.config.persistSessions) {
      // Implement session persistence logic here
      logger.info('Session persistence not implemented yet');
    }

    logger.info('SessionManager shutdown complete');
  }
}