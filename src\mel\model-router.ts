import { EventEmitter } from 'events';
import { ModelProvider, TextGenerationRequest, ModelInfo, ProviderHealth, CostEstimation } from './provider-interface';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface RoutingPolicy {
  id: string;
  name: string;
  enabled: boolean;
  priority: number;
  conditions: RoutingCondition[];
  action: RoutingAction;
  budget?: BudgetConstraints;
}

export interface RoutingCondition {
  type: ConditionType;
  field: string;
  operator: string;
  value: any;
}

export enum ConditionType {
  MODEL_NAME = 'model_name',
  PROVIDER_TYPE = 'provider_type',
  REQUEST_SIZE = 'request_size',
  USER_TIER = 'user_tier',
  TIME_OF_DAY = 'time_of_day',
  COST_THRESHOLD = 'cost_threshold',
  LATENCY_REQUIREMENT = 'latency_requirement',
  QUALITY_REQUIREMENT = 'quality_requirement',
}

export interface RoutingAction {
  type: ActionType;
  targetProviders: string[];
  fallbackProviders?: string[];
  loadBalancing: LoadBalancingStrategy;
  retryPolicy?: RetryPolicy;
}

export enum ActionType {
  ROUTE_TO_PROVIDER = 'route_to_provider',
  LOAD_BALANCE = 'load_balance',
  REJECT_REQUEST = 'reject_request',
  QUEUE_REQUEST = 'queue_request',
}

export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  LEAST_LATENCY = 'least_latency',
  LEAST_COST = 'least_cost',
  QUALITY_WEIGHTED = 'quality_weighted',
  CUSTOM = 'custom',
}

export interface RetryPolicy {
  maxAttempts: number;
  backoffStrategy: BackoffStrategy;
  retryableErrors: string[];
  timeout: number;
}

export enum BackoffStrategy {
  FIXED = 'fixed',
  LINEAR = 'linear',
  EXPONENTIAL = 'exponential',
  JITTER = 'jitter',
}

export interface BudgetConstraints {
  dailyBudget?: number;
  monthlyBudget?: number;
  maxCostPerRequest?: number;
  budgetAllocation?: Record<string, number>; // provider -> allocation percentage
  alertThresholds?: {
    warning: number; // percentage
    critical: number; // percentage
  };
}

export interface RoutingDecision {
  providerId: string;
  providerName: string;
  reason: string;
  confidence: number;
  estimatedCost: number;
  estimatedLatency: number;
  appliedPolicies: string[];
  fallbackProviders: string[];
  metadata?: Record<string, any>;
}

export interface ProviderScore {
  providerId: string;
  score: number;
  factors: {
    availability: number;
    latency: number;
    cost: number;
    quality: number;
    capacity: number;
  };
  metadata?: Record<string, any>;
}

export interface BudgetStatus {
  totalBudget: number;
  usedBudget: number;
  remainingBudget: number;
  percentage: number;
  period: 'daily' | 'monthly';
  resetDate: Date;
  providerBreakdown: Record<string, {
    allocated: number;
    used: number;
    remaining: number;
  }>;
}

export interface RouterConfig {
  defaultProvider?: string;
  enableLoadBalancing: boolean;
  healthCheckInterval: number;
  budgetTrackingEnabled: boolean;
  qualityMetricsEnabled: boolean;
  costOptimizationEnabled: boolean;
  maxConcurrentRequests: number;
  requestQueueSize: number;
}

export class ModelRouter extends EventEmitter {
  private providers: Map<string, ModelProvider> = new Map();
  private policies: Map<string, RoutingPolicy> = new Map();
  private providerHealth: Map<string, ProviderHealth> = new Map();
  private loadBalancingState: Map<string, any> = new Map();
  private budgetTracking: Map<string, BudgetStatus> = new Map();
  private requestQueue: RequestQueueItem[] = [];
  private activeRequests: Set<string> = new Set();
  private config: RouterConfig;

  constructor(config?: Partial<RouterConfig>) {
    super();
    this.config = {
      enableLoadBalancing: true,
      healthCheckInterval: 30000, // 30 seconds
      budgetTrackingEnabled: true,
      qualityMetricsEnabled: true,
      costOptimizationEnabled: true,
      maxConcurrentRequests: 100,
      requestQueueSize: 1000,
      ...config,
    };

    this.initializeDefaultPolicies();
    this.startHealthChecking();

    logger.info('ModelRouter initialized', { config: this.config });
  }

  private initializeDefaultPolicies(): void {
    // High-priority requests route to fastest providers
    this.addPolicy({
      id: 'high-priority-routing',
      name: 'High Priority Request Routing',
      enabled: true,
      priority: 10,
      conditions: [
        {
          type: ConditionType.USER_TIER,
          field: 'tier',
          operator: 'equals',
          value: 'premium',
        },
      ],
      action: {
        type: ActionType.ROUTE_TO_PROVIDER,
        targetProviders: [],
        loadBalancing: LoadBalancingStrategy.LEAST_LATENCY,
      },
    });

    // Cost-sensitive routing
    this.addPolicy({
      id: 'cost-optimization',
      name: 'Cost Optimization Routing',
      enabled: true,
      priority: 20,
      conditions: [
        {
          type: ConditionType.COST_THRESHOLD,
          field: 'maxCost',
          operator: 'less_than',
          value: 0.01,
        },
      ],
      action: {
        type: ActionType.LOAD_BALANCE,
        targetProviders: [],
        loadBalancing: LoadBalancingStrategy.LEAST_COST,
      },
      budget: {
        maxCostPerRequest: 0.01,
      },
    });

    // Large request routing
    this.addPolicy({
      id: 'large-request-routing',
      name: 'Large Request Handling',
      enabled: true,
      priority: 30,
      conditions: [
        {
          type: ConditionType.REQUEST_SIZE,
          field: 'tokens',
          operator: 'greater_than',
          value: 10000,
        },
      ],
      action: {
        type: ActionType.ROUTE_TO_PROVIDER,
        targetProviders: [],
        loadBalancing: LoadBalancingStrategy.QUALITY_WEIGHTED,
      },
    });
  }

  async route(request: TextGenerationRequest, context?: RoutingContext): Promise<RoutingDecision> {
    const startTime = Date.now();

    try {
      // Check if we're at capacity
      if (this.activeRequests.size >= this.config.maxConcurrentRequests) {
        if (this.requestQueue.length >= this.config.requestQueueSize) {
          throw new Error('Request queue full');
        }

        return this.queueRequest(request, context);
      }

      // Evaluate routing policies
      const applicablePolicies = await this.evaluatePolicies(request, context);
      
      // Get available providers
      const availableProviders = await this.getAvailableProviders(request.model);
      
      if (availableProviders.length === 0) {
        throw new Error('No available providers for the requested model');
      }

      // Score providers based on policies and current state
      const providerScores = await this.scoreProviders(availableProviders, request, applicablePolicies, context);
      
      // Select best provider
      const selectedProvider = this.selectProvider(providerScores, applicablePolicies);
      
      // Check budget constraints
      const budgetCheck = await this.checkBudgetConstraints(selectedProvider.providerId, request, context);
      if (!budgetCheck.allowed) {
        throw new Error(`Budget constraint violated: ${budgetCheck.reason}`);
      }

      // Get cost and latency estimates
      const provider = this.providers.get(selectedProvider.providerId)!;
      const costEstimate = await provider.estimateCost({
        model: request.model,
        estimatedInputTokens: this.estimateInputTokens(request),
        estimatedOutputTokens: request.parameters.maxTokens,
        operationType: 'generation',
      });

      const decision: RoutingDecision = {
        providerId: selectedProvider.providerId,
        providerName: provider.name,
        reason: this.buildRoutingReason(applicablePolicies, selectedProvider),
        confidence: selectedProvider.score,
        estimatedCost: costEstimate.estimatedCost,
        estimatedLatency: this.estimateLatency(selectedProvider.providerId, request),
        appliedPolicies: applicablePolicies.map(p => p.id),
        fallbackProviders: this.getFallbackProviders(providerScores, selectedProvider.providerId),
      };

      // Record metrics
      const routingTime = Date.now() - startTime;
      applicationMetrics.recordHistogram('model_routing_duration_ms', routingTime, {
        selected_provider: selectedProvider.providerId,
        model: request.model,
      });

      applicationMetrics.recordCounter('routing_decisions_total', 1, {
        provider: selectedProvider.providerId,
        model: request.model,
        user_tier: context?.userTier || 'unknown',
      });

      logger.debug('Routing decision made', {
        decision,
        routingTime,
        availableProviders: availableProviders.length,
        appliedPolicies: applicablePolicies.length,
      });

      return decision;

    } catch (error) {
      applicationMetrics.recordCounter('routing_errors_total', 1, {
        model: request.model,
        error_type: error instanceof Error ? error.constructor.name : 'UnknownError',
      });

      logger.error('Routing decision failed', {
        error,
        model: request.model,
        context,
      });

      throw error;
    }
  }

  private async evaluatePolicies(request: TextGenerationRequest, context?: RoutingContext): Promise<RoutingPolicy[]> {
    const applicablePolicies: RoutingPolicy[] = [];

    const sortedPolicies = Array.from(this.policies.values())
      .filter(policy => policy.enabled)
      .sort((a, b) => a.priority - b.priority);

    for (const policy of sortedPolicies) {
      try {
        const matches = await this.evaluatePolicy(policy, request, context);
        if (matches) {
          applicablePolicies.push(policy);
        }
      } catch (error) {
        logger.warn('Policy evaluation error', {
          policyId: policy.id,
          error,
        });
      }
    }

    return applicablePolicies;
  }

  private async evaluatePolicy(policy: RoutingPolicy, request: TextGenerationRequest, context?: RoutingContext): Promise<boolean> {
    // All conditions must be true for policy to apply
    for (const condition of policy.conditions) {
      const result = await this.evaluateCondition(condition, request, context);
      if (!result) {
        return false;
      }
    }
    return true;
  }

  private async evaluateCondition(condition: RoutingCondition, request: TextGenerationRequest, context?: RoutingContext): Promise<boolean> {
    let value: any;

    switch (condition.type) {
      case ConditionType.MODEL_NAME:
        value = request.model;
        break;
      case ConditionType.REQUEST_SIZE:
        value = this.estimateInputTokens(request) + request.parameters.maxTokens;
        break;
      case ConditionType.USER_TIER:
        value = context?.userTier;
        break;
      case ConditionType.TIME_OF_DAY:
        value = new Date().getHours();
        break;
      case ConditionType.COST_THRESHOLD:
        value = context?.maxCost;
        break;
      case ConditionType.LATENCY_REQUIREMENT:
        value = context?.maxLatency;
        break;
      default:
        return false;
    }

    return this.applyConditionOperator(value, condition.operator, condition.value);
  }

  private applyConditionOperator(value: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'equals':
        return value === expectedValue;
      case 'not_equals':
        return value !== expectedValue;
      case 'greater_than':
        return Number(value) > Number(expectedValue);
      case 'less_than':
        return Number(value) < Number(expectedValue);
      case 'contains':
        return String(value).includes(String(expectedValue));
      case 'in_list':
        return Array.isArray(expectedValue) && expectedValue.includes(value);
      default:
        return false;
    }
  }

  private async getAvailableProviders(modelName: string): Promise<string[]> {
    const availableProviders: string[] = [];

    for (const [providerId, provider] of this.providers.entries()) {
      try {
        const isHealthy = await provider.isHealthy();
        if (!isHealthy) continue;

        const models = await provider.listModels();
        const hasModel = models.some(model => model.name === modelName || model.id === modelName);
        
        if (hasModel) {
          availableProviders.push(providerId);
        }
      } catch (error) {
        logger.warn('Provider availability check failed', {
          providerId,
          error,
        });
      }
    }

    return availableProviders;
  }

  private async scoreProviders(
    providerIds: string[], 
    request: TextGenerationRequest, 
    policies: RoutingPolicy[], 
    context?: RoutingContext
  ): Promise<ProviderScore[]> {
    const scores: ProviderScore[] = [];

    for (const providerId of providerIds) {
      try {
        const score = await this.calculateProviderScore(providerId, request, policies, context);
        scores.push(score);
      } catch (error) {
        logger.warn('Provider scoring failed', {
          providerId,
          error,
        });
      }
    }

    return scores.sort((a, b) => b.score - a.score);
  }

  private async calculateProviderScore(
    providerId: string, 
    request: TextGenerationRequest, 
    policies: RoutingPolicy[], 
    context?: RoutingContext
  ): Promise<ProviderScore> {
    const provider = this.providers.get(providerId)!;
    
    // Base factors
    const availability = await this.getAvailabilityScore(providerId);
    const latency = await this.getLatencyScore(providerId);
    const cost = await this.getCostScore(providerId, request);
    const quality = await this.getQualityScore(providerId, request.model);
    const capacity = await this.getCapacityScore(providerId);

    // Apply policy weights
    const weights = this.calculatePolicyWeights(policies);
    
    const weightedScore = 
      availability * weights.availability +
      latency * weights.latency +
      cost * weights.cost +
      quality * weights.quality +
      capacity * weights.capacity;

    return {
      providerId,
      score: Math.max(0, Math.min(1, weightedScore)),
      factors: { availability, latency, cost, quality, capacity },
    };
  }

  private calculatePolicyWeights(policies: RoutingPolicy[]): Record<string, number> {
    // Default weights
    const weights = {
      availability: 0.3,
      latency: 0.25,
      cost: 0.2,
      quality: 0.15,
      capacity: 0.1,
    };

    // Adjust weights based on policies
    for (const policy of policies) {
      if (policy.action.loadBalancing === LoadBalancingStrategy.LEAST_LATENCY) {
        weights.latency += 0.2;
        weights.cost -= 0.1;
      } else if (policy.action.loadBalancing === LoadBalancingStrategy.LEAST_COST) {
        weights.cost += 0.2;
        weights.latency -= 0.1;
      } else if (policy.action.loadBalancing === LoadBalancingStrategy.QUALITY_WEIGHTED) {
        weights.quality += 0.2;
        weights.cost -= 0.1;
      }
    }

    // Normalize weights
    const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    Object.keys(weights).forEach(key => {
      weights[key as keyof typeof weights] /= total;
    });

    return weights;
  }

  private async getAvailabilityScore(providerId: string): Promise<number> {
    const health = this.providerHealth.get(providerId);
    if (!health) return 0;

    switch (health.status) {
      case 'healthy': return 1.0;
      case 'degraded': return 0.6;
      case 'unhealthy': return 0.1;
      default: return 0;
    }
  }

  private async getLatencyScore(providerId: string): Promise<number> {
    // Mock implementation - in real system, use historical latency data
    const provider = this.providers.get(providerId)!;
    const metrics = provider.getMetrics();
    const avgLatency = metrics.averageLatency || 1000;
    
    // Score inversely proportional to latency (lower latency = higher score)
    return Math.max(0, 1 - (avgLatency / 5000)); // Normalize to 5 second max
  }

  private async getCostScore(providerId: string, request: TextGenerationRequest): Promise<number> {
    try {
      const provider = this.providers.get(providerId)!;
      const costEstimate = await provider.estimateCost({
        model: request.model,
        estimatedInputTokens: this.estimateInputTokens(request),
        estimatedOutputTokens: request.parameters.maxTokens,
        operationType: 'generation',
      });

      // Score inversely proportional to cost (lower cost = higher score)
      return Math.max(0, 1 - (costEstimate.estimatedCost / 1.0)); // Normalize to $1 max
    } catch {
      return 0.5; // Default score if cost estimation fails
    }
  }

  private async getQualityScore(providerId: string, modelName: string): Promise<number> {
    // Mock implementation - in real system, use quality metrics
    const provider = this.providers.get(providerId)!;
    const models = await provider.listModels();
    const model = models.find(m => m.name === modelName || m.id === modelName);
    
    if (!model) return 0;
    
    // Use context length as a proxy for model quality
    return Math.min(1.0, model.contextLength / 32000); // Normalize to 32k context
  }

  private async getCapacityScore(providerId: string): Promise<number> {
    try {
      const provider = this.providers.get(providerId)!;
      const usage = await provider.getResourceUsage();
      
      // Score based on available capacity
      const cpuScore = 1 - (usage.cpu.usage / 100);
      const memoryScore = 1 - (usage.memory.percentage / 100);
      
      return (cpuScore + memoryScore) / 2;
    } catch {
      return 0.5; // Default score if resource usage check fails
    }
  }

  private selectProvider(scores: ProviderScore[], policies: RoutingPolicy[]): ProviderScore {
    if (scores.length === 0) {
      throw new Error('No providers available for selection');
    }

    // Get load balancing strategy from policies
    const strategy = policies.find(p => p.action.loadBalancing)?.action.loadBalancing || LoadBalancingStrategy.QUALITY_WEIGHTED;

    switch (strategy) {
      case LoadBalancingStrategy.ROUND_ROBIN:
        return this.selectRoundRobin(scores);
      case LoadBalancingStrategy.LEAST_CONNECTIONS:
        return this.selectLeastConnections(scores);
      default:
        return scores[0]; // Highest scored provider
    }
  }

  private selectRoundRobin(scores: ProviderScore[]): ProviderScore {
    const key = 'round_robin_index';
    const currentIndex = this.loadBalancingState.get(key) || 0;
    const nextIndex = (currentIndex + 1) % scores.length;
    this.loadBalancingState.set(key, nextIndex);
    return scores[currentIndex];
  }

  private selectLeastConnections(scores: ProviderScore[]): ProviderScore {
    // Select provider with least active connections
    let minConnections = Infinity;
    let selectedProvider = scores[0];

    for (const score of scores) {
      const connections = Array.from(this.activeRequests).filter(req => req.startsWith(score.providerId)).length;
      if (connections < minConnections) {
        minConnections = connections;
        selectedProvider = score;
      }
    }

    return selectedProvider;
  }

  private async checkBudgetConstraints(providerId: string, request: TextGenerationRequest, context?: RoutingContext): Promise<{ allowed: boolean; reason?: string }> {
    if (!this.config.budgetTrackingEnabled) {
      return { allowed: true };
    }

    const provider = this.providers.get(providerId)!;
    const costEstimate = await provider.estimateCost({
      model: request.model,
      estimatedInputTokens: this.estimateInputTokens(request),
      estimatedOutputTokens: request.parameters.maxTokens,
      operationType: 'generation',
    });

    // Check per-request budget
    if (context?.maxCost && costEstimate.estimatedCost > context.maxCost) {
      return {
        allowed: false,
        reason: `Request cost (${costEstimate.estimatedCost}) exceeds maximum allowed (${context.maxCost})`,
      };
    }

    // Check daily/monthly budgets
    const budgetStatus = this.budgetTracking.get('daily');
    if (budgetStatus) {
      if (budgetStatus.usedBudget + costEstimate.estimatedCost > budgetStatus.totalBudget) {
        return {
          allowed: false,
          reason: 'Daily budget would be exceeded',
        };
      }
    }

    return { allowed: true };
  }

  private estimateInputTokens(request: TextGenerationRequest): number {
    // Simple estimation - in real system, use tokenizer
    const promptLength = request.prompt?.length || 0;
    const systemPromptLength = request.systemPrompt?.length || 0;
    const messagesLength = request.messages?.reduce((sum, msg) => sum + msg.content.length, 0) || 0;
    
    return Math.ceil((promptLength + systemPromptLength + messagesLength) / 4); // Rough approximation
  }

  private estimateLatency(providerId: string, request: TextGenerationRequest): number {
    const provider = this.providers.get(providerId)!;
    const metrics = provider.getMetrics();
    const baseLatency = metrics.averageLatency || 1000;
    
    // Adjust based on request size
    const inputTokens = this.estimateInputTokens(request);
    const outputTokens = request.parameters.maxTokens;
    const sizeMultiplier = 1 + ((inputTokens + outputTokens) / 10000); // Increase by 1% per 100 tokens
    
    return baseLatency * sizeMultiplier;
  }

  private buildRoutingReason(policies: RoutingPolicy[], selectedProvider: ProviderScore): string {
    const reasons: string[] = [];
    
    if (policies.length > 0) {
      reasons.push(`Applied policies: ${policies.map(p => p.name).join(', ')}`);
    }
    
    reasons.push(`Selected based on score: ${selectedProvider.score.toFixed(3)}`);
    
    const topFactors = Object.entries(selectedProvider.factors)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 2)
      .map(([factor, score]) => `${factor}:${score.toFixed(2)}`);
    
    reasons.push(`Top factors: ${topFactors.join(', ')}`);
    
    return reasons.join(' | ');
  }

  private getFallbackProviders(scores: ProviderScore[], selectedProviderId: string): string[] {
    return scores
      .filter(score => score.providerId !== selectedProviderId)
      .slice(0, 3)
      .map(score => score.providerId);
  }

  private queueRequest(request: TextGenerationRequest, context?: RoutingContext): RoutingDecision {
    const queueItem: RequestQueueItem = {
      id: crypto.randomUUID(),
      request,
      context,
      timestamp: new Date(),
    };

    this.requestQueue.push(queueItem);

    return {
      providerId: 'queue',
      providerName: 'Request Queue',
      reason: 'Request queued due to capacity limits',
      confidence: 0,
      estimatedCost: 0,
      estimatedLatency: 30000, // 30 seconds estimated queue time
      appliedPolicies: [],
      fallbackProviders: [],
      metadata: { queuePosition: this.requestQueue.length },
    };
  }

  private startHealthChecking(): void {
    setInterval(async () => {
      for (const [providerId, provider] of this.providers.entries()) {
        try {
          const health = await provider.isHealthy();
          this.providerHealth.set(providerId, {
            status: health ? 'healthy' : 'unhealthy',
            lastCheck: new Date(),
          });
        } catch (error) {
          this.providerHealth.set(providerId, {
            status: 'unhealthy',
            lastCheck: new Date(),
            errors: [error instanceof Error ? error.message : 'Health check failed'],
          });
        }
      }
    }, this.config.healthCheckInterval);
  }

  // Public management methods
  addProvider(providerId: string, provider: ModelProvider): void {
    this.providers.set(providerId, provider);
    this.providerHealth.set(providerId, {
      status: 'unknown',
      lastCheck: new Date(),
    });
    
    logger.info('Provider added to router', { providerId });
  }

  removeProvider(providerId: string): boolean {
    const removed = this.providers.delete(providerId);
    this.providerHealth.delete(providerId);
    
    if (removed) {
      logger.info('Provider removed from router', { providerId });
    }
    
    return removed;
  }

  addPolicy(policy: RoutingPolicy): void {
    this.policies.set(policy.id, policy);
    logger.info('Routing policy added', { policyId: policy.id, policyName: policy.name });
  }

  removePolicy(policyId: string): boolean {
    const removed = this.policies.delete(policyId);
    if (removed) {
      logger.info('Routing policy removed', { policyId });
    }
    return removed;
  }

  getProviderHealth(): Map<string, ProviderHealth> {
    return new Map(this.providerHealth);
  }

  getBudgetStatus(): Map<string, BudgetStatus> {
    return new Map(this.budgetTracking);
  }
}

interface RoutingContext {
  userId?: string;
  sessionId?: string;
  userTier?: string;
  maxCost?: number;
  maxLatency?: number;
  qualityRequirement?: number;
  metadata?: Record<string, any>;
}

interface RequestQueueItem {
  id: string;
  request: TextGenerationRequest;
  context?: RoutingContext;
  timestamp: Date;
}