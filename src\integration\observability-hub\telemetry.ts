import { NodeSDK } from '@opentelemetry/sdk-node';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { JaegerExporter } from '@opentelemetry/exporter-jaeger';
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus';
import { logger } from '@/utils/logger';

export interface TelemetryConfig {
  serviceName: string;
  serviceVersion: string;
  environment: string;
  jaegerEndpoint?: string;
  prometheusPort?: number;
  traceEnabled: boolean;
  metricsEnabled: boolean;
}

export class ObservabilityHub {
  private sdk: NodeSDK;
  private config: TelemetryConfig;
  private prometheusExporter?: PrometheusExporter;

  constructor(config: TelemetryConfig) {
    this.config = config;
    this.initializeSDK();
  }

  private initializeSDK(): void {
    const resource = new Resource({
      [SemanticResourceAttributes.SERVICE_NAME]: this.config.serviceName,
      [SemanticResourceAttributes.SERVICE_VERSION]: this.config.serviceVersion,
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: this.config.environment,
    });

    const instrumentations = getNodeAutoInstrumentations({
      '@opentelemetry/instrumentation-fs': {
        enabled: false, // Disable file system instrumentation for performance
      },
    });

    const exporters = this.setupExporters();

    this.sdk = new NodeSDK({
      resource,
      instrumentations,
      traceExporter: exporters.traceExporter,
      metricReader: exporters.metricReader,
    });

    logger.info('OpenTelemetry SDK initialized', {
      serviceName: this.config.serviceName,
      environment: this.config.environment,
    });
  }

  private setupExporters() {
    const exporters: {
      traceExporter?: JaegerExporter;
      metricReader?: PrometheusExporter;
    } = {};

    // Setup trace exporter
    if (this.config.traceEnabled && this.config.jaegerEndpoint) {
      exporters.traceExporter = new JaegerExporter({
        endpoint: this.config.jaegerEndpoint,
      });
      logger.info('Jaeger trace exporter configured', {
        endpoint: this.config.jaegerEndpoint,
      });
    }

    // Setup metrics exporter
    if (this.config.metricsEnabled) {
      this.prometheusExporter = new PrometheusExporter({
        port: this.config.prometheusPort || 9464,
        preventServerStart: false,
      });
      exporters.metricReader = this.prometheusExporter;
      logger.info('Prometheus metrics exporter configured', {
        port: this.config.prometheusPort || 9464,
      });
    }

    return exporters;
  }

  async start(): Promise<void> {
    try {
      await this.sdk.start();
      logger.info('OpenTelemetry SDK started successfully');
    } catch (error) {
      logger.error('Failed to start OpenTelemetry SDK', { error });
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    try {
      await this.sdk.shutdown();
      logger.info('OpenTelemetry SDK shutdown successfully');
    } catch (error) {
      logger.error('Failed to shutdown OpenTelemetry SDK', { error });
      throw error;
    }
  }

  getMetricsEndpoint(): string | undefined {
    if (this.prometheusExporter) {
      return `http://localhost:${this.config.prometheusPort || 9464}/metrics`;
    }
    return undefined;
  }
}

// Factory function to create observability hub
export function createObservabilityHub(config?: Partial<TelemetryConfig>): ObservabilityHub {
  const defaultConfig: TelemetryConfig = {
    serviceName: process.env.SERVICE_NAME || 'custom-agent-system',
    serviceVersion: process.env.SERVICE_VERSION || '2.0.0',
    environment: process.env.NODE_ENV || 'development',
    jaegerEndpoint: process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
    prometheusPort: parseInt(process.env.PROMETHEUS_PORT || '9464'),
    traceEnabled: process.env.TRACE_ENABLED !== 'false',
    metricsEnabled: process.env.METRICS_ENABLED !== 'false',
  };

  return new ObservabilityHub({ ...defaultConfig, ...config });
}