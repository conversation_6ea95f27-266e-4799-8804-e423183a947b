import { EventEmitter } from 'events';

export interface ModelProvider {
  readonly id: string;
  readonly name: string;
  readonly type: ProviderType;
  readonly capabilities: ProviderCapabilities;
  readonly config: ProviderConfig;
  
  initialize(): Promise<void>;
  shutdown(): Promise<void>;
  isHealthy(): Promise<boolean>;
  
  // Model management
  listModels(): Promise<ModelInfo[]>;
  getModel(modelId: string): Promise<ModelInfo | null>;
  loadModel(modelId: string): Promise<void>;
  unloadModel(modelId: string): Promise<void>;
  
  // Text generation
  generateText(request: TextGenerationRequest): Promise<TextGenerationResponse>;
  generateTextStream(request: TextGenerationRequest): AsyncIterable<TextGenerationChunk>;
  
  // Embeddings (optional)
  generateEmbeddings?(request: EmbeddingRequest): Promise<EmbeddingResponse>;
  
  // Fine-tuning (optional)
  createFineTuningJob?(request: FineTuningRequest): Promise<FineTuningJob>;
  getFineTuningJob?(jobId: string): Promise<FineTuningJob>;
  
  // Resource management
  getResourceUsage(): Promise<ResourceUsage>;
  estimateCost(request: CostEstimationRequest): Promise<CostEstimation>;
}

export enum ProviderType {
  LOCAL = 'local',
  CLOUD = 'cloud',
  HYBRID = 'hybrid',
}

export interface ProviderCapabilities {
  textGeneration: boolean;
  streaming: boolean;
  embeddings: boolean;
  fineTuning: boolean;
  multiModal: boolean;
  functionCalling: boolean;
  maxContextLength: number;
  supportedFormats: string[];
  customParameters: boolean;
}

export interface ProviderConfig {
  baseUrl?: string;
  apiKey?: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  rateLimits: {
    requestsPerMinute: number;
    tokensPerMinute: number;
    concurrent: number;
  };
  customHeaders?: Record<string, string>;
  proxy?: {
    url: string;
    auth?: { username: string; password: string };
  };
}

export interface ModelInfo {
  id: string;
  name: string;
  description: string;
  provider: string;
  version: string;
  contextLength: number;
  inputCost: number; // per 1K tokens
  outputCost: number; // per 1K tokens
  capabilities: string[];
  parameters?: Record<string, any>;
  metadata?: Record<string, any>;
  status: ModelStatus;
  lastUsed?: Date;
  loadTime?: number;
}

export enum ModelStatus {
  AVAILABLE = 'available',
  LOADING = 'loading',
  LOADED = 'loaded',
  UNLOADING = 'unloading',
  ERROR = 'error',
  UNAVAILABLE = 'unavailable',
}

export interface TextGenerationRequest {
  model: string;
  prompt: string;
  systemPrompt?: string;
  messages?: ChatMessage[];
  parameters: GenerationParameters;
  constraints?: GenerationConstraints;
  metadata?: Record<string, any>;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  name?: string;
  functionCall?: FunctionCall;
}

export interface FunctionCall {
  name: string;
  arguments: string;
}

export interface GenerationParameters {
  maxTokens: number;
  temperature: number;
  topP: number;
  topK?: number;
  frequencyPenalty: number;
  presencePenalty: number;
  stopSequences: string[];
  seed?: number;
  logitBias?: Record<string, number>;
  stream?: boolean;
  customParameters?: Record<string, any>;
}

export interface GenerationConstraints {
  maxInputTokens?: number;
  maxOutputTokens?: number;
  timeoutMs?: number;
  requireJsonOutput?: boolean;
  jsonSchema?: object;
  bannedWords?: string[];
  requiredWords?: string[];
}

export interface TextGenerationResponse {
  id: string;
  model: string;
  content: string;
  finishReason: FinishReason;
  usage: TokenUsage;
  cost?: CostBreakdown;
  latency: number;
  metadata?: Record<string, any>;
  functionCalls?: FunctionCall[];
}

export interface TextGenerationChunk {
  id: string;
  model: string;
  delta: string;
  index: number;
  finishReason?: FinishReason;
  usage?: Partial<TokenUsage>;
  timestamp: number;
  metadata?: Record<string, any>;
}

export enum FinishReason {
  STOP = 'stop',
  LENGTH = 'length',
  FUNCTION_CALL = 'function_call',
  CONTENT_FILTER = 'content_filter',
  ERROR = 'error',
  TIMEOUT = 'timeout',
  CANCELLED = 'cancelled',
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  estimatedTokens?: number;
}

export interface CostBreakdown {
  inputCost: number;
  outputCost: number;
  totalCost: number;
  currency: string;
  billingModel: string;
}

export interface EmbeddingRequest {
  model: string;
  input: string | string[];
  dimensions?: number;
  metadata?: Record<string, any>;
}

export interface EmbeddingResponse {
  id: string;
  model: string;
  embeddings: number[][];
  usage: TokenUsage;
  cost?: CostBreakdown;
}

export interface FineTuningRequest {
  model: string;
  trainingData: string; // file path or data
  validationData?: string;
  hyperparameters?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface FineTuningJob {
  id: string;
  model: string;
  status: FineTuningStatus;
  createdAt: Date;
  finishedAt?: Date;
  trainingLoss?: number;
  validationLoss?: number;
  progress?: number;
  metadata?: Record<string, any>;
}

export enum FineTuningStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface ResourceUsage {
  cpu: {
    usage: number; // percentage
    cores: number;
  };
  memory: {
    used: number; // bytes
    total: number; // bytes
    percentage: number;
  };
  gpu?: {
    usage: number; // percentage
    memory: {
      used: number; // bytes
      total: number; // bytes
    };
    temperature?: number; // celsius
  };
  storage: {
    used: number; // bytes
    available: number; // bytes
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
}

export interface CostEstimationRequest {
  model: string;
  estimatedInputTokens: number;
  estimatedOutputTokens: number;
  operationType: 'generation' | 'embedding' | 'fine_tuning';
}

export interface CostEstimation {
  estimatedCost: number;
  currency: string;
  breakdown: {
    inputCost: number;
    outputCost: number;
    additionalCosts?: Record<string, number>;
  };
  confidence: number; // 0-1
}

export interface ProviderHealth {
  status: HealthStatus;
  latency?: number;
  lastCheck: Date;
  errors?: string[];
  metadata?: Record<string, any>;
}

export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown',
}

export interface ProviderMetrics {
  requestCount: number;
  successCount: number;
  errorCount: number;
  averageLatency: number;
  totalTokensProcessed: number;
  totalCost: number;
  uptime: number;
  lastReset: Date;
}

export abstract class BaseModelProvider extends EventEmitter implements ModelProvider {
  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly type: ProviderType;
  abstract readonly capabilities: ProviderCapabilities;
  public readonly config: ProviderConfig;

  private health: ProviderHealth;
  private metrics: ProviderMetrics;
  private initialized = false;

  constructor(config: ProviderConfig) {
    super();
    this.config = config;
    this.health = {
      status: HealthStatus.UNKNOWN,
      lastCheck: new Date(),
    };
    this.metrics = {
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      averageLatency: 0,
      totalTokensProcessed: 0,
      totalCost: 0,
      uptime: 0,
      lastReset: new Date(),
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      await this.doInitialize();
      this.initialized = true;
      this.health.status = HealthStatus.HEALTHY;
      this.emit('initialized', this.id);
    } catch (error) {
      this.health.status = HealthStatus.UNHEALTHY;
      this.health.errors = [error instanceof Error ? error.message : 'Unknown error'];
      this.emit('error', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      await this.doShutdown();
      this.initialized = false;
      this.health.status = HealthStatus.UNKNOWN;
      this.emit('shutdown', this.id);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async isHealthy(): Promise<boolean> {
    try {
      this.health.lastCheck = new Date();
      const healthy = await this.checkHealth();
      this.health.status = healthy ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY;
      return healthy;
    } catch (error) {
      this.health.status = HealthStatus.UNHEALTHY;
      this.health.errors = [error instanceof Error ? error.message : 'Health check failed'];
      return false;
    }
  }

  getHealth(): ProviderHealth {
    return { ...this.health };
  }

  getMetrics(): ProviderMetrics {
    return { ...this.metrics };
  }

  resetMetrics(): void {
    this.metrics = {
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      averageLatency: 0,
      totalTokensProcessed: 0,
      totalCost: 0,
      uptime: Date.now() - this.metrics.lastReset.getTime(),
      lastReset: new Date(),
    };
    this.emit('metrics_reset', this.id);
  }

  protected recordRequest(latency: number, success: boolean, tokens?: number, cost?: number): void {
    this.metrics.requestCount++;
    
    if (success) {
      this.metrics.successCount++;
    } else {
      this.metrics.errorCount++;
    }

    // Update average latency
    const totalLatency = this.metrics.averageLatency * (this.metrics.requestCount - 1) + latency;
    this.metrics.averageLatency = totalLatency / this.metrics.requestCount;

    if (tokens) {
      this.metrics.totalTokensProcessed += tokens;
    }

    if (cost) {
      this.metrics.totalCost += cost;
    }

    this.emit('request_recorded', {
      providerId: this.id,
      latency,
      success,
      tokens,
      cost,
    });
  }

  // Abstract methods to be implemented by concrete providers
  protected abstract doInitialize(): Promise<void>;
  protected abstract doShutdown(): Promise<void>;
  protected abstract checkHealth(): Promise<boolean>;

  abstract listModels(): Promise<ModelInfo[]>;
  abstract getModel(modelId: string): Promise<ModelInfo | null>;
  abstract loadModel(modelId: string): Promise<void>;
  abstract unloadModel(modelId: string): Promise<void>;
  abstract generateText(request: TextGenerationRequest): Promise<TextGenerationResponse>;
  abstract generateTextStream(request: TextGenerationRequest): AsyncIterable<TextGenerationChunk>;
  abstract getResourceUsage(): Promise<ResourceUsage>;
  abstract estimateCost(request: CostEstimationRequest): Promise<CostEstimation>;
}

export interface ProviderFactory {
  create(config: ProviderConfig): ModelProvider;
  getProviderType(): ProviderType;
  getSupportedCapabilities(): ProviderCapabilities;
  validateConfig(config: ProviderConfig): boolean;
}