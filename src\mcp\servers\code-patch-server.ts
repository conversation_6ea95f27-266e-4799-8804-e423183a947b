/**
 * Code Patch MCP Server Example
 * Provides code analysis, patching, and modification capabilities
 */

import { EventEmitter } from 'events';
import { WebSocketServer, WebSocket } from 'ws';
import { createHash } from 'crypto';
import { logger } from '../../utils/logger';
import { TraceLogger } from '../../runtime/trace-logger';

// MCP Types
export interface MCPMessage {
  jsonrpc: '2.0';
  id?: string | number;
  method?: string;
  params?: any;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

// Code Analysis Types
export interface AnalyzeCodeRequest {
  code: string;
  language: string;
  analysis_types?: ('syntax' | 'complexity' | 'security' | 'performance' | 'style' | 'dependencies')[];
  include_suggestions?: boolean;
}

export interface AnalyzeCodeResponse {
  analysis: CodeAnalysis;
  suggestions?: CodeSuggestion[];
  execution_time: number;
}

export interface CodeAnalysis {
  syntax?: {
    valid: boolean;
    errors: SyntaxError[];
    warnings: SyntaxWarning[];
  };
  complexity?: {
    cyclomatic_complexity: number;
    cognitive_complexity: number;
    lines_of_code: number;
    maintainability_index: number;
  };
  security?: {
    vulnerabilities: SecurityIssue[];
    risk_score: number;
    recommendations: string[];
  };
  performance?: {
    issues: PerformanceIssue[];
    optimization_score: number;
    bottlenecks: string[];
  };
  style?: {
    violations: StyleViolation[];
    consistency_score: number;
    formatting_issues: string[];
  };
  dependencies?: {
    imports: DependencyInfo[];
    unused_imports: string[];
    circular_dependencies: string[];
  };
}

export interface SyntaxError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
}

export interface SyntaxWarning {
  line: number;
  column: number;
  message: string;
  rule: string;
}

export interface SecurityIssue {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  line: number;
  description: string;
  recommendation: string;
}

export interface PerformanceIssue {
  type: string;
  line: number;
  description: string;
  impact: 'low' | 'medium' | 'high';
  suggestion: string;
}

export interface StyleViolation {
  rule: string;
  line: number;
  column: number;
  message: string;
  fixable: boolean;
}

export interface DependencyInfo {
  name: string;
  version?: string;
  type: 'import' | 'require' | 'dynamic';
  line: number;
  used: boolean;
}

export interface CodeSuggestion {
  type: 'refactor' | 'optimize' | 'fix' | 'enhance';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  before_code: string;
  after_code: string;
  line_range: [number, number];
  confidence: number;
}

// Code Patching Types
export interface GeneratePatchRequest {
  original_code: string;
  target_code?: string;
  language: string;
  patch_type: 'unified' | 'context' | 'git' | 'custom';
  context_lines?: number;
  include_metadata?: boolean;
}

export interface GeneratePatchResponse {
  patch: string;
  patch_type: string;
  metadata: PatchMetadata;
  execution_time: number;
}

export interface PatchMetadata {
  lines_added: number;
  lines_removed: number;
  lines_modified: number;
  files_affected: number;
  complexity_change: number;
  estimated_review_time: number;
}

export interface ApplyPatchRequest {
  original_code: string;
  patch: string;
  patch_type: string;
  validate_result?: boolean;
  dry_run?: boolean;
}

export interface ApplyPatchResponse {
  patched_code: string;
  success: boolean;
  validation_result?: ValidationResult;
  conflicts?: PatchConflict[];
  execution_time: number;
}

export interface ValidationResult {
  syntax_valid: boolean;
  compilation_errors: string[];
  warnings: string[];
  test_results?: TestResult[];
}

export interface PatchConflict {
  line: number;
  type: 'merge' | 'context' | 'deletion';
  description: string;
  suggested_resolution: string;
}

export interface TestResult {
  test_name: string;
  status: 'pass' | 'fail' | 'skip';
  message?: string;
  duration: number;
}

// Code Generation Types
export interface GenerateCodeRequest {
  description: string;
  language: string;
  style?: 'functional' | 'object_oriented' | 'procedural';
  framework?: string;
  include_tests?: boolean;
  include_documentation?: boolean;
  max_length?: number;
}

export interface GenerateCodeResponse {
  code: string;
  tests?: string;
  documentation?: string;
  metadata: GenerationMetadata;
  execution_time: number;
}

export interface GenerationMetadata {
  language: string;
  style: string;
  framework?: string;
  lines_generated: number;
  complexity_estimate: number;
  confidence_score: number;
  dependencies: string[];
}

export interface RefactorCodeRequest {
  code: string;
  language: string;
  refactor_type: 'extract_method' | 'extract_class' | 'rename' | 'move' | 'inline' | 'optimize';
  target?: string;
  options?: RefactorOptions;
}

export interface RefactorOptions {
  preserve_comments?: boolean;
  update_references?: boolean;
  generate_tests?: boolean;
  maintain_compatibility?: boolean;
}

export interface RefactorCodeResponse {
  refactored_code: string;
  changes: RefactorChange[];
  impact_analysis: ImpactAnalysis;
  execution_time: number;
}

export interface RefactorChange {
  type: 'add' | 'remove' | 'modify' | 'move';
  description: string;
  line_range: [number, number];
  before_code?: string;
  after_code?: string;
}

export interface ImpactAnalysis {
  breaking_changes: boolean;
  affected_files: string[];
  test_updates_needed: boolean;
  documentation_updates_needed: boolean;
  estimated_effort: string;
}

// Server Configuration
export interface CodePatchServerConfig {
  port: number;
  host: string;
  supportedLanguages: string[];
  maxCodeLength: number;
  enableCaching: boolean;
  cacheSize: number;
  enableTracing: boolean;
  rateLimits: {
    requestsPerMinute: number;
    linesPerMinute: number;
  };
  analysisTimeout: number;
  patchTimeout: number;
}

// Mock Code Analysis Engine
class MockCodeAnalysisEngine {
  private supportedLanguages: Set<string>;

  constructor(supportedLanguages: string[]) {
    this.supportedLanguages = new Set(supportedLanguages);
  }

  async analyzeCode(
    code: string,
    language: string,
    analysisTypes: string[],
    includeSuggestions: boolean
  ): Promise<{ analysis: CodeAnalysis; suggestions?: CodeSuggestion[] }> {
    if (!this.supportedLanguages.has(language)) {
      throw new Error(`Language ${language} not supported`);
    }

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, Math.min(code.length / 50, 3000)));

    const lines = code.split('\n');
    const analysis: CodeAnalysis = {};

    if (analysisTypes.includes('syntax')) {
      analysis.syntax = this.analyzeSyntax(code, language, lines);
    }

    if (analysisTypes.includes('complexity')) {
      analysis.complexity = this.analyzeComplexity(code, lines);
    }

    if (analysisTypes.includes('security')) {
      analysis.security = this.analyzeSecurity(code, language, lines);
    }

    if (analysisTypes.includes('performance')) {
      analysis.performance = this.analyzePerformance(code, language, lines);
    }

    if (analysisTypes.includes('style')) {
      analysis.style = this.analyzeStyle(code, language, lines);
    }

    if (analysisTypes.includes('dependencies')) {
      analysis.dependencies = this.analyzeDependencies(code, language, lines);
    }

    let suggestions: CodeSuggestion[] | undefined;
    if (includeSuggestions) {
      suggestions = this.generateSuggestions(code, analysis, language);
    }

    return { analysis, suggestions };
  }

  async generatePatch(
    originalCode: string,
    targetCode: string,
    language: string,
    patchType: string,
    contextLines: number
  ): Promise<{ patch: string; metadata: PatchMetadata }> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const originalLines = originalCode.split('\n');
    const targetLines = targetCode.split('\n');

    // Simple diff algorithm
    const changes = this.computeDiff(originalLines, targetLines);
    const patch = this.formatPatch(changes, patchType, contextLines);
    
    const metadata: PatchMetadata = {
      lines_added: changes.filter(c => c.type === 'add').length,
      lines_removed: changes.filter(c => c.type === 'remove').length,
      lines_modified: changes.filter(c => c.type === 'modify').length,
      files_affected: 1,
      complexity_change: this.estimateComplexityChange(originalCode, targetCode),
      estimated_review_time: Math.max(5, changes.length * 2) // minutes
    };

    return { patch, metadata };
  }

  async applyPatch(
    originalCode: string,
    patch: string,
    patchType: string,
    validateResult: boolean,
    dryRun: boolean
  ): Promise<{ patchedCode: string; success: boolean; validationResult?: ValidationResult; conflicts?: PatchConflict[] }> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      const patchedCode = this.applyPatchToCode(originalCode, patch, patchType);
      
      let validationResult: ValidationResult | undefined;
      if (validateResult) {
        validationResult = this.validateCode(patchedCode);
      }

      return {
        patchedCode: dryRun ? originalCode : patchedCode,
        success: true,
        validationResult,
        conflicts: []
      };
    } catch (error) {
      return {
        patchedCode: originalCode,
        success: false,
        conflicts: [{
          line: 1,
          type: 'merge',
          description: (error as Error).message,
          suggested_resolution: 'Review patch format and try again'
        }]
      };
    }
  }

  private analyzeSyntax(code: string, language: string, lines: string[]): CodeAnalysis['syntax'] {
    const errors: SyntaxError[] = [];
    const warnings: SyntaxWarning[] = [];

    // Simple syntax checks based on language
    switch (language) {
      case 'javascript':
      case 'typescript':
        // Check for common JS/TS syntax issues
        lines.forEach((line, index) => {
          if (line.includes('var ') && !line.includes('//')) {
            warnings.push({
              line: index + 1,
              column: line.indexOf('var ') + 1,
              message: 'Consider using let or const instead of var',
              rule: 'no-var'
            });
          }

          if (line.includes('==') && !line.includes('===') && !line.includes('//')) {
            warnings.push({
              line: index + 1,
              column: line.indexOf('==') + 1,
              message: 'Use strict equality (===) instead of loose equality (==)',
              rule: 'eqeqeq'
            });
          }

          // Check for unmatched brackets
          const openBrackets = (line.match(/\{/g) || []).length;
          const closeBrackets = (line.match(/\}/g) || []).length;
          if (openBrackets !== closeBrackets && !line.trim().endsWith(',')) {
            errors.push({
              line: index + 1,
              column: line.length,
              message: 'Unmatched brackets',
              severity: 'error'
            });
          }
        });
        break;

      case 'python':
        // Check for common Python syntax issues
        lines.forEach((line, index) => {
          if (line.trim().endsWith(':') && lines[index + 1] && !lines[index + 1].startsWith('    ')) {
            errors.push({
              line: index + 2,
              column: 1,
              message: 'Expected indented block',
              severity: 'error'
            });
          }
        });
        break;
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  private analyzeComplexity(code: string, lines: string[]): CodeAnalysis['complexity'] {
    // Simple complexity metrics
    const linesOfCode = lines.filter(line => line.trim().length > 0 && !line.trim().startsWith('//')).length;

    // Count decision points for cyclomatic complexity
    const decisionPoints = (code.match(/\b(if|else|while|for|switch|case|catch|&&|\|\|)\b/g) || []).length;
    const cyclomaticComplexity = decisionPoints + 1;

    // Cognitive complexity (simplified)
    const nestingLevel = Math.max(...lines.map(line => {
      const leading = line.match(/^(\s*)/)?.[1] || '';
      return Math.floor(leading.length / 2);
    }));
    const cognitiveComplexity = decisionPoints + nestingLevel;

    // Maintainability index (simplified formula)
    const maintainabilityIndex = Math.max(0, 171 - 5.2 * Math.log(linesOfCode) - 0.23 * cyclomaticComplexity);

    return {
      cyclomatic_complexity: cyclomaticComplexity,
      cognitive_complexity: cognitiveComplexity,
      lines_of_code: linesOfCode,
      maintainability_index: Math.round(maintainabilityIndex)
    };
  }

  private analyzeSecurity(code: string, language: string, lines: string[]): CodeAnalysis['security'] {
    const vulnerabilities: SecurityIssue[] = [];
    const recommendations: string[] = [];

    // Check for common security issues
    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // SQL injection patterns
      if (line.includes('SELECT') && line.includes('+')) {
        vulnerabilities.push({
          type: 'sql_injection',
          severity: 'high',
          line: lineNumber,
          description: 'Potential SQL injection vulnerability',
          recommendation: 'Use parameterized queries or prepared statements'
        });
      }

      // XSS patterns
      if (line.includes('innerHTML') && line.includes('+')) {
        vulnerabilities.push({
          type: 'xss',
          severity: 'medium',
          line: lineNumber,
          description: 'Potential XSS vulnerability',
          recommendation: 'Sanitize user input before inserting into DOM'
        });
      }

      // Hardcoded secrets
      if (/password\s*=\s*["'][^"']+["']/i.test(line)) {
        vulnerabilities.push({
          type: 'hardcoded_secret',
          severity: 'critical',
          line: lineNumber,
          description: 'Hardcoded password detected',
          recommendation: 'Use environment variables or secure configuration'
        });
      }

      // Eval usage
      if (line.includes('eval(')) {
        vulnerabilities.push({
          type: 'code_injection',
          severity: 'high',
          line: lineNumber,
          description: 'Use of eval() can lead to code injection',
          recommendation: 'Avoid eval() and use safer alternatives'
        });
      }
    });

    // Generate recommendations
    if (vulnerabilities.length > 0) {
      recommendations.push('Implement input validation and sanitization');
      recommendations.push('Use security linting tools in your CI/CD pipeline');
      recommendations.push('Regular security audits and dependency updates');
    }

    const riskScore = vulnerabilities.reduce((score, vuln) => {
      const severityScores = { low: 1, medium: 3, high: 7, critical: 10 };
      return score + severityScores[vuln.severity];
    }, 0);

    return {
      vulnerabilities,
      risk_score: Math.min(100, riskScore),
      recommendations
    };
  }

  private analyzePerformance(code: string, language: string, lines: string[]): CodeAnalysis['performance'] {
    const issues: PerformanceIssue[] = [];
    const bottlenecks: string[] = [];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Nested loops
      if (line.includes('for') && code.includes('for', code.indexOf(line) + line.length)) {
        issues.push({
          type: 'nested_loop',
          line: lineNumber,
          description: 'Nested loops can cause performance issues with large datasets',
          impact: 'high',
          suggestion: 'Consider optimizing algorithm complexity or using more efficient data structures'
        });
        bottlenecks.push(`Nested loops at line ${lineNumber}`);
      }

      // Synchronous file operations
      if (line.includes('readFileSync') || line.includes('writeFileSync')) {
        issues.push({
          type: 'blocking_io',
          line: lineNumber,
          description: 'Synchronous file operations block the event loop',
          impact: 'medium',
          suggestion: 'Use asynchronous file operations instead'
        });
      }

      // Large array operations
      if (line.includes('.map(') && line.includes('.filter(')) {
        issues.push({
          type: 'chained_array_ops',
          line: lineNumber,
          description: 'Chained array operations create intermediate arrays',
          impact: 'medium',
          suggestion: 'Consider using a single reduce operation or for...of loop'
        });
      }
    });

    const optimizationScore = Math.max(0, 100 - (issues.length * 10));

    return {
      issues,
      optimization_score: optimizationScore,
      bottlenecks
    };
  }

  private analyzeStyle(code: string, language: string, lines: string[]): CodeAnalysis['style'] {
    const violations: StyleViolation[] = [];
    const formattingIssues: string[] = [];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // Line length
      if (line.length > 120) {
        violations.push({
          rule: 'max-line-length',
          line: lineNumber,
          column: 121,
          message: 'Line exceeds maximum length of 120 characters',
          fixable: true
        });
      }

      // Trailing whitespace
      if (line.endsWith(' ') || line.endsWith('\t')) {
        violations.push({
          rule: 'no-trailing-spaces',
          line: lineNumber,
          column: line.length,
          message: 'Trailing whitespace',
          fixable: true
        });
      }

      // Inconsistent indentation
      const leadingSpaces = line.match(/^( *)/)?.[1]?.length || 0;
      if (leadingSpaces % 2 !== 0 && line.trim().length > 0) {
        violations.push({
          rule: 'indent',
          line: lineNumber,
          column: 1,
          message: 'Inconsistent indentation',
          fixable: true
        });
      }
    });

    // Check for formatting issues
    if (code.includes('  ')) {
      formattingIssues.push('Multiple consecutive spaces found');
    }
    if (code.includes('\t')) {
      formattingIssues.push('Tabs found, consider using spaces for consistency');
    }

    const consistencyScore = Math.max(0, 100 - (violations.length * 5));

    return {
      violations,
      consistency_score: consistencyScore,
      formatting_issues: formattingIssues
    };
  }

  private analyzeDependencies(code: string, language: string, lines: string[]): CodeAnalysis['dependencies'] {
    const imports: DependencyInfo[] = [];
    const unusedImports: string[] = [];
    const circularDependencies: string[] = [];

    lines.forEach((line, index) => {
      const lineNumber = index + 1;

      // JavaScript/TypeScript imports
      if (language === 'javascript' || language === 'typescript') {
        const importMatch = line.match(/import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/);
        if (importMatch) {
          const moduleName = importMatch[1];
          const isUsed = code.includes(moduleName.split('/').pop() || moduleName);

          imports.push({
            name: moduleName,
            type: 'import',
            line: lineNumber,
            used: isUsed
          });

          if (!isUsed) {
            unusedImports.push(moduleName);
          }
        }

        const requireMatch = line.match(/require\(['"]([^'"]+)['"]\)/);
        if (requireMatch) {
          const moduleName = requireMatch[1];
          imports.push({
            name: moduleName,
            type: 'require',
            line: lineNumber,
            used: true // Assume require imports are used
          });
        }
      }

      // Python imports
      if (language === 'python') {
        const importMatch = line.match(/^import\s+(\w+)/);
        if (importMatch) {
          const moduleName = importMatch[1];
          const isUsed = code.includes(moduleName);

          imports.push({
            name: moduleName,
            type: 'import',
            line: lineNumber,
            used: isUsed
          });

          if (!isUsed) {
            unusedImports.push(moduleName);
          }
        }

        const fromImportMatch = line.match(/^from\s+(\w+)\s+import/);
        if (fromImportMatch) {
          const moduleName = fromImportMatch[1];
          imports.push({
            name: moduleName,
            type: 'import',
            line: lineNumber,
            used: true // Assume from imports are used
          });
        }
      }
    });

    return {
      imports,
      unused_imports: unusedImports,
      circular_dependencies: circularDependencies
    };
  }

  private generateSuggestions(code: string, analysis: CodeAnalysis, language: string): CodeSuggestion[] {
    const suggestions: CodeSuggestion[] = [];

    // Generate suggestions based on analysis results
    if (analysis.complexity && analysis.complexity.cyclomatic_complexity > 10) {
      suggestions.push({
        type: 'refactor',
        priority: 'high',
        title: 'Reduce Cyclomatic Complexity',
        description: 'This function has high cyclomatic complexity. Consider breaking it into smaller functions.',
        before_code: '// Complex function with many branches',
        after_code: '// Refactored into smaller, focused functions',
        line_range: [1, code.split('\n').length],
        confidence: 0.8
      });
    }

    if (analysis.security && analysis.security.vulnerabilities.length > 0) {
      const highSeverityVulns = analysis.security.vulnerabilities.filter(v => v.severity === 'high' || v.severity === 'critical');
      if (highSeverityVulns.length > 0) {
        suggestions.push({
          type: 'fix',
          priority: 'high',
          title: 'Fix Security Vulnerabilities',
          description: 'Critical security issues detected that should be addressed immediately.',
          before_code: '// Code with security vulnerabilities',
          after_code: '// Secure implementation',
          line_range: [highSeverityVulns[0].line, highSeverityVulns[0].line],
          confidence: 0.9
        });
      }
    }

    if (analysis.performance && analysis.performance.optimization_score < 70) {
      suggestions.push({
        type: 'optimize',
        priority: 'medium',
        title: 'Optimize Performance',
        description: 'Several performance issues detected. Consider optimizing algorithms and data structures.',
        before_code: '// Performance bottlenecks',
        after_code: '// Optimized implementation',
        line_range: [1, code.split('\n').length],
        confidence: 0.7
      });
    }

    if (analysis.style && analysis.style.violations.length > 5) {
      suggestions.push({
        type: 'enhance',
        priority: 'low',
        title: 'Improve Code Style',
        description: 'Multiple style violations detected. Run a code formatter to improve consistency.',
        before_code: '// Inconsistent formatting',
        after_code: '// Properly formatted code',
        line_range: [1, code.split('\n').length],
        confidence: 0.95
      });
    }

    return suggestions;
  }

  private computeDiff(originalLines: string[], targetLines: string[]): DiffChange[] {
    const changes: DiffChange[] = [];

    // Simple diff algorithm (LCS-based would be more accurate)
    let i = 0, j = 0;

    while (i < originalLines.length || j < targetLines.length) {
      if (i >= originalLines.length) {
        // Remaining lines are additions
        changes.push({ type: 'add', line: j + 1, content: targetLines[j] });
        j++;
      } else if (j >= targetLines.length) {
        // Remaining lines are deletions
        changes.push({ type: 'remove', line: i + 1, content: originalLines[i] });
        i++;
      } else if (originalLines[i] === targetLines[j]) {
        // Lines are the same
        i++;
        j++;
      } else {
        // Lines are different - check if it's a modification or add/remove
        if (i + 1 < originalLines.length && originalLines[i + 1] === targetLines[j]) {
          // Next original line matches current target - this is a deletion
          changes.push({ type: 'remove', line: i + 1, content: originalLines[i] });
          i++;
        } else if (j + 1 < targetLines.length && originalLines[i] === targetLines[j + 1]) {
          // Current original line matches next target - this is an addition
          changes.push({ type: 'add', line: j + 1, content: targetLines[j] });
          j++;
        } else {
          // Lines are different - modification
          changes.push({ type: 'modify', line: i + 1, content: targetLines[j], originalContent: originalLines[i] });
          i++;
          j++;
        }
      }
    }

    return changes;
  }

  private formatPatch(changes: DiffChange[], patchType: string, contextLines: number): string {
    let patch = '';

    switch (patchType) {
      case 'unified':
        patch += '--- original\n+++ target\n';
        for (const change of changes) {
          switch (change.type) {
            case 'add':
              patch += `+${change.content}\n`;
              break;
            case 'remove':
              patch += `-${change.content}\n`;
              break;
            case 'modify':
              patch += `-${change.originalContent}\n`;
              patch += `+${change.content}\n`;
              break;
          }
        }
        break;

      case 'git':
        patch += 'diff --git a/file b/file\n';
        patch += 'index 1234567..abcdefg 100644\n';
        patch += '--- a/file\n';
        patch += '+++ b/file\n';
        patch += '@@ -1,10 +1,10 @@\n';
        for (const change of changes) {
          switch (change.type) {
            case 'add':
              patch += `+${change.content}\n`;
              break;
            case 'remove':
              patch += `-${change.content}\n`;
              break;
            case 'modify':
              patch += `-${change.originalContent}\n`;
              patch += `+${change.content}\n`;
              break;
          }
        }
        break;

      default:
        // Simple format
        for (const change of changes) {
          patch += `${change.type.toUpperCase()}: Line ${change.line}: ${change.content}\n`;
        }
    }

    return patch;
  }

  private estimateComplexityChange(originalCode: string, targetCode: string): number {
    const originalComplexity = (originalCode.match(/\b(if|else|while|for|switch|case)\b/g) || []).length;
    const targetComplexity = (targetCode.match(/\b(if|else|while|for|switch|case)\b/g) || []).length;
    return targetComplexity - originalComplexity;
  }

  private applyPatchToCode(originalCode: string, patch: string, patchType: string): string {
    // Simple patch application (real implementation would be more robust)
    const lines = originalCode.split('\n');
    const patchLines = patch.split('\n');

    let result = [...lines];
    let lineOffset = 0;

    for (const patchLine of patchLines) {
      if (patchLine.startsWith('+')) {
        const content = patchLine.substring(1);
        result.splice(lineOffset, 0, content);
        lineOffset++;
      } else if (patchLine.startsWith('-')) {
        const content = patchLine.substring(1);
        const index = result.findIndex(line => line === content);
        if (index !== -1) {
          result.splice(index, 1);
          lineOffset--;
        }
      }
    }

    return result.join('\n');
  }

  private validateCode(code: string): ValidationResult {
    // Simple validation (real implementation would use language-specific parsers)
    const syntaxValid = !code.includes('undefined') && !code.includes('null');
    const compilationErrors: string[] = [];
    const warnings: string[] = [];

    if (!syntaxValid) {
      compilationErrors.push('Potential undefined or null references detected');
    }

    if (code.includes('console.log')) {
      warnings.push('Debug statements found - consider removing before production');
    }

    return {
      syntax_valid: syntaxValid,
      compilation_errors: compilationErrors,
      warnings
    };
  }
}

interface DiffChange {
  type: 'add' | 'remove' | 'modify';
  line: number;
  content: string;
  originalContent?: string;
}

/**
 * Code Patch MCP Server
 */
export class CodePatchServer extends EventEmitter {
  private config: CodePatchServerConfig;
  private tracer: TraceLogger;
  private wss: WebSocketServer | null = null;
  private clients = new Map<WebSocket, ClientInfo>();
  private analysisEngine: MockCodeAnalysisEngine;
  private cache = new Map<string, any>();
  private rateLimitCounters = new Map<string, RateLimitCounter>();

  constructor(config: Partial<CodePatchServerConfig>) {
    super();

    this.config = {
      port: 8083,
      host: '0.0.0.0',
      supportedLanguages: ['javascript', 'typescript', 'python', 'java', 'go', 'rust', 'cpp'],
      maxCodeLength: 100000,
      enableCaching: true,
      cacheSize: 200,
      enableTracing: true,
      rateLimits: {
        requestsPerMinute: 30,
        linesPerMinute: 5000,
      },
      analysisTimeout: 30000,
      patchTimeout: 15000,
      ...config,
    };

    this.tracer = new TraceLogger({
      serviceName: 'mcp-code-patch-server',
      enableConsoleSpans: this.config.enableTracing,
    });

    this.analysisEngine = new MockCodeAnalysisEngine(this.config.supportedLanguages);

    logger.info('Code Patch MCP server initialized', {
      port: this.config.port,
      supportedLanguages: this.config.supportedLanguages.length,
      caching: this.config.enableCaching,
    });
  }

  async start(): Promise<void> {
    const span = this.tracer.startSpan('start_code_patch_server');

    try {
      this.wss = new WebSocketServer({
        port: this.config.port,
        host: this.config.host,
      });

      this.wss.on('connection', (ws, request) => {
        this.handleConnection(ws, request);
      });

      this.wss.on('error', (error) => {
        logger.error('WebSocket server error', { error });
        this.emit('error', error);
      });

      // Start cleanup interval
      setInterval(() => this.cleanup(), 60000); // 1 minute

      span.setAttributes({
        port: this.config.port,
        host: this.config.host,
      });

      logger.info('Code Patch MCP server started', {
        port: this.config.port,
        host: this.config.host,
      });

      this.emit('started');

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Failed to start code patch server', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  async stop(): Promise<void> {
    logger.info('Stopping code patch MCP server');

    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    // Close all client connections
    for (const ws of this.clients.keys()) {
      ws.close(1000, 'Server shutdown');
    }
    this.clients.clear();

    this.cache.clear();
    this.rateLimitCounters.clear();

    this.emit('stopped');
    logger.info('Code patch MCP server stopped');
  }

  private handleConnection(ws: WebSocket, request: any): void {
    const clientInfo: ClientInfo = {
      id: Math.random().toString(36).substr(2, 9),
      connectedAt: new Date(),
      lastActivity: new Date(),
      requestCount: 0,
      ipAddress: request.socket.remoteAddress,
      userAgent: request.headers['user-agent'],
    };

    this.clients.set(ws, clientInfo);

    logger.info('Client connected', {
      clientId: clientInfo.id,
      ipAddress: clientInfo.ipAddress,
    });

    ws.on('message', async (data) => {
      await this.handleMessage(ws, clientInfo, data);
    });

    ws.on('close', (code, reason) => {
      this.clients.delete(ws);
      logger.info('Client disconnected', {
        clientId: clientInfo.id,
        code,
        reason: reason.toString(),
      });
    });

    ws.on('error', (error) => {
      logger.error('Client connection error', {
        clientId: clientInfo.id,
        error,
      });
    });

    // Send initial server capabilities
    this.sendMessage(ws, {
      jsonrpc: '2.0',
      method: 'initialized',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: [
            {
              name: 'analyze_code',
              description: 'Analyze code for syntax, complexity, security, performance, and style issues',
              inputSchema: {
                type: 'object',
                properties: {
                  code: { type: 'string' },
                  language: { type: 'string', enum: this.config.supportedLanguages },
                  analysis_types: {
                    type: 'array',
                    items: { type: 'string', enum: ['syntax', 'complexity', 'security', 'performance', 'style', 'dependencies'] }
                  },
                  include_suggestions: { type: 'boolean' }
                },
                required: ['code', 'language']
              }
            },
            {
              name: 'generate_patch',
              description: 'Generate a patch between original and target code',
              inputSchema: {
                type: 'object',
                properties: {
                  original_code: { type: 'string' },
                  target_code: { type: 'string' },
                  language: { type: 'string', enum: this.config.supportedLanguages },
                  patch_type: { type: 'string', enum: ['unified', 'context', 'git', 'custom'] },
                  context_lines: { type: 'number', minimum: 0, maximum: 10 },
                  include_metadata: { type: 'boolean' }
                },
                required: ['original_code', 'target_code', 'language']
              }
            },
            {
              name: 'apply_patch',
              description: 'Apply a patch to original code',
              inputSchema: {
                type: 'object',
                properties: {
                  original_code: { type: 'string' },
                  patch: { type: 'string' },
                  patch_type: { type: 'string', enum: ['unified', 'context', 'git', 'custom'] },
                  validate_result: { type: 'boolean' },
                  dry_run: { type: 'boolean' }
                },
                required: ['original_code', 'patch']
              }
            }
          ],
          resources: [
            {
              uri: 'code-patch://languages',
              name: 'Supported Languages',
              description: 'List of supported programming languages'
            }
          ]
        },
        serverInfo: {
          name: 'Code Patch MCP Server',
          version: '1.0.0'
        }
      }
    });
  }

  private async sendMessage(ws: WebSocket, message: MCPMessage): Promise<void> {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private cleanup(): void {
    const now = Date.now();

    // Clean up old rate limit counters
    for (const [key, counter] of this.rateLimitCounters.entries()) {
      if (now - counter.windowStart > 300000) { // 5 minutes
        this.rateLimitCounters.delete(key);
      }
    }

    logger.debug('Code patch server cleanup completed', {
      rateLimitCounters: this.rateLimitCounters.size,
      cacheSize: this.cache.size,
      activeClients: this.clients.size,
    });
  }

  getServerInfo(): {
    status: string;
    clients: number;
    supportedLanguages: number;
    cacheSize: number;
    uptime: number;
  } {
    return {
      status: this.wss ? 'running' : 'stopped',
      clients: this.clients.size,
      supportedLanguages: this.config.supportedLanguages.length,
      cacheSize: this.cache.size,
      uptime: process.uptime(),
    };
  }
}

interface ClientInfo {
  id: string;
  connectedAt: Date;
  lastActivity: Date;
  requestCount: number;
  ipAddress?: string;
  userAgent?: string;
}

interface RateLimitCounter {
  requests: number;
  lines: number;
  windowStart: number;
}
