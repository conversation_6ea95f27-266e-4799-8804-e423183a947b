#!/usr/bin/env tsx

import { HostRuntime, HostRuntimeConfig } from '../src/runtime';
import { ModelRouter, healthMonitor, retryHandler } from '../src/mel';
import { createObservabilityHub } from '../src/integration/observability-hub';
import { logger } from '../src/utils/logger';

// Mock Ollama Provider Implementation
class OllamaProvider {
  readonly id = 'ollama-local';
  readonly name = 'Ollama Local';
  readonly type = 'local' as const;
  readonly capabilities = {
    textGeneration: true,
    streaming: true,
    embeddings: false,
    fineTuning: false,
    multiModal: false,
    functionCalling: false,
    maxContextLength: 4096,
    supportedFormats: ['text'],
    customParameters: true,
  };

  constructor(public readonly config: any) {}

  async initialize(): Promise<void> {
    logger.info('Ollama provider initialized');
  }

  async shutdown(): Promise<void> {
    logger.info('Ollama provider shutdown');
  }

  async isHealthy(): Promise<boolean> {
    return true;
  }

  async listModels(): Promise<any[]> {
    return [
      {
        id: 'llama2',
        name: 'llama2',
        description: 'Llama 2 7B model',
        provider: 'ollama',
        version: '1.0.0',
        contextLength: 4096,
        inputCost: 0,
        outputCost: 0,
        capabilities: ['text-generation'],
        status: 'available',
      },
    ];
  }

  async getModel(modelId: string): Promise<any> {
    const models = await this.listModels();
    return models.find(m => m.id === modelId) || null;
  }

  async loadModel(modelId: string): Promise<void> {
    logger.info('Loading model', { modelId });
  }

  async unloadModel(modelId: string): Promise<void> {
    logger.info('Unloading model', { modelId });
  }

  async generateText(request: any): Promise<any> {
    // Mock implementation
    return {
      id: crypto.randomUUID(),
      model: request.model,
      content: 'This is a mock response from Ollama provider.',
      finishReason: 'stop',
      usage: {
        promptTokens: 10,
        completionTokens: 12,
        totalTokens: 22,
      },
      latency: 500,
    };
  }

  async *generateTextStream(request: any): AsyncIterable<any> {
    const tokens = ['This', ' is', ' a', ' mock', ' streaming', ' response', '.'];
    
    for (let i = 0; i < tokens.length; i++) {
      yield {
        id: crypto.randomUUID(),
        model: request.model,
        delta: tokens[i],
        index: i,
        timestamp: Date.now(),
      };
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  async getResourceUsage(): Promise<any> {
    return {
      cpu: { usage: 45, cores: 8 },
      memory: { used: **********, total: **********, percentage: 25 },
      storage: { used: **********, available: **********0 },
      network: { bytesIn: 1024, bytesOut: 2048 },
    };
  }

  async estimateCost(request: any): Promise<any> {
    return {
      estimatedCost: 0, // Local models are free
      currency: 'USD',
      breakdown: { inputCost: 0, outputCost: 0 },
      confidence: 1.0,
    };
  }

  getMetrics(): any {
    return {
      requestCount: 10,
      successCount: 9,
      errorCount: 1,
      averageLatency: 500,
      totalTokensProcessed: 1000,
      totalCost: 0,
      uptime: 3600000,
      lastReset: new Date(),
    };
  }

  getHealth(): any {
    return {
      status: 'healthy',
      lastCheck: new Date(),
    };
  }
}

async function demonstrateCompleteSystem(): Promise<void> {
  console.log('🚀 Starting Complete System Demonstration\n');

  try {
    // 1. Initialize observability
    console.log('📊 Initializing observability...');
    const observabilityHub = createObservabilityHub();
    await observabilityHub.start();

    // 2. Initialize MEL components
    console.log('🧠 Setting up Model Execution Layer...');
    
    // Create model router
    const modelRouter = new ModelRouter({
      enableLoadBalancing: true,
      budgetTrackingEnabled: true,
      costOptimizationEnabled: true,
    });

    // Add mock Ollama provider
    const ollamaProvider = new OllamaProvider({
      baseUrl: 'http://localhost:11434',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimits: {
        requestsPerMinute: 60,
        tokensPerMinute: 10000,
        concurrent: 5,
      },
    });

    await ollamaProvider.initialize();
    modelRouter.addProvider('ollama-local', ollamaProvider as any);
    healthMonitor.addProvider('ollama-local', ollamaProvider as any);

    // 3. Configure Host Runtime
    console.log('🏗️ Setting up Host Runtime...');
    const runtimeConfig: HostRuntimeConfig = {
      host: '0.0.0.0',
      port: 3000,
      cors: {
        origin: true,
        credentials: true,
      },
      rateLimit: {
        max: 100,
        timeWindow: '1 minute',
      },
      websocket: {
        options: {
          maxPayload: 1024 * 1024, // 1MB
          idleTimeout: 30000,
        },
      },
      security: {
        helmet: true,
        apiKeyRequired: false, // Disabled for demo
      },
    };

    const hostRuntime = new HostRuntime(runtimeConfig);

    // 4. Start the runtime
    console.log('🎯 Starting Host Runtime...');
    await hostRuntime.start();

    console.log(`✅ System started successfully!`);
    console.log(`   - Host Runtime: http://localhost:3000`);
    console.log(`   - Health Check: http://localhost:3000/health`);
    console.log(`   - WebSocket: ws://localhost:3000/ws`);
    console.log(`   - Metrics: http://localhost:3000/metrics`);

    // 5. Demonstrate routing
    console.log('\n🔀 Demonstrating model routing...');
    
    const mockRequest = {
      model: 'llama2',
      prompt: 'Hello, how are you?',
      parameters: {
        maxTokens: 100,
        temperature: 0.7,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0,
        stopSequences: [],
      },
    };

    try {
      const routingDecision = await modelRouter.route(mockRequest);
      console.log('   ✅ Routing decision:', {
        provider: routingDecision.providerName,
        reason: routingDecision.reason,
        estimatedCost: routingDecision.estimatedCost,
        estimatedLatency: routingDecision.estimatedLatency,
      });
    } catch (error) {
      console.log('   ❌ Routing failed:', error instanceof Error ? error.message : error);
    }

    // 6. Demonstrate retry mechanism
    console.log('\n🔄 Demonstrating retry mechanism...');
    
    try {
      const result = await retryHandler.executeWithRetry(
        'demo-operation',
        async (context) => {
          console.log(`   🔃 Attempt ${context.attempt}/${context.maxAttempts}`);
          
          // Simulate failure on first attempt
          if (context.attempt === 1) {
            throw new Error('Simulated network error');
          }
          
          return { success: true, data: 'Operation completed successfully' };
        },
        { maxAttempts: 3, initialDelay: 500 }
      );
      
      console.log('   ✅ Retry operation succeeded:', result);
    } catch (error) {
      console.log('   ❌ Retry operation failed:', error instanceof Error ? error.message : error);
    }

    // 7. Demonstrate health monitoring
    console.log('\n🏥 Demonstrating health monitoring...');
    
    const systemHealth = await healthMonitor.getSystemHealth();
    console.log('   📊 System Health:', {
      status: systemHealth.status,
      uptime: Math.round(systemHealth.uptime / 1000) + 's',
      summary: systemHealth.summary,
    });

    // 8. Test WebSocket streaming (programmatically)
    console.log('\n🌊 Testing streaming capabilities...');
    console.log('   ℹ️  Connect to ws://localhost:3000/models/stream/demo-session to test streaming');

    // 9. Display API endpoints
    console.log('\n📡 Available API Endpoints:');
    console.log('   GET  /health - Health check');
    console.log('   GET  /metrics - Prometheus metrics');
    console.log('   POST /sessions - Create session');
    console.log('   GET  /sessions/:id - Get session');
    console.log('   DELETE /sessions/:id - Delete session');
    console.log('   POST /models/execute - Execute model (placeholder)');
    console.log('   WS   /ws - General WebSocket connection');
    console.log('   WS   /models/stream/:sessionId - Model streaming');

    // 10. Keep running for demonstration
    console.log('\n⏰ System running... Press Ctrl+C to stop');
    
    // Graceful shutdown handling
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down gracefully...');
      
      try {
        await hostRuntime.stop();
        await ollamaProvider.shutdown();
        await healthMonitor.shutdown();
        await observabilityHub.shutdown();
        
        console.log('✅ Shutdown complete');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    });

    // Demonstrate periodic health checks
    setInterval(async () => {
      const health = await healthMonitor.getSystemHealth();
      logger.debug('Periodic health check', {
        status: health.status,
        healthyChecks: health.summary.healthy,
        totalChecks: health.summary.total,
      });
    }, 30000);

  } catch (error) {
    console.error('❌ System startup failed:', error);
    process.exit(1);
  }
}

// Example usage functions
export async function testSessionManagement(): Promise<void> {
  console.log('\n🧪 Testing Session Management...');
  
  // This would typically be called through the REST API
  console.log('   ℹ️  Use POST /sessions to create a new session');
  console.log('   ℹ️  Use GET /sessions/:id to retrieve session data');
  console.log('   ℹ️  Sessions include conversation history, preferences, and resource tracking');
}

export async function testPolicyEnforcement(): Promise<void> {
  console.log('\n🧪 Testing Policy Enforcement...');
  
  console.log('   ℹ️  Policies are automatically enforced on all requests');
  console.log('   ℹ️  Try making requests without API keys to see policy violations');
  console.log('   ℹ️  Rate limiting is active - make rapid requests to test throttling');
}

export async function testStreamingTokens(): Promise<void> {
  console.log('\n🧪 Testing Token Streaming...');
  
  console.log('   ℹ️  Connect WebSocket client to ws://localhost:3000/models/stream/test-session');
  console.log('   ℹ️  Send JSON message: {"prompt": "Hello", "model": "llama2"}');
  console.log('   ℹ️  Receive real-time token stream with metadata');
}

// Run the demonstration if this file is executed directly
if (require.main === module) {
  demonstrateCompleteSystem().catch(error => {
    console.error('Demo failed:', error);
    process.exit(1);
  });
}