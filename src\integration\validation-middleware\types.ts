import { ErrorObject } from 'ajv';

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  constraint?: string;
  code: string;
  severity: 'error' | 'warning' | 'info';
  suggestions?: string[];
  context?: Record<string, any>;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  data?: any;
  metadata?: {
    schemaId: string;
    schemaVersion: string;
    validationTime: number;
    attemptCount: number;
    migrationApplied?: boolean;
  };
}

export interface ReAskOptions {
  maxAttempts: number;
  allowPartialData: boolean;
  retryStrategy: 'immediate' | 'exponential' | 'linear';
  baseDelay: number;
  maxDelay: number;
  onRetry?: (attempt: number, error: ValidationError[]) => Promise<any>;
  onMaxAttemptsReached?: (errors: ValidationError[]) => Promise<any>;
}

export interface SchemaValidationConfig {
  schemaId: string;
  version?: string;
  strict: boolean;
  allowMigration: boolean;
  reAskOptions?: ReAskOptions;
  customValidators?: Record<string, (data: any) => ValidationError[]>;
  transformers?: Record<string, (data: any) => any>;
}

export interface MiddlewareOptions {
  enableReAsk: boolean;
  enableMigration: boolean;
  enableCaching: boolean;
  debugMode: boolean;
  defaultReAskOptions: ReAskOptions;
  errorReporting: {
    includeStackTrace: boolean;
    includeContext: boolean;
    maxErrorsPerField: number;
  };
  performance: {
    enableMetrics: boolean;
    slowValidationThreshold: number;
    memoryThreshold: number;
  };
}

export interface HostRuntimeAPI {
  requestInput(prompt: string, schema: object, options?: any): Promise<any>;
  showError(error: ValidationError[]): Promise<void>;
  showWarning(warnings: ValidationError[]): Promise<void>;
  log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any): void;
  metrics: {
    increment(metric: string, tags?: Record<string, string>): void;
    timing(metric: string, duration: number, tags?: Record<string, string>): void;
    gauge(metric: string, value: number, tags?: Record<string, string>): void;
  };
}

export interface MigrationRule {
  fromVersion: string;
  toVersion: string;
  transform: (data: any) => any;
  validate?: (data: any) => boolean;
  description: string;
  reversible: boolean;
}

export interface ValidationContext {
  schemaId: string;
  version: string;
  attempt: number;
  startTime: number;
  hostAPI: HostRuntimeAPI;
  config: SchemaValidationConfig;
  metadata: Record<string, any>;
}

export interface RetryContext {
  attempt: number;
  maxAttempts: number;
  lastErrors: ValidationError[];
  data: any;
  config: SchemaValidationConfig;
  delay: number;
}

export interface ValidationCache {
  get(key: string): ValidationResult | undefined;
  set(key: string, result: ValidationResult, ttl?: number): void;
  invalidate(pattern: string): void;
  clear(): void;
  stats(): {
    hits: number;
    misses: number;
    size: number;
  };
}

export interface ErrorFormatter {
  formatError(error: ErrorObject, data: any, context: ValidationContext): ValidationError;
  formatSummary(errors: ValidationError[]): string;
  generateSuggestions(error: ValidationError, data: any): string[];
}