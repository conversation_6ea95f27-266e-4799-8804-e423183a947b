{"compilerOptions": {"target": "ES2023", "lib": ["ES2023"], "module": "ESNext", "moduleResolution": "Node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@ui/*": ["src/ui/*"], "@orchestration/*": ["src/orchestration/*"], "@integration/*": ["src/integration/*"], "@data/*": ["src/data/*"], "@schemas/*": ["schemas/*"], "@tests/*": ["tests/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "vitest/globals"]}, "include": ["src/**/*", "tests/**/*", "schemas/**/*"], "exclude": ["node_modules", "dist", "coverage", "docker", "docs"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}