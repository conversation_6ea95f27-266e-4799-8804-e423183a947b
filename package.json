{"name": "custom-agent-system", "version": "2.0.0", "description": "Model-Agnostic, MCP-First, Local-First Modular Agent Platform", "main": "dist/index.js", "type": "module", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "demo": "tsx examples/complete-system-demo.ts", "test": "vitest", "test:ci": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "playwright test", "lint": "eslint src/**/*.ts --fix", "lint:check": "eslint src/**/*.ts", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "security:audit": "npm audit", "security:scan": "npm run security:audit && snyk test", "schema:validate": "tsx scripts/validate-schemas.ts", "schema:compile": "json-schema-to-typescript", "schema:examples": "tsx examples/schema-validation-examples.ts", "docker:build": "docker build -t custom-agent-system .", "docker:dev": "docker-compose -f docker/docker-compose.dev.yml up", "k8s:deploy": "helm upgrade --install custom-agent-system ./charts/custom-agent-system", "observability:start": "docker-compose -f docker/observability.yml up -d"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@opentelemetry/api": "^1.7.0", "@opentelemetry/auto-instrumentations-node": "^0.41.0", "@opentelemetry/exporter-jaeger": "^1.18.1", "@opentelemetry/exporter-prometheus": "^0.46.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/sdk-node": "^0.46.0", "@opentelemetry/semantic-conventions": "^1.18.1", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "chroma-js": "^2.4.2", "chromadb": "^1.7.3", "duckdb": "^0.9.2", "fastify": "^4.24.3", "joi": "^17.11.0", "json-schema-to-typescript": "^13.1.1", "mcp-client": "^1.0.0", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "postgres": "^3.4.3", "yjs": "^13.6.10", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.1", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitest/coverage-v8": "^1.1.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-security": "^1.7.1", "prettier": "^3.1.1", "snyk": "^1.1275.0", "tsc-alias": "^1.8.8", "tsx": "^4.6.2", "typescript": "^5.3.3", "vitest": "^1.1.0"}, "keywords": ["ai-agents", "mcp", "local-first", "typescript", "contract-first", "zero-trust", "observability"], "author": "Custom Agent System Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/custom-agents/custom-agent-system.git"}, "bugs": {"url": "https://github.com/custom-agents/custom-agent-system/issues"}, "homepage": "https://github.com/custom-agents/custom-agent-system#readme"}