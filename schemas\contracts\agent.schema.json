{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://custom-agents.dev/schemas/agent.schema.json", "title": "Agent <PERSON><PERSON>", "description": "Schema for agent capability contracts", "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the agent"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable agent name"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Semantic version of the agent"}, "capabilities": {"type": "array", "items": {"$ref": "#/$defs/capability"}, "minItems": 1, "description": "List of agent capabilities"}, "metadata": {"type": "object", "properties": {"description": {"type": "string", "maxLength": 1000, "description": "Agent description"}, "author": {"type": "string", "description": "Agent author"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Agent tags for categorization"}, "created": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}, "required": ["description", "author", "created"], "additionalProperties": false}}, "required": ["id", "name", "version", "capabilities", "metadata"], "additionalProperties": false, "$defs": {"capability": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "description": "Capability name"}, "type": {"type": "string", "enum": ["function", "tool", "resource", "prompt"], "description": "Type of capability"}, "input_schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "JSON Schema for input validation"}, "output_schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "JSON Schema for output validation"}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["read", "write", "execute", "network", "filesystem"]}, "description": "Required permissions"}, "security_level": {"type": "string", "enum": ["low", "medium", "high", "critical"], "default": "medium", "description": "Security classification level"}}, "required": ["name", "type", "input_schema", "output_schema"], "additionalProperties": false}}}