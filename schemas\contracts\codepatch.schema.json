{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://custom-agents.dev/schemas/codepatch.schema.json", "title": "CodePatch Schema", "description": "Schema for code patches in diff-first workflows with semantic versioning support", "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the code patch"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(?:-[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?(?:\\+[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?$", "description": "Semantic version following semver.org specification"}, "patch_type": {"type": "string", "enum": ["unified", "context", "git", "mercurial", "svn", "custom"], "default": "unified", "description": "Type of patch format"}, "operation": {"type": "string", "enum": ["create", "update", "delete", "move", "copy", "rename", "merge"], "description": "Type of operation being performed"}, "source": {"type": "object", "properties": {"file_path": {"type": "string", "minLength": 1, "maxLength": 1000, "description": "Path to the source file"}, "content": {"type": "string", "description": "Original file content"}, "encoding": {"type": "string", "enum": ["utf-8", "utf-16", "ascii", "iso-8859-1", "binary"], "default": "utf-8", "description": "File encoding"}, "language": {"type": "string", "enum": ["javascript", "typescript", "python", "java", "csharp", "cpp", "c", "rust", "go", "php", "ruby", "swift", "kotlin", "scala", "html", "css", "scss", "less", "json", "yaml", "xml", "markdown", "sql", "shell", "dockerfile", "terraform", "plaintext"], "description": "Programming language"}, "line_endings": {"type": "string", "enum": ["lf", "crlf", "cr"], "default": "lf", "description": "Line ending style"}, "checksum": {"type": "string", "pattern": "^[a-f0-9]{64}$", "description": "SHA-256 checksum of original content"}}, "required": ["file_path"], "additionalProperties": false}, "target": {"type": "object", "properties": {"file_path": {"type": "string", "minLength": 1, "maxLength": 1000, "description": "Path to the target file (may differ from source for move/copy)"}, "content": {"type": "string", "description": "Modified file content"}, "encoding": {"type": "string", "enum": ["utf-8", "utf-16", "ascii", "iso-8859-1", "binary"], "default": "utf-8"}, "checksum": {"type": "string", "pattern": "^[a-f0-9]{64}$", "description": "SHA-256 checksum of modified content"}}, "required": ["file_path"], "additionalProperties": false}, "diff": {"type": "object", "properties": {"format": {"type": "string", "enum": ["unified", "context", "side-by-side", "json"], "default": "unified"}, "content": {"type": "string", "description": "The actual diff content"}, "context_lines": {"type": "integer", "minimum": 0, "maximum": 50, "default": 3, "description": "Number of context lines around changes"}, "hunks": {"type": "array", "items": {"$ref": "#/$defs/diff_hunk"}, "description": "Individual change hunks"}, "statistics": {"type": "object", "properties": {"lines_added": {"type": "integer", "minimum": 0}, "lines_removed": {"type": "integer", "minimum": 0}, "lines_modified": {"type": "integer", "minimum": 0}, "total_changes": {"type": "integer", "minimum": 0}}, "additionalProperties": false}}, "required": ["format", "content"], "additionalProperties": false}, "metadata": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 200, "description": "Short description of the patch"}, "description": {"type": "string", "maxLength": 5000, "description": "Detailed description of changes"}, "author": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "id": {"type": "string", "format": "uuid"}}, "required": ["name"], "additionalProperties": false}, "tags": {"type": "array", "items": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"}, "uniqueItems": true, "maxItems": 20}, "category": {"type": "string", "enum": ["feature", "bugfix", "refactor", "docs", "test", "style", "performance", "security"], "description": "Category of the change"}, "priority": {"type": "string", "enum": ["low", "normal", "high", "critical"], "default": "normal"}, "impact": {"type": "string", "enum": ["minimal", "low", "medium", "high", "breaking"], "description": "Impact level of the change"}, "related_issues": {"type": "array", "items": {"type": "string"}, "description": "Related issue or ticket identifiers"}}, "additionalProperties": false}, "validation": {"type": "object", "properties": {"syntax_check": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "status": {"type": "string", "enum": ["pending", "valid", "invalid", "error"]}, "errors": {"type": "array", "items": {"$ref": "#/$defs/validation_error"}}}, "additionalProperties": false}, "semantic_check": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "status": {"type": "string", "enum": ["pending", "valid", "invalid", "error"]}, "warnings": {"type": "array", "items": {"$ref": "#/$defs/validation_warning"}}}, "additionalProperties": false}, "security_scan": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "status": {"type": "string", "enum": ["pending", "safe", "vulnerable", "error"]}, "vulnerabilities": {"type": "array", "items": {"$ref": "#/$defs/security_vulnerability"}}}, "additionalProperties": false}, "test_coverage": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "percentage": {"type": "number", "minimum": 0, "maximum": 100}, "lines_covered": {"type": "integer", "minimum": 0}, "lines_total": {"type": "integer", "minimum": 0}, "missing_coverage": {"type": "array", "items": {"$ref": "#/$defs/coverage_gap"}}}, "additionalProperties": false}}, "additionalProperties": false}, "approval": {"type": "object", "properties": {"status": {"type": "string", "enum": ["pending", "approved", "rejected", "needs_changes"], "default": "pending"}, "reviewers": {"type": "array", "items": {"$ref": "#/$defs/reviewer"}}, "automated_checks": {"type": "object", "properties": {"ci_status": {"type": "string", "enum": ["pending", "running", "passed", "failed", "skipped"]}, "build_status": {"type": "string", "enum": ["pending", "running", "passed", "failed", "skipped"]}, "test_status": {"type": "string", "enum": ["pending", "running", "passed", "failed", "skipped"]}}, "additionalProperties": false}, "requirements": {"type": "object", "properties": {"min_approvals": {"type": "integer", "minimum": 0, "default": 1}, "require_owner_approval": {"type": "boolean", "default": false}, "require_all_checks": {"type": "boolean", "default": true}, "block_on_vulnerabilities": {"type": "boolean", "default": true}}, "additionalProperties": false}}, "additionalProperties": false}, "timestamps": {"type": "object", "properties": {"created": {"type": "string", "format": "date-time", "description": "Patch creation timestamp"}, "updated": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "applied": {"type": "string", "format": "date-time", "description": "Timestamp when patch was applied"}, "expires": {"type": "string", "format": "date-time", "description": "Optional expiration timestamp"}}, "required": ["created", "updated"], "additionalProperties": false}, "context": {"type": "object", "properties": {"repository": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "branch": {"type": "string"}, "commit": {"type": "string", "pattern": "^[a-f0-9]{7,40}$"}, "tag": {"type": "string"}}, "additionalProperties": false}, "environment": {"type": "string", "enum": ["development", "staging", "production", "test"]}, "agent_session": {"type": "string", "format": "uuid", "description": "AI agent session that created this patch"}, "dependencies": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Dependent patch IDs that must be applied first"}}, "additionalProperties": false}}, "required": ["id", "version", "patch_type", "operation", "source", "diff", "timestamps"], "additionalProperties": false, "$defs": {"diff_hunk": {"type": "object", "properties": {"header": {"type": "string", "description": "Hunk header (e.g., @@ -1,4 +1,5 @@)"}, "source_start": {"type": "integer", "minimum": 0}, "source_length": {"type": "integer", "minimum": 0}, "target_start": {"type": "integer", "minimum": 0}, "target_length": {"type": "integer", "minimum": 0}, "lines": {"type": "array", "items": {"$ref": "#/$defs/diff_line"}}}, "required": ["header", "source_start", "source_length", "target_start", "target_length", "lines"], "additionalProperties": false}, "diff_line": {"type": "object", "properties": {"type": {"type": "string", "enum": ["context", "add", "remove"]}, "content": {"type": "string"}, "line_number_source": {"type": "integer", "minimum": 0}, "line_number_target": {"type": "integer", "minimum": 0}}, "required": ["type", "content"], "additionalProperties": false}, "validation_error": {"type": "object", "properties": {"type": {"type": "string", "enum": ["syntax", "semantic", "style", "type"]}, "severity": {"type": "string", "enum": ["error", "warning", "info"]}, "message": {"type": "string"}, "line": {"type": "integer", "minimum": 1}, "column": {"type": "integer", "minimum": 1}, "rule": {"type": "string"}, "fix_suggestion": {"type": "string"}}, "required": ["type", "severity", "message"], "additionalProperties": false}, "validation_warning": {"type": "object", "properties": {"type": {"type": "string"}, "message": {"type": "string"}, "line": {"type": "integer", "minimum": 1}, "column": {"type": "integer", "minimum": 1}, "suggestion": {"type": "string"}}, "required": ["type", "message"], "additionalProperties": false}, "security_vulnerability": {"type": "object", "properties": {"id": {"type": "string"}, "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "type": {"type": "string"}, "description": {"type": "string"}, "cwe_id": {"type": "string"}, "line": {"type": "integer", "minimum": 1}, "remediation": {"type": "string"}}, "required": ["id", "severity", "type", "description"], "additionalProperties": false}, "coverage_gap": {"type": "object", "properties": {"start_line": {"type": "integer", "minimum": 1}, "end_line": {"type": "integer", "minimum": 1}, "type": {"type": "string", "enum": ["statement", "branch", "function", "line"]}}, "required": ["start_line", "end_line", "type"], "additionalProperties": false}, "reviewer": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected", "needs_changes"]}, "comments": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "status"], "additionalProperties": false}}}