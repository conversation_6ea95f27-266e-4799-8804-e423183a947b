import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { ValidationResult, SchemaRegistry } from './validator';
import { SemanticVersionManager, SchemaVersionInfo, schemaVersionRegistry } from './semantic-versioning';
import { logger } from '@/utils/logger';
import crypto from 'crypto';

export interface ValidationOptions {
  strict?: boolean;
  allowDeprecated?: boolean;
  requireLatestVersion?: boolean;
  customValidators?: Record<string, (data: any) => boolean>;
  securityLevel?: 'low' | 'medium' | 'high' | 'critical';
}

export interface AdvancedValidationResult extends ValidationResult {
  version?: string;
  schemaChecksum?: string;
  securityIssues?: SecurityIssue[];
  performanceMetrics?: PerformanceMetrics;
  suggestions?: string[];
}

export interface SecurityIssue {
  type: 'injection' | 'xss' | 'overflow' | 'traversal' | 'disclosure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  field: string;
  description: string;
  remediation: string;
}

export interface PerformanceMetrics {
  validationTimeMs: number;
  memoryUsageMB: number;
  schemaComplexity: number;
  dataSize: number;
}

export class AdvancedContractValidator implements SchemaRegistry {
  private ajv: Ajv;
  private schemas: Map<string, any> = new Map();
  private securityValidators: Map<string, (data: any) => SecurityIssue[]> = new Map();

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      verbose: true,
      strict: true,
      validateSchema: true,
      addUsedSchema: false,
      allowUnionTypes: true,
    });

    addFormats(this.ajv);
    this.initializeSecurityValidators();
    this.loadBuiltInSchemas();
  }

  private initializeSecurityValidators(): void {
    // SQL Injection detection
    this.securityValidators.set('sql_injection', (data: any) => {
      const issues: SecurityIssue[] = [];
      const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
        /(;|\-\-|\/\*|\*\/|xp_|sp_)/i,
        /('|\"|`).*(OR|AND).*(=|<|>)/i
      ];

      this.checkStringFields(data, '', (value, field) => {
        sqlPatterns.forEach(pattern => {
          if (pattern.test(value)) {
            issues.push({
              type: 'injection',
              severity: 'high',
              field,
              description: 'Potential SQL injection detected',
              remediation: 'Use parameterized queries and input sanitization'
            });
          }
        });
      });

      return issues;
    });

    // XSS detection
    this.securityValidators.set('xss', (data: any) => {
      const issues: SecurityIssue[] = [];
      const xssPatterns = [
        /<script[^>]*>.*?<\/script>/gi,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe[^>]*>/i,
        /expression\s*\(/i
      ];

      this.checkStringFields(data, '', (value, field) => {
        xssPatterns.forEach(pattern => {
          if (pattern.test(value)) {
            issues.push({
              type: 'xss',
              severity: 'high',
              field,
              description: 'Potential XSS vulnerability detected',
              remediation: 'Sanitize and escape user input'
            });
          }
        });
      });

      return issues;
    });

    // Path traversal detection
    this.securityValidators.set('path_traversal', (data: any) => {
      const issues: SecurityIssue[] = [];
      const traversalPatterns = [
        /\.\.\/|\.\.\\/g,
        /\/etc\/passwd/i,
        /\/windows\/system32/i,
        /%2e%2e%2f/i
      ];

      this.checkStringFields(data, '', (value, field) => {
        traversalPatterns.forEach(pattern => {
          if (pattern.test(value)) {
            issues.push({
              type: 'traversal',
              severity: 'medium',
              field,
              description: 'Potential path traversal detected',
              remediation: 'Validate and sanitize file paths'
            });
          }
        });
      });

      return issues;
    });
  }

  private checkStringFields(obj: any, prefix: string, callback: (value: string, field: string) => void): void {
    if (typeof obj === 'string') {
      callback(obj, prefix);
      return;
    }

    if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        const fieldPath = prefix ? `${prefix}.${key}` : key;
        if (typeof value === 'string') {
          callback(value, fieldPath);
        } else if (typeof value === 'object') {
          this.checkStringFields(value, fieldPath, callback);
        }
      }
    }
  }

  private loadBuiltInSchemas(): void {
    try {
      // Register all built-in schemas with version info
      const schemas = [
        { id: 'micronote', file: 'micronote.schema.json', version: '1.0.0' },
        { id: 'uielement', file: 'uielement.schema.json', version: '1.0.0' },
        { id: 'codepatch', file: 'codepatch.schema.json', version: '1.0.0' },
        { id: 'tool-io', file: 'tool-io.schema.json', version: '1.0.0' },
        { id: 'agent', file: 'agent.schema.json', version: '1.0.0' },
        { id: 'mcp', file: 'mcp.schema.json', version: '1.0.0' },
      ];

      schemas.forEach(({ id, version }) => {
        const checksum = this.calculateSchemaChecksum(id);
        schemaVersionRegistry.registerSchemaVersion({
          schemaId: id,
          version,
          checksum,
          createdAt: new Date(),
        });
      });

      logger.info('Built-in schemas with versioning loaded successfully');
    } catch (error) {
      logger.error('Failed to load built-in schemas', { error });
      throw new Error('Schema initialization failed');
    }
  }

  private calculateSchemaChecksum(schemaId: string): string {
    const schema = this.schemas.get(schemaId);
    if (!schema) {
      return '';
    }
    return crypto.createHash('sha256').update(JSON.stringify(schema)).digest('hex');
  }

  registerSchema(schemaId: string, schema: object, version?: string): void {
    try {
      this.ajv.addSchema(schema, schemaId);
      this.schemas.set(schemaId, schema);

      // Register version information
      if (version) {
        const checksum = crypto.createHash('sha256').update(JSON.stringify(schema)).digest('hex');
        schemaVersionRegistry.registerSchemaVersion({
          schemaId,
          version,
          checksum,
          createdAt: new Date(),
        });
      }

      logger.debug(`Schema registered: ${schemaId}`, { version });
    } catch (error) {
      logger.error(`Failed to register schema: ${schemaId}`, { error });
      throw new Error(`Schema registration failed: ${schemaId}`);
    }
  }

  validateContract(data: unknown, schemaId: string, options: ValidationOptions = {}): AdvancedValidationResult {
    const startTime = Date.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      // Get schema version info
      const versionInfo = schemaVersionRegistry.getLatestVersion(schemaId);
      if (options.requireLatestVersion && !versionInfo) {
        return {
          valid: false,
          errors: [`No version information found for schema: ${schemaId}`],
        };
      }

      // Check if deprecated
      if (versionInfo?.deprecated && !options.allowDeprecated) {
        return {
          valid: false,
          errors: [`Schema ${schemaId} version ${versionInfo.version} is deprecated`],
        };
      }

      // Basic schema validation
      const validate = this.ajv.getSchema(schemaId);
      if (!validate) {
        return {
          valid: false,
          errors: [`Schema not found: ${schemaId}`],
        };
      }

      const valid = validate(data);
      const endTime = Date.now();
      const endMemory = process.memoryUsage().heapUsed;

      // Performance metrics
      const performanceMetrics: PerformanceMetrics = {
        validationTimeMs: endTime - startTime,
        memoryUsageMB: (endMemory - startMemory) / (1024 * 1024),
        schemaComplexity: this.calculateSchemaComplexity(this.schemas.get(schemaId)),
        dataSize: JSON.stringify(data).length,
      };

      const result: AdvancedValidationResult = {
        valid,
        version: versionInfo?.version,
        schemaChecksum: versionInfo?.checksum,
        performanceMetrics,
      };

      if (!valid) {
        result.errors = validate.errors?.map(error => {
          const path = error.instancePath ? `${error.instancePath}: ` : '';
          return `${path}${error.message}`;
        }) || ['Unknown validation error'];
      }

      // Security validation
      if (options.securityLevel && options.securityLevel !== 'low') {
        result.securityIssues = this.performSecurityValidation(data, options.securityLevel);
      }

      // Generate suggestions
      result.suggestions = this.generateSuggestions(data, schemaId, result);

      logger.debug('Advanced contract validation completed', {
        schemaId,
        valid: result.valid,
        duration: performanceMetrics.validationTimeMs,
        securityIssues: result.securityIssues?.length || 0,
      });

      return result;
    } catch (error) {
      logger.error('Advanced contract validation error', { schemaId, error });
      return {
        valid: false,
        errors: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`],
      };
    }
  }

  private calculateSchemaComplexity(schema: any): number {
    if (!schema) return 0;
    
    let complexity = 0;
    
    // Count properties
    if (schema.properties) {
      complexity += Object.keys(schema.properties).length;
    }
    
    // Count nested objects
    if (schema.$defs || schema.definitions) {
      const defs = schema.$defs || schema.definitions;
      complexity += Object.keys(defs).length * 2;
    }
    
    // Count required fields
    if (schema.required) {
      complexity += schema.required.length;
    }
    
    return complexity;
  }

  private performSecurityValidation(data: any, level: string): SecurityIssue[] {
    const issues: SecurityIssue[] = [];
    
    // Run all security validators based on level
    const validatorsToRun = level === 'critical' 
      ? Array.from(this.securityValidators.keys())
      : level === 'high'
      ? ['sql_injection', 'xss', 'path_traversal']
      : ['sql_injection', 'xss'];
    
    validatorsToRun.forEach(validatorName => {
      const validator = this.securityValidators.get(validatorName);
      if (validator) {
        issues.push(...validator(data));
      }
    });
    
    return issues;
  }

  private generateSuggestions(data: any, schemaId: string, result: AdvancedValidationResult): string[] {
    const suggestions: string[] = [];
    
    // Performance suggestions
    if (result.performanceMetrics && result.performanceMetrics.validationTimeMs > 100) {
      suggestions.push('Consider optimizing data structure for faster validation');
    }
    
    if (result.performanceMetrics && result.performanceMetrics.dataSize > 1024 * 1024) {
      suggestions.push('Large data size detected - consider pagination or compression');
    }
    
    // Security suggestions
    if (result.securityIssues && result.securityIssues.length > 0) {
      suggestions.push('Security issues detected - review input validation and sanitization');
    }
    
    // Version suggestions
    const versionInfo = schemaVersionRegistry.getLatestVersion(schemaId);
    if (versionInfo?.deprecated) {
      suggestions.push(`Consider migrating to a newer version of ${schemaId} schema`);
    }
    
    return suggestions;
  }

  getSchema(schemaId: string): object | undefined {
    return this.schemas.get(schemaId);
  }

  listSchemas(): string[] {
    return Array.from(this.schemas.keys());
  }

  getSchemaVersions(schemaId: string): SchemaVersionInfo[] {
    return schemaVersionRegistry.getAllVersions(schemaId);
  }

  validateWithVersion(data: unknown, schemaId: string, version: string, options: ValidationOptions = {}): AdvancedValidationResult {
    const versionInfo = schemaVersionRegistry.getVersion(schemaId, version);
    if (!versionInfo) {
      return {
        valid: false,
        errors: [`Schema version ${version} not found for ${schemaId}`],
      };
    }

    // For now, validate against the current schema
    // In a full implementation, we'd load the specific version
    return this.validateContract(data, schemaId, options);
  }

  // Convenience methods for specific schemas
  validateMicroNote(data: unknown, options: ValidationOptions = {}): AdvancedValidationResult {
    return this.validateContract(data, 'micronote', options);
  }

  validateUIElement(data: unknown, options: ValidationOptions = {}): AdvancedValidationResult {
    return this.validateContract(data, 'uielement', options);
  }

  validateCodePatch(data: unknown, options: ValidationOptions = {}): AdvancedValidationResult {
    return this.validateContract(data, 'codepatch', options);
  }

  validateToolIO(data: unknown, options: ValidationOptions = {}): AdvancedValidationResult {
    return this.validateContract(data, 'tool-io', options);
  }
}

export const advancedContractValidator = new AdvancedContractValidator();