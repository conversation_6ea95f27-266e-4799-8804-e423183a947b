# Host Runtime & MEL Implementation Guide

## Overview

This guide covers the implementation of the **Host Runtime skeleton** and **Model Execution Layer (MEL)** interfaces for the Custom Agent System v2.0. The implementation provides a complete foundation for model-agnostic AI agent hosting with enterprise-grade features.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────┐
│                    Host Runtime                         │
├─────────────────┬─────────────────┬─────────────────────┤
│ REST API        │ WebSocket API   │ Session Mgmt        │
│ (Fastify)       │ (Real-time)     │ (TypeScript)        │
├─────────────────┼─────────────────┼─────────────────────┤
│ Streaming       │ Trace Logging   │ Policy Enforcement  │
│ Service         │ (OpenTelemetry) │ (Middleware)        │
└─────────────────┴─────────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                Model Execution Layer (MEL)              │
├─────────────────┬─────────────────┬─────────────────────┤
│ Provider        │ Model Router    │ Retry/Timeout       │
│ Interface       │ (Budget/Policy) │ (Circuit Breaker)   │
├─────────────────┼─────────────────┼─────────────────────┤
│ Health Monitor  │ Cost Tracking   │ Load Balancing      │
│ (System)        │ (Budget Mgmt)   │ (Multi-strategy)    │
└─────────────────┴─────────────────┴─────────────────────┘
```

## Day 1: Host Runtime Implementation ✅

### 1. REST/WebSocket API Structure (Fastify)

**Location**: `src/runtime/host-runtime.ts`

**Features**:
- **Fastify-based HTTP server** with security middleware
- **WebSocket support** for real-time communication
- **Comprehensive error handling** with structured responses
- **Rate limiting** and CORS protection
- **Authentication middleware** with API key support
- **Request/response logging** with OpenTelemetry integration

**Key Endpoints**:
```typescript
// Health and monitoring
GET  /health          // System health check
GET  /metrics         // Prometheus metrics

// Session management
POST   /sessions      // Create new session
GET    /sessions/:id  // Get session details
DELETE /sessions/:id  // Destroy session

// Model execution
POST /models/execute  // Execute model request

// Real-time streaming
WS /ws                           // General WebSocket
WS /models/stream/:sessionId     // Model token streaming
```

### 2. Session Management

**Location**: `src/runtime/session-manager.ts`

**Features**:
- **Comprehensive session lifecycle** management
- **Conversation history** tracking with metadata
- **Resource usage monitoring** (tokens, costs, latency)
- **Permission-based access control** with quotas
- **Automatic cleanup** of expired sessions
- **Rate limiting** per session

**Session Structure**:
```typescript
interface Session {
  id: string;
  metadata: SessionMetadata;        // User info, timestamps, tags
  context: SessionContext;          // Conversation, preferences, permissions
  status: SessionStatus;            // active, idle, expired, suspended
}
```

### 3. Streaming Mock Implementation

**Location**: `src/runtime/streaming-service.ts`

**Features**:
- **Realistic token streaming** with configurable delays
- **Multiple response templates** based on prompt content
- **Comprehensive metadata** (probabilities, timing, models)
- **Stream control** (pause, resume, abort)
- **Performance metrics** tracking
- **Event-driven architecture** with detailed events

**Stream Events**:
- `stream_start` - Stream initialization
- `token` - Individual token delivery
- `metadata` - Periodic progress updates
- `stream_complete` - Successful completion
- `stream_abort` - Manual cancellation
- `error` - Error conditions

### 4. Trace Logging Structure

**Location**: `src/runtime/trace-logger.ts`

**Features**:
- **OpenTelemetry integration** with distributed tracing
- **Multiple trace types** (requests, models, policies, database)
- **Security-aware logging** with sensitive data sanitization
- **Performance metrics** collection
- **Configurable sampling** and retention
- **Structured logging** with correlation IDs

**Trace Types**:
- **RequestTrace** - HTTP request/response cycles
- **ModelExecutionTrace** - AI model invocations
- **PolicyEnforcementTrace** - Policy decision auditing
- **DatabaseTrace** - Database operation monitoring

### 5. Policy Enforcement Middleware

**Location**: `src/runtime/policy-enforcer.ts`

**Features**:
- **Rule-based policy engine** with priority ordering
- **Multiple condition types** (headers, paths, user attributes)
- **Flexible actions** (allow, deny, throttle, redirect)
- **Rate limiting** with multiple strategies
- **Budget enforcement** with quota tracking
- **Caching** for performance optimization

**Policy Actions**:
- `ALLOW` - Permit request processing
- `DENY` - Block request with error response
- `THROTTLE` - Apply rate limiting
- `REDIRECT` - Redirect to different endpoint
- `WARN` - Log warning but allow processing

## Day 2: Model Execution Layer (MEL) ✅

### 1. Abstract Provider Interface

**Location**: `src/mel/provider-interface.ts`

**Features**:
- **Provider abstraction** for Ollama, OpenAI, Anthropic, etc.
- **Comprehensive model metadata** with capabilities
- **Resource usage monitoring** (CPU, memory, GPU)
- **Cost estimation** and tracking
- **Health checking** and status reporting
- **Event-driven architecture** for monitoring

**Provider Types**:
- `LOCAL` - Ollama, local models
- `CLOUD` - OpenAI, Anthropic, etc.
- `HYBRID` - Mixed deployment strategies

### 2. Routing Logic Framework

**Location**: `src/mel/model-router.ts`

**Features**:
- **Multi-criteria routing** (cost, latency, quality, availability)
- **Budget policy enforcement** with daily/monthly limits
- **Load balancing strategies** (round-robin, least-latency, least-cost)
- **Provider scoring** with weighted factors
- **Fallback handling** with provider chains
- **Request queuing** when at capacity

**Routing Strategies**:
- `LEAST_LATENCY` - Prioritize fastest providers
- `LEAST_COST` - Optimize for cost efficiency
- `QUALITY_WEIGHTED` - Balance quality and performance
- `ROUND_ROBIN` - Distribute load evenly

### 3. Retry/Timeout Mechanisms

**Location**: `src/mel/retry-handler.ts`

**Features**:
- **Multiple backoff strategies** (fixed, linear, exponential)
- **Circuit breaker pattern** for fault isolation
- **Configurable retry conditions** based on error types
- **Timeout management** with operation-specific limits
- **Jitter support** to prevent thundering herd
- **Comprehensive metrics** and monitoring

**Backoff Strategies**:
- `FIXED` - Constant retry delay
- `LINEAR` - Linearly increasing delay
- `EXPONENTIAL` - Exponentially increasing delay
- `CUSTOM` - User-defined calculation

### 4. Health Check Integration

**Location**: `src/mel/health-monitor.ts`

**Features**:
- **System-wide health monitoring** with dependency tracking
- **Provider health checking** with automatic discovery
- **Alert generation** with severity levels
- **Dependency validation** before health checks
- **Historical tracking** and trend analysis
- **Configurable thresholds** and escalation

**Health Status Levels**:
- `HEALTHY` - All systems operational
- `DEGRADED` - Some issues but functional
- `UNHEALTHY` - Significant problems detected
- `UNKNOWN` - Status cannot be determined

## Usage Examples

### Starting the Complete System

```bash
# Install dependencies
npm ci

# Run the complete system demonstration
npm run demo

# Or start individual components
npm run dev  # Development server with hot reload
```

### Creating a Custom Provider

```typescript
import { BaseModelProvider, ProviderType } from '@/mel';

class CustomProvider extends BaseModelProvider {
  readonly id = 'custom-provider';
  readonly name = 'Custom AI Provider';
  readonly type = ProviderType.CLOUD;
  readonly capabilities = {
    textGeneration: true,
    streaming: true,
    embeddings: false,
    // ... other capabilities
  };

  protected async doInitialize(): Promise<void> {
    // Initialize your provider
  }

  async generateText(request: TextGenerationRequest): Promise<TextGenerationResponse> {
    // Implement text generation
  }

  // ... implement other required methods
}
```

### Adding Custom Routing Policies

```typescript
import { ModelRouter } from '@/mel';

const router = new ModelRouter();

router.addPolicy({
  id: 'premium-user-routing',
  name: 'Premium User Fast Lane',
  enabled: true,
  priority: 10,
  conditions: [
    {
      type: 'user_tier',
      field: 'tier',
      operator: 'equals',
      value: 'premium',
    }
  ],
  action: {
    type: 'route_to_provider',
    targetProviders: ['fast-provider'],
    loadBalancing: 'least_latency',
  },
});
```

### Implementing Custom Health Checks

```typescript
import { HealthMonitor, HealthChecker } from '@/mel';

class CustomHealthChecker implements HealthChecker {
  async check(): Promise<HealthCheckResult> {
    // Implement your health check logic
    return {
      status: 'healthy',
      latency: 100,
      timestamp: new Date(),
    };
  }
}

const healthMonitor = new HealthMonitor();
healthMonitor.addCheck({
  id: 'custom-check',
  name: 'Custom Service Check',
  enabled: true,
  interval: 30000,
  timeout: 5000,
  retries: 2,
  critical: true,
  dependencies: [],
  checker: new CustomHealthChecker(),
});
```

## Configuration

### Host Runtime Configuration

```typescript
const runtimeConfig: HostRuntimeConfig = {
  host: '0.0.0.0',
  port: 3000,
  cors: {
    origin: ['http://localhost:3000'],
    credentials: true,
  },
  rateLimit: {
    max: 100,
    timeWindow: '1 minute',
  },
  security: {
    helmet: true,
    apiKeyRequired: true,
  },
};
```

### MEL Configuration

```typescript
const melConfig = {
  // Router configuration
  router: {
    enableLoadBalancing: true,
    budgetTrackingEnabled: true,
    costOptimizationEnabled: true,
  },
  
  // Retry configuration
  retry: {
    maxAttempts: 3,
    initialDelay: 1000,
    backoffStrategy: 'exponential',
    circuitBreakerEnabled: true,
  },
  
  // Health monitoring
  health: {
    globalInterval: 30000,
    alertThresholds: {
      degraded: 25,
      critical: 50,
    },
  },
};
```

## Monitoring and Observability

### Metrics Available

- **Request metrics**: Rate, latency, errors
- **Session metrics**: Count, duration, resource usage
- **Provider metrics**: Health, performance, costs
- **Routing metrics**: Decisions, fallbacks, budget usage
- **Retry metrics**: Attempts, success rates, circuit breaker states

### Health Endpoints

```bash
# System health
curl http://localhost:3000/health

# Prometheus metrics
curl http://localhost:3000/metrics

# Provider health details
# Available through HealthMonitor.getSystemHealth()
```

### Distributed Tracing

All operations are traced with OpenTelemetry:
- Request spans with full context
- Model execution traces
- Policy enforcement auditing
- Cross-service correlation

## Security Features

### Authentication & Authorization
- API key validation
- Session-based permissions
- Rate limiting per user/session
- Request sanitization

### Policy Enforcement
- Rule-based access control
- Budget and quota enforcement
- Security header validation
- Custom condition evaluation

### Data Protection
- Sensitive data sanitization
- Audit trail logging
- Encryption support ready
- GDPR compliance features

## Performance Optimizations

### Caching
- Policy evaluation results
- Health check status
- Provider metadata
- Session data (configurable)

### Load Balancing
- Multiple strategies supported
- Provider scoring algorithms
- Request queuing at capacity
- Automatic failover

### Circuit Breakers
- Provider isolation
- Automatic recovery
- Configurable thresholds
- Metrics integration

## Next Steps

This implementation provides a solid foundation for:

1. **Provider Integration**: Add real Ollama, OpenAI, Anthropic providers
2. **Enhanced Streaming**: Implement Server-Sent Events (SSE) support
3. **Advanced Policies**: Add ML-based routing decisions
4. **Persistence**: Add database integration for sessions and metrics
5. **Scaling**: Add horizontal scaling with Redis/database backends
6. **Security**: Add OAuth2, JWT, and advanced authentication

The system is production-ready for development and testing environments, with clear extension points for production deployment requirements.