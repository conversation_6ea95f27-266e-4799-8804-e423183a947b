/**
 * MCP Schema Validation System
 * Validates tool invocations, resource schemas, and protocol messages
 */

import Ajv, { type JSONSchemaType, type ValidateFunction } from 'ajv';
import addFormats from 'ajv-formats';
import { logger } from '../utils/logger';
import { TraceLogger } from '../runtime/trace-logger';
import type { ToolDescriptor, ResourceDescriptor } from './registry';

// Validation Types
export interface ValidationResult {
  valid: boolean;
  errors?: ValidationError[];
  warnings?: ValidationWarning[];
  metadata?: ValidationMetadata;
}

export interface ValidationError {
  code: string;
  message: string;
  path?: string;
  value?: any;
  expected?: any;
  severity: 'error' | 'critical';
}

export interface ValidationWarning {
  code: string;
  message: string;
  path?: string;
  suggestion?: string;
}

export interface ValidationMetadata {
  validatedAt: Date;
  schemaVersion: string;
  validatorVersion: string;
  executionTime: number;
}

// Tool Invocation Types
export interface ToolInvocation {
  tool: string;
  server: string;
  arguments: any;
  context?: InvocationContext;
  metadata?: Record<string, any>;
}

export interface InvocationContext {
  sessionId?: string;
  userId?: string;
  timestamp: Date;
  correlationId?: string;
  parentInvocationId?: string;
}

export interface ToolInvocationResult {
  success: boolean;
  result?: any;
  error?: string;
  metadata?: {
    executionTime: number;
    tokensUsed?: number;
    cost?: number;
  };
}

// Schema Types
export interface SchemaDefinition {
  id: string;
  title: string;
  description?: string;
  version: string;
  schema: any; // JSON Schema
  examples?: any[];
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface SchemaRegistry {
  schemas: Map<string, SchemaDefinition>;
  validators: Map<string, ValidateFunction>;
}

// Validation Configuration
export interface ValidatorConfig {
  strictMode: boolean;
  allowUnknownProperties: boolean;
  coerceTypes: boolean;
  useDefaults: boolean;
  enableFormats: boolean;
  enableSecurity: boolean;
  maxDepth: number;
  maxItems: number;
  maxProperties: number;
  enableTracing: boolean;
}

// Pre-defined MCP Schemas
const MCP_PROTOCOL_SCHEMAS = {
  // JSON-RPC 2.0 Message Schema
  jsonRpcMessage: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'JSON-RPC 2.0 Message',
    type: 'object',
    properties: {
      jsonrpc: { const: '2.0' },
      id: { oneOf: [{ type: 'string' }, { type: 'number' }, { type: 'null' }] },
      method: { type: 'string' },
      params: { oneOf: [{ type: 'object' }, { type: 'array' }] },
      result: {},
      error: {
        type: 'object',
        properties: {
          code: { type: 'number' },
          message: { type: 'string' },
          data: {}
        },
        required: ['code', 'message']
      }
    },
    required: ['jsonrpc'],
    anyOf: [
      { required: ['method'] },
      { required: ['result'] },
      { required: ['error'] }
    ]
  },

  // Tool Call Schema
  toolCall: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'MCP Tool Call',
    type: 'object',
    properties: {
      method: { const: 'tools/call' },
      params: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1 },
          arguments: { type: 'object' }
        },
        required: ['name']
      }
    },
    required: ['method', 'params']
  },

  // Resource Access Schema
  resourceAccess: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'MCP Resource Access',
    type: 'object',
    properties: {
      method: { const: 'resources/read' },
      params: {
        type: 'object',
        properties: {
          uri: { type: 'string', format: 'uri' }
        },
        required: ['uri']
      }
    },
    required: ['method', 'params']
  },

  // Prompt Access Schema
  promptAccess: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'MCP Prompt Access',
    type: 'object',
    properties: {
      method: { const: 'prompts/get' },
      params: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1 },
          arguments: { type: 'object' }
        },
        required: ['name']
      }
    },
    required: ['method', 'params']
  }
};

// Common validation schemas for tools
const COMMON_TOOL_SCHEMAS = {
  // Text processing input
  textInput: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'Text Input',
    type: 'object',
    properties: {
      text: { type: 'string', minLength: 1, maxLength: 100000 },
      language: { type: 'string', pattern: '^[a-z]{2}(-[A-Z]{2})?$' },
      encoding: { type: 'string', enum: ['utf-8', 'ascii', 'latin1'] }
    },
    required: ['text']
  },

  // File processing input
  fileInput: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'File Input',
    type: 'object',
    properties: {
      path: { type: 'string', minLength: 1 },
      content: { type: 'string' },
      mimeType: { type: 'string' },
      encoding: { type: 'string', enum: ['utf-8', 'base64', 'binary'] }
    },
    anyOf: [
      { required: ['path'] },
      { required: ['content'] }
    ]
  },

  // API request input
  apiInput: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    title: 'API Request Input',
    type: 'object',
    properties: {
      url: { type: 'string', format: 'uri' },
      method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] },
      headers: { type: 'object', additionalProperties: { type: 'string' } },
      body: { oneOf: [{ type: 'string' }, { type: 'object' }] },
      timeout: { type: 'number', minimum: 1000, maximum: 300000 }
    },
    required: ['url']
  }
};

/**
 * MCP Schema Validator
 */
export class SchemaValidator {
  private config: ValidatorConfig;
  private tracer: TraceLogger;
  private ajv: Ajv;
  private schemaRegistry: SchemaRegistry;

  constructor(config: Partial<ValidatorConfig> = {}) {
    this.config = {
      strictMode: true,
      allowUnknownProperties: false,
      coerceTypes: false,
      useDefaults: true,
      enableFormats: true,
      enableSecurity: true,
      maxDepth: 20,
      maxItems: 1000,
      maxProperties: 100,
      enableTracing: true,
      ...config,
    };

    this.tracer = new TraceLogger({
      serviceName: 'mcp-schema-validator',
      enableConsoleSpans: this.config.enableTracing,
    });

    // Initialize AJV with configuration
    this.ajv = new Ajv({
      strict: this.config.strictMode,
      additionalProperties: this.config.allowUnknownProperties,
      coerceTypes: this.config.coerceTypes,
      useDefaults: this.config.useDefaults,
      removeAdditional: this.config.strictMode ? 'all' : false,
      allErrors: true,
      verbose: true,
    });

    // Add format validators
    if (this.config.enableFormats) {
      addFormats(this.ajv);
    }

    // Add custom formats
    this.addCustomFormats();

    // Add security keywords
    if (this.config.enableSecurity) {
      this.addSecurityKeywords();
    }

    // Initialize schema registry
    this.schemaRegistry = {
      schemas: new Map(),
      validators: new Map(),
    };

    // Register built-in schemas
    this.registerBuiltinSchemas();

    logger.info('Schema validator initialized', {
      strictMode: this.config.strictMode,
      enableSecurity: this.config.enableSecurity,
      enableFormats: this.config.enableFormats,
    });
  }

  /**
   * Validate tool invocation
   */
  async validateToolInvocation(
    invocation: ToolInvocation,
    toolDescriptor: ToolDescriptor
  ): Promise<ValidationResult> {
    const span = this.tracer.startSpan('validate_tool_invocation', {
      tool: invocation.tool,
      server: invocation.server,
    });

    const startTime = Date.now();

    try {
      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];

      // Validate basic structure
      if (!invocation.tool || typeof invocation.tool !== 'string') {
        errors.push({
          code: 'INVALID_TOOL_NAME',
          message: 'Tool name must be a non-empty string',
          path: 'tool',
          value: invocation.tool,
          severity: 'error',
        });
      }

      if (!invocation.server || typeof invocation.server !== 'string') {
        errors.push({
          code: 'INVALID_SERVER_NAME',
          message: 'Server name must be a non-empty string',
          path: 'server',
          value: invocation.server,
          severity: 'error',
        });
      }

      // Validate tool exists and matches descriptor
      if (invocation.tool !== toolDescriptor.name) {
        errors.push({
          code: 'TOOL_MISMATCH',
          message: `Tool name '${invocation.tool}' does not match descriptor '${toolDescriptor.name}'`,
          path: 'tool',
          value: invocation.tool,
          expected: toolDescriptor.name,
          severity: 'error',
        });
      }

      if (invocation.server !== toolDescriptor.serverId) {
        errors.push({
          code: 'SERVER_MISMATCH',
          message: `Server name '${invocation.server}' does not match descriptor '${toolDescriptor.serverId}'`,
          path: 'server',
          value: invocation.server,
          expected: toolDescriptor.serverId,
          severity: 'error',
        });
      }

      // Validate input against schema
      if (toolDescriptor.inputSchema) {
        const inputValidation = await this.validateData(
          invocation.arguments,
          toolDescriptor.inputSchema,
          'tool-input'
        );

        if (!inputValidation.valid) {
          errors.push(...(inputValidation.errors || []));
        }
        warnings.push(...(inputValidation.warnings || []));
      } else {
        warnings.push({
          code: 'NO_INPUT_SCHEMA',
          message: 'Tool has no input schema defined',
          suggestion: 'Define an input schema for better validation',
        });
      }

      // Validate context if present
      if (invocation.context) {
        const contextValidation = await this.validateInvocationContext(invocation.context);
        if (!contextValidation.valid) {
          errors.push(...(contextValidation.errors || []));
        }
        warnings.push(...(contextValidation.warnings || []));
      }

      // Security validation
      const securityValidation = await this.validateSecurity(invocation.arguments);
      if (!securityValidation.valid) {
        errors.push(...(securityValidation.errors || []));
      }
      warnings.push(...(securityValidation.warnings || []));

      const valid = errors.length === 0;
      const executionTime = Date.now() - startTime;

      span.setAttributes({
        valid,
        errorsCount: errors.length,
        warningsCount: warnings.length,
        executionTime,
      });

      return {
        valid,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime,
        },
      };

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Tool invocation validation failed', { error });
      
      return {
        valid: false,
        errors: [{
          code: 'VALIDATION_ERROR',
          message: `Validation error: ${(error as Error).message}`,
          severity: 'critical',
        }],
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime: Date.now() - startTime,
        },
      };
    } finally {
      span.end();
    }
  }

  /**
   * Validate tool invocation result
   */
  async validateToolResult(
    result: ToolInvocationResult,
    toolDescriptor: ToolDescriptor
  ): Promise<ValidationResult> {
    const span = this.tracer.startSpan('validate_tool_result', {
      tool: toolDescriptor.name,
      success: result.success,
    });

    const startTime = Date.now();

    try {
      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];

      // Validate basic structure
      if (typeof result.success !== 'boolean') {
        errors.push({
          code: 'INVALID_SUCCESS_FLAG',
          message: 'Success flag must be a boolean',
          path: 'success',
          value: result.success,
          severity: 'error',
        });
      }

      // Validate result against output schema if defined
      if (result.success && result.result !== undefined && toolDescriptor.outputSchema) {
        const outputValidation = await this.validateData(
          result.result,
          toolDescriptor.outputSchema,
          'tool-output'
        );

        if (!outputValidation.valid) {
          errors.push(...(outputValidation.errors || []));
        }
        warnings.push(...(outputValidation.warnings || []));
      }

      // Validate error format
      if (!result.success && result.error) {
        if (typeof result.error !== 'string') {
          errors.push({
            code: 'INVALID_ERROR_FORMAT',
            message: 'Error must be a string',
            path: 'error',
            value: result.error,
            severity: 'error',
          });
        }
      }

      // Validate metadata if present
      if (result.metadata) {
        if (typeof result.metadata !== 'object') {
          errors.push({
            code: 'INVALID_METADATA_FORMAT',
            message: 'Metadata must be an object',
            path: 'metadata',
            value: result.metadata,
            severity: 'error',
          });
        } else {
          // Validate specific metadata fields
          if (result.metadata.executionTime !== undefined && 
              (typeof result.metadata.executionTime !== 'number' || result.metadata.executionTime < 0)) {
            errors.push({
              code: 'INVALID_EXECUTION_TIME',
              message: 'Execution time must be a non-negative number',
              path: 'metadata.executionTime',
              value: result.metadata.executionTime,
              severity: 'error',
            });
          }

          if (result.metadata.tokensUsed !== undefined && 
              (typeof result.metadata.tokensUsed !== 'number' || result.metadata.tokensUsed < 0)) {
            errors.push({
              code: 'INVALID_TOKENS_USED',
              message: 'Tokens used must be a non-negative number',
              path: 'metadata.tokensUsed',
              value: result.metadata.tokensUsed,
              severity: 'error',
            });
          }

          if (result.metadata.cost !== undefined && 
              (typeof result.metadata.cost !== 'number' || result.metadata.cost < 0)) {
            errors.push({
              code: 'INVALID_COST',
              message: 'Cost must be a non-negative number',
              path: 'metadata.cost',
              value: result.metadata.cost,
              severity: 'error',
            });
          }
        }
      }

      const valid = errors.length === 0;
      const executionTime = Date.now() - startTime;

      span.setAttributes({
        valid,
        errorsCount: errors.length,
        warningsCount: warnings.length,
        executionTime,
      });

      return {
        valid,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime,
        },
      };

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Tool result validation failed', { error });
      
      return {
        valid: false,
        errors: [{
          code: 'VALIDATION_ERROR',
          message: `Validation error: ${(error as Error).message}`,
          severity: 'critical',
        }],
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime: Date.now() - startTime,
        },
      };
    } finally {
      span.end();
    }
  }

  /**
   * Validate resource data
   */
  async validateResource(
    data: any,
    resourceDescriptor: ResourceDescriptor
  ): Promise<ValidationResult> {
    const span = this.tracer.startSpan('validate_resource', {
      uri: resourceDescriptor.uri,
      mimeType: resourceDescriptor.mimeType,
    });

    const startTime = Date.now();

    try {
      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];

      // Validate against schema if defined
      if (resourceDescriptor.schema) {
        const schemaValidation = await this.validateData(
          data,
          resourceDescriptor.schema,
          'resource-data'
        );

        if (!schemaValidation.valid) {
          errors.push(...(schemaValidation.errors || []));
        }
        warnings.push(...(schemaValidation.warnings || []));
      }

      // Validate size if specified
      if (resourceDescriptor.size !== undefined) {
        const dataSize = this.calculateDataSize(data);
        if (dataSize !== resourceDescriptor.size) {
          warnings.push({
            code: 'SIZE_MISMATCH',
            message: `Data size ${dataSize} does not match expected size ${resourceDescriptor.size}`,
            suggestion: 'Verify resource integrity',
          });
        }
      }

      // Validate checksum if specified
      if (resourceDescriptor.checksum) {
        const calculatedChecksum = await this.calculateChecksum(data);
        if (calculatedChecksum !== resourceDescriptor.checksum) {
          errors.push({
            code: 'CHECKSUM_MISMATCH',
            message: 'Resource checksum does not match expected value',
            path: 'checksum',
            value: calculatedChecksum,
            expected: resourceDescriptor.checksum,
            severity: 'error',
          });
        }
      }

      // MIME type validation
      if (resourceDescriptor.mimeType && typeof data === 'string') {
        const isValidMimeType = await this.validateMimeType(data, resourceDescriptor.mimeType);
        if (!isValidMimeType) {
          warnings.push({
            code: 'MIME_TYPE_MISMATCH',
            message: `Data does not appear to match MIME type ${resourceDescriptor.mimeType}`,
            suggestion: 'Verify resource content type',
          });
        }
      }

      const valid = errors.length === 0;
      const executionTime = Date.now() - startTime;

      return {
        valid,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime,
        },
      };

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Resource validation failed', { error });
      
      return {
        valid: false,
        errors: [{
          code: 'VALIDATION_ERROR',
          message: `Validation error: ${(error as Error).message}`,
          severity: 'critical',
        }],
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime: Date.now() - startTime,
        },
      };
    } finally {
      span.end();
    }
  }

  /**
   * Validate MCP protocol message
   */
  async validateProtocolMessage(message: any): Promise<ValidationResult> {
    return this.validateData(message, MCP_PROTOCOL_SCHEMAS.jsonRpcMessage, 'protocol-message');
  }

  /**
   * Register custom schema
   */
  registerSchema(schema: SchemaDefinition): void {
    try {
      const validator = this.ajv.compile(schema.schema);
      this.schemaRegistry.schemas.set(schema.id, schema);
      this.schemaRegistry.validators.set(schema.id, validator);

      logger.info('Schema registered successfully', {
        schemaId: schema.id,
        title: schema.title,
        version: schema.version,
      });
    } catch (error) {
      logger.error('Failed to register schema', { schemaId: schema.id, error });
      throw error;
    }
  }

  /**
   * Get registered schema
   */
  getSchema(schemaId: string): SchemaDefinition | undefined {
    return this.schemaRegistry.schemas.get(schemaId);
  }

  /**
   * List all registered schemas
   */
  listSchemas(): SchemaDefinition[] {
    return Array.from(this.schemaRegistry.schemas.values());
  }

  // Private helper methods

  private async validateData(data: any, schema: any, context: string): Promise<ValidationResult> {
    const startTime = Date.now();

    try {
      // Create validator if not cached
      let validator = this.schemaRegistry.validators.get(context);
      if (!validator) {
        validator = this.ajv.compile(schema);
        this.schemaRegistry.validators.set(context, validator);
      }

      const valid = validator(data);
      const errors: ValidationError[] = [];
      const warnings: ValidationWarning[] = [];

      if (!valid && validator.errors) {
        for (const error of validator.errors) {
          errors.push({
            code: error.keyword?.toUpperCase() || 'VALIDATION_ERROR',
            message: error.message || 'Validation failed',
            path: error.instancePath,
            value: error.data,
            expected: error.schema,
            severity: 'error',
          });
        }
      }

      return {
        valid,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime: Date.now() - startTime,
        },
      };

    } catch (error) {
      return {
        valid: false,
        errors: [{
          code: 'SCHEMA_ERROR',
          message: `Schema validation error: ${(error as Error).message}`,
          severity: 'critical',
        }],
        metadata: {
          validatedAt: new Date(),
          schemaVersion: '2024-11-05',
          validatorVersion: '2.0.0',
          executionTime: Date.now() - startTime,
        },
      };
    }
  }

  private async validateInvocationContext(context: InvocationContext): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    if (!(context.timestamp instanceof Date)) {
      errors.push({
        code: 'INVALID_TIMESTAMP',
        message: 'Timestamp must be a Date object',
        path: 'context.timestamp',
        value: context.timestamp,
        severity: 'error',
      });
    }

    if (context.sessionId && typeof context.sessionId !== 'string') {
      errors.push({
        code: 'INVALID_SESSION_ID',
        message: 'Session ID must be a string',
        path: 'context.sessionId',
        value: context.sessionId,
        severity: 'error',
      });
    }

    if (context.userId && typeof context.userId !== 'string') {
      errors.push({
        code: 'INVALID_USER_ID',
        message: 'User ID must be a string',
        path: 'context.userId',
        value: context.userId,
        severity: 'error',
      });
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  private async validateSecurity(data: any): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check for potential security issues
    if (typeof data === 'string') {
      // Check for potential XSS
      if (data.includes('<script>') || data.includes('javascript:')) {
        errors.push({
          code: 'POTENTIAL_XSS',
          message: 'Input contains potential XSS content',
          severity: 'critical',
        });
      }

      // Check for SQL injection patterns
      const sqlPatterns = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/i;
      if (sqlPatterns.test(data)) {
        warnings.push({
          code: 'POTENTIAL_SQL_INJECTION',
          message: 'Input contains SQL-like keywords',
          suggestion: 'Ensure proper input sanitization',
        });
      }
    }

    // Check for oversized data
    if (this.calculateDataSize(data) > 10 * 1024 * 1024) { // 10MB
      warnings.push({
        code: 'LARGE_INPUT',
        message: 'Input data is very large',
        suggestion: 'Consider chunking large data',
      });
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  }

  private calculateDataSize(data: any): number {
    if (typeof data === 'string') {
      return Buffer.byteLength(data, 'utf8');
    }
    return Buffer.byteLength(JSON.stringify(data), 'utf8');
  }

  private async calculateChecksum(data: any): Promise<string> {
    const { createHash } = await import('crypto');
    const hash = createHash('sha256');
    
    if (typeof data === 'string') {
      hash.update(data);
    } else {
      hash.update(JSON.stringify(data));
    }
    
    return hash.digest('hex');
  }

  private async validateMimeType(data: string, expectedMimeType: string): Promise<boolean> {
    // Basic MIME type validation - in a real implementation,
    // you might use a library like 'file-type' for magic number detection
    
    if (expectedMimeType.startsWith('text/')) {
      // For text types, check if it's valid UTF-8
      try {
        Buffer.from(data, 'utf8').toString('utf8');
        return true;
      } catch {
        return false;
      }
    }

    if (expectedMimeType === 'application/json') {
      try {
        JSON.parse(data);
        return true;
      } catch {
        return false;
      }
    }

    // For other types, return true (basic implementation)
    return true;
  }

  private addCustomFormats(): void {
    // Add custom format validators
    this.ajv.addFormat('uuid', {
      type: 'string',
      validate: (value: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value)
    });

    this.ajv.addFormat('semver', {
      type: 'string',
      validate: (value: string) => /^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?(\+[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*)?$/.test(value)
    });

    this.ajv.addFormat('mime-type', {
      type: 'string',
      validate: (value: string) => /^[a-zA-Z][a-zA-Z0-9][a-zA-Z0-9!#$&\-^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-^_.]*(\s*;\s*[a-zA-Z0-9-]+=([a-zA-Z0-9-]+|"[^"]*"))*$/.test(value)
    });
  }

  private addSecurityKeywords(): void {
    // Add security-related keywords
    this.ajv.addKeyword({
      keyword: 'maxDepth',
      type: 'object',
      schemaType: 'number',
      compile: (schemaVal: number) => {
        return function validate(data: any): boolean {
          function getDepth(obj: any, currentDepth = 0): number {
            if (currentDepth >= schemaVal) return currentDepth;
            if (typeof obj !== 'object' || obj === null) return currentDepth;
            
            let maxChildDepth = currentDepth;
            for (const value of Object.values(obj)) {
              const childDepth = getDepth(value, currentDepth + 1);
              maxChildDepth = Math.max(maxChildDepth, childDepth);
            }
            return maxChildDepth;
          }
          
          return getDepth(data) <= schemaVal;
        };
      }
    });

    this.ajv.addKeyword({
      keyword: 'noScript',
      type: 'string',
      schemaType: 'boolean',
      compile: (schemaVal: boolean) => {
        return function validate(data: string): boolean {
          if (!schemaVal) return true;
          return !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(data);
        };
      }
    });
  }

  private registerBuiltinSchemas(): void {
    // Register MCP protocol schemas
    for (const [id, schema] of Object.entries(MCP_PROTOCOL_SCHEMAS)) {
      this.registerSchema({
        id: `mcp:${id}`,
        title: schema.title,
        version: '2024-11-05',
        schema,
        tags: ['mcp', 'protocol'],
      });
    }

    // Register common tool schemas
    for (const [id, schema] of Object.entries(COMMON_TOOL_SCHEMAS)) {
      this.registerSchema({
        id: `common:${id}`,
        title: schema.title,
        version: '2.0.0',
        schema,
        tags: ['common', 'tool'],
      });
    }

    logger.info('Built-in schemas registered', {
      mcpSchemas: Object.keys(MCP_PROTOCOL_SCHEMAS).length,
      commonSchemas: Object.keys(COMMON_TOOL_SCHEMAS).length,
    });
  }

  /**
   * Shutdown validator and cleanup resources
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down schema validator');
    
    this.schemaRegistry.schemas.clear();
    this.schemaRegistry.validators.clear();
  }
}