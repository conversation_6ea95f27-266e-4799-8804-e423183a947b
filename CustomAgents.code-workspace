{
	"folders": [
		{
			"name": "Custom Agents System",
			"path": "."
		}
	],
	"settings": {
		// Editor Settings
		"editor.tabSize": 4,
		"editor.insertSpaces": true,
		"editor.detectIndentation": true,
		"editor.trimAutoWhitespace": true,
		"editor.formatOnSave": true,
		"editor.codeActionsOnSave": {
			"source.organizeImports": "explicit",
			"source.fixAll": "explicit"
		},
		"editor.rulers": [80, 120],
		"editor.wordWrap": "wordWrapColumn",
		"editor.wordWrapColumn": 120,
		
		// File Settings
		"files.autoSave": "onFocusChange",
		"files.trimTrailingWhitespace": true,
		"files.insertFinalNewline": true,
		"files.trimFinalNewlines": true,
		"files.exclude": {
			"**/.git": true,
			"**/.svn": true,
			"**/.hg": true,
			"**/CVS": true,
			"**/.DS_Store": true,
			"**/Thumbs.db": true,
			"**/__pycache__": true,
			"**/*.pyc": true,
			"**/*.pyo": true,
			"**/node_modules": true,
			"**/dist": true,
			"**/build": true,
			"**/.env": true,
			"**/.venv": true,
			"**/venv": true,
			"**/*.log": true
		},
		"files.watcherExclude": {
			"**/.git/objects/**": true,
			"**/.git/subtree-cache/**": true,
			"**/node_modules/**": true,
			"**/__pycache__/**": true,
			"**/dist/**": true,
			"**/build/**": true
		},
		
		// Search Settings
		"search.exclude": {
			"**/node_modules": true,
			"**/bower_components": true,
			"**/*.code-search": true,
			"**/__pycache__": true,
			"**/dist": true,
			"**/build": true,
			"**/.git": true,
			"**/*.log": true
		},
		"search.useIgnoreFiles": true,
		"search.useGlobalIgnoreFiles": true,
		
		// Language-specific Settings
		"[python]": {
			"editor.defaultFormatter": "ms-python.python",
			"editor.formatOnSave": true,
			"editor.codeActionsOnSave": {
				"source.organizeImports": "explicit"
			}
		},
		"[javascript]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode"
		},
		"[typescript]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode"
		},
		"[json]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode"
		},
		"[jsonc]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode"
		},
		"[markdown]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode",
			"editor.wordWrap": "on",
			"editor.quickSuggestions": {
				"comments": "off",
				"strings": "off",
				"other": "off"
			}
		},
		"[yaml]": {
			"editor.defaultFormatter": "esbenp.prettier-vscode"
		},
		
		// Git Settings
		"git.autofetch": true,
		"git.enableSmartCommit": true,
		"git.confirmSync": false,
		"git.postCommitCommand": "none",
		
		// Terminal Settings
		"terminal.integrated.defaultProfile.windows": "PowerShell",
		"terminal.integrated.copyOnSelection": true,
		"terminal.integrated.rightClickBehavior": "paste",
		
		// Explorer Settings
		"explorer.confirmDelete": false,
		"explorer.confirmDragAndDrop": false,
		"explorer.sortOrder": "type",
		"explorer.fileNesting.enabled": true,
		"explorer.fileNesting.patterns": {
			"*.py": "${capture}.pyc, ${capture}.pyo",
			"*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
			"*.ts": "${capture}.js, ${capture}.d.ts.map, ${capture}.d.ts, ${capture}.js.map",
			"package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml",
			"requirements.txt": "requirements-dev.txt, requirements-test.txt",
			"README.md": "CHANGELOG.md, CONTRIBUTING.md, LICENSE, LICENSE.md"
		},
		
		// Workbench Settings
		"workbench.editor.enablePreview": false,
		"workbench.editor.enablePreviewFromQuickOpen": false,
		"workbench.startupEditor": "readme",
		"workbench.tree.indent": 20,
		
		// Python-specific Settings
		"python.defaultInterpreterPath": "./venv/Scripts/python.exe",
		"python.terminal.activateEnvironment": true,
		"python.linting.enabled": true,
		"python.linting.pylintEnabled": false,
		"python.linting.flake8Enabled": true,
		"python.testing.pytestEnabled": true,
		"python.testing.unittestEnabled": false,
		"python.testing.pytestArgs": [
			"tests"
		],
		
		// Custom Agents System Specific
		"emmet.includeLanguages": {
			"javascript": "javascriptreact",
			"typescript": "typescriptreact"
		},
		"todo-tree.general.tags": [
			"BUG",
			"HACK",
			"FIXME",
			"TODO",
			"XXX",
			"AGENT",
			"MODULE",
			"SERVICE"
		],
		"todo-tree.highlights.customHighlight": {
			"AGENT": {
				"icon": "robot",
				"foreground": "#00ff00"
			},
			"MODULE": {
				"icon": "package",
				"foreground": "#0080ff"
			},
			"SERVICE": {
				"icon": "gear",
				"foreground": "#ff8000"
			}
		}
	},
	"extensions": {
		"recommendations": [
			// Core Development
			"ms-python.python",
			"ms-python.black-formatter",
			"ms-python.flake8",
			"ms-python.pylint",
			"ms-python.isort",
			
			// JavaScript/TypeScript
			"ms-vscode.vscode-typescript-next",
			"esbenp.prettier-vscode",
			"bradlc.vscode-tailwindcss",
			
			// General Productivity
			"ms-vscode.vscode-json",
			"redhat.vscode-yaml",
			"yzhang.markdown-all-in-one",
			"davidanson.vscode-markdownlint",
			
			// Git and Version Control
			"eamodio.gitlens",
			"github.vscode-pull-request-github",
			"github.copilot",
			"github.copilot-chat",
			
			// Code Quality
			"streetsidesoftware.code-spell-checker",
			"gruntfuggly.todo-tree",
			"aaron-bond.better-comments",
			"oderwat.indent-rainbow",
			
			// Testing and Debugging
			"ms-python.pytest",
			"hbenl.vscode-test-explorer",
			"ms-vscode.test-adapter-converter",
			
			// Documentation
			"ms-python.docstring-generator",
			"njpwerner.autodocstring",
			
			// Utilities
			"ms-vscode.vscode-json",
			"ms-vscode-remote.remote-containers",
			"ms-vscode-remote.remote-wsl",
			"ms-vscode.powershell",
			"formulahendry.auto-rename-tag",
			"christian-kohler.path-intellisense",
			"visualstudioexptteam.vscodeintellicode"
		]
	},
	"tasks": {
		"version": "2.0.0",
		"tasks": [
			{
				"label": "Setup Virtual Environment",
				"type": "shell",
				"command": "python",
				"args": ["-m", "venv", "venv"],
				"group": "build",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			},
			{
				"label": "Install Dependencies",
				"type": "shell",
				"command": "./venv/Scripts/python.exe",
				"args": ["-m", "pip", "install", "-r", "requirements.txt"],
				"group": "build",
				"dependsOn": "Setup Virtual Environment",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			},
			{
				"label": "Run Tests",
				"type": "shell",
				"command": "./venv/Scripts/python.exe",
				"args": ["-m", "pytest", "tests/", "-v"],
				"group": "test",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			},
			{
				"label": "Format Code (Black)",
				"type": "shell",
				"command": "./venv/Scripts/python.exe",
				"args": ["-m", "black", "."],
				"group": "build",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			},
			{
				"label": "Lint Code (Flake8)",
				"type": "shell",
				"command": "./venv/Scripts/python.exe",
				"args": ["-m", "flake8", "."],
				"group": "test",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			},
			{
				"label": "Start Agent System",
				"type": "shell",
				"command": "./venv/Scripts/python.exe",
				"args": ["main.py"],
				"group": "build",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": true,
					"panel": "new"
				},
				"problemMatcher": []
			},
			{
				"label": "Generate Documentation",
				"type": "shell",
				"command": "./venv/Scripts/python.exe",
				"args": ["-m", "pydoc", "-w", "."],
				"group": "build",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			},
			{
				"label": "Clean Build Artifacts",
				"type": "shell",
				"command": "powershell",
				"args": [
					"-Command",
					"Remove-Item -Recurse -Force -ErrorAction SilentlyContinue __pycache__, *.pyc, *.pyo, .pytest_cache, .coverage, htmlcov, dist, build, *.egg-info"
				],
				"group": "build",
				"presentation": {
					"echo": true,
					"reveal": "always",
					"focus": false,
					"panel": "shared"
				},
				"problemMatcher": []
			}
		]
	},
	"launch": {
		"version": "0.2.0",
		"configurations": [
			{
				"name": "Debug Agent System",
				"type": "debugpy",
				"request": "launch",
				"program": "${workspaceFolder}/main.py",
				"console": "integratedTerminal",
				"cwd": "${workspaceFolder}",
				"env": {
					"PYTHONPATH": "${workspaceFolder}"
				},
				"args": [],
				"justMyCode": false,
				"stopOnEntry": false
			},
			{
				"name": "Debug Current File",
				"type": "debugpy",
				"request": "launch",
				"program": "${file}",
				"console": "integratedTerminal",
				"cwd": "${workspaceFolder}",
				"env": {
					"PYTHONPATH": "${workspaceFolder}"
				},
				"justMyCode": true
			},
			{
				"name": "Debug Tests",
				"type": "debugpy",
				"request": "launch",
				"module": "pytest",
				"args": [
					"tests/",
					"-v",
					"--tb=short"
				],
				"console": "integratedTerminal",
				"cwd": "${workspaceFolder}",
				"env": {
					"PYTHONPATH": "${workspaceFolder}"
				},
				"justMyCode": false
			},
			{
				"name": "Debug Specific Agent",
				"type": "debugpy",
				"request": "launch",
				"program": "${workspaceFolder}/main.py",
				"console": "integratedTerminal",
				"cwd": "${workspaceFolder}",
				"env": {
					"PYTHONPATH": "${workspaceFolder}",
					"DEBUG_AGENT": "true"
				},
				"args": [
					"--agent",
					"${input:agentName}",
					"--debug"
				],
				"justMyCode": false
			}
		],
		"inputs": [
			{
				"id": "agentName",
				"description": "Enter the agent name to debug",
				"default": "code_maintainer",
				"type": "promptString"
			}
		]
	}
}