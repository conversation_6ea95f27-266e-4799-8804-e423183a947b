# Custom Agent System v2.0

> Model-Agnostic, MCP-First, Local-First Modular Agent Platform

[![CI](https://github.com/custom-agents/custom-agent-system/workflows/CI/badge.svg)](https://github.com/custom-agents/custom-agent-system/actions)
[![Security](https://github.com/custom-agents/custom-agent-system/workflows/Security/badge.svg)](https://github.com/custom-agents/custom-agent-system/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D20.0.0-brightgreen)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3+-blue)](https://www.typescriptlang.org/)

## Overview

The Custom Agent System v2.0 is a production-ready, security-first agent platform built with 2025 industry best practices. It implements AI-native design principles, local-first development, and comprehensive observability while maintaining strict adherence to schema contracts and human-centered AI principles.

## 🏗️ Architecture

The system implements a 4-layer architecture designed for scalability, security, and maintainability:

```
┌─────────────────────────────────────────────────────────┐
│                 User Interface Layer                    │
├─────────────────┬─────────────────┬─────────────────────┤
│ Diff Approval   │ Capability      │ Retrieval │ Micro-  │
│ Interface       │ Explorer        │ Tuner     │ Notes   │
└─────────────────┴─────────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────┐
│              Agent Orchestration Layer                  │
├─────────────────┬─────────────────┬─────────────────────┤
│ Multi-Agent     │ Reasoning       │ Ethics    │ Explan- │
│ Coordinator     │ Engine          │ Monitor   │ ation   │
└─────────────────┴─────────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────┐
│               Capability Integration                    │
├─────────────────┬─────────────────┬─────────────────────┤
│ MCP Client      │ Contract        │ Security  │ Observ- │
│ (Enhanced)      │ Validator       │ Gateway   │ ability │
└─────────────────┴─────────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                Data & Storage Layer                     │
├─────────────────┬─────────────────┬─────────────────────┤
│ CRDT Sync       │ Vector Store    │ Analytics │ Audit   │
│ Engine          │ (Chroma+)       │ (DuckDB)  │ Log     │
└─────────────────┴─────────────────┴─────────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** 20+ LTS
- **Docker** 24+ with BuildKit
- **Git** for version control

### Installation

```bash
# Clone the repository
git clone https://github.com/custom-agents/custom-agent-system.git
cd custom-agent-system

# Install dependencies
npm ci

# Set up environment
cp .env.example .env

# Start development environment
npm run docker:dev
```

### Development

```bash
# Start the development server
npm run dev

# Run tests
npm test

# Run type checking
npm run type-check

# Run linting
npm run lint

# Format code
npm run format
```

## 📋 Features

### ✅ Phase 0 - Foundation & Contracts (COMPLETED)

- **Contract-First Development**: JSON Schema 2020-12 with semantic versioning
- **DevSecOps Pipeline**: Automated security scanning and compliance
- **Observability Foundation**: OpenTelemetry with four golden signals
- **Schema Registry**: Centralized contract management with validation
- **Security Framework**: Zero-trust architecture with comprehensive scanning

### 🚧 Phase 1 - Enhanced MCP Integration (NEXT)

- **MCP 2.0 Features**: OAuth 2.1, signed descriptors, real-time negotiation
- **Multi-Modal AI**: Vision, text, and audio integration
- **Contract Testing**: Consumer-driven contract validation
- **Advanced Security**: mTLS, advanced authentication flows

### 📅 Future Phases

- **Phase 2**: Local-First Data Architecture with CRDTs
- **Phase 3**: Security & Compliance Excellence
- **Phase 4**: Advanced Observability & SRE
- **Phase 5**: Multi-Agent Orchestration

## 🔒 Security

Security is built into every layer of the system:

- **Zero-Trust Architecture**: Verify every request
- **Automated Scanning**: SAST, DAST, dependency vulnerability scanning
- **Secure Defaults**: Security-first configuration throughout
- **Compliance Ready**: GDPR, CCPA, SOC2 framework support

### Security Commands

```bash
# Run security audit
npm run security:audit

# Run comprehensive security scan
npm run security:scan

# Run security validation script
./scripts/security-check.sh
```

## 📊 Monitoring & Observability

The platform includes comprehensive observability:

- **Metrics**: Prometheus-compatible metrics with custom dashboards
- **Tracing**: Distributed tracing with Jaeger
- **Logging**: Structured JSON logging with Pino
- **Health Checks**: Service health monitoring

### Observability Stack

```bash
# Start observability stack
npm run observability:start

# Access dashboards
open http://localhost:3001  # Grafana
open http://localhost:16686 # Jaeger
open http://localhost:9090  # Prometheus
```

## 🧪 Testing

Comprehensive testing strategy with multiple layers:

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e

# Run tests with coverage
npm run test:ci

# Run contract validation
npm run schema:validate
```

## 📝 Schema Contracts

The system uses JSON Schema 2020-12 for all contracts:

- **Agent Contracts**: AI agent capability definitions
- **MCP Protocol**: Model Context Protocol schemas
- **UI Elements**: Component definition schemas
- **Tool I/O**: Input/output validation schemas

### Schema Validation

```bash
# Validate all schemas
npm run schema:validate

# Run schema examples
npm run schema:examples

# Compile TypeScript types
npm run schema:compile
```

## 🐳 Docker & Deployment

### Development Environment

```bash
# Start full development stack
npm run docker:dev

# Build production image
npm run docker:build

# Deploy to Kubernetes
npm run k8s:deploy
```

### Production Deployment

The system is designed for cloud-native deployment with:

- **Multi-stage Docker builds** for optimized images
- **Kubernetes manifests** with Helm charts
- **GitOps deployment** with ArgoCD
- **Service mesh** architecture with Istio

## 🔧 Configuration

### Environment Variables

Key configuration options:

```bash
# Application
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Database
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Observability
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_PORT=9464

# Security
ENCRYPTION_KEY=...
JWT_SECRET=...
```

### Configuration Files

- `config/security.json` - Security policies and settings
- `docker/` - Container configurations
- `.github/workflows/` - CI/CD pipeline definitions

## 📚 Documentation

- **[API Documentation](docs/api/)** - Complete API reference
- **[Architecture Guide](docs/architecture.md)** - System design and patterns
- **[Security Guide](docs/security.md)** - Security implementation details
- **[Development Guide](docs/development.md)** - Developer onboarding
- **[Deployment Guide](docs/deployment.md)** - Production deployment

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Standards

- **TypeScript**: Strict mode with comprehensive type coverage
- **Testing**: Unit tests for all new functionality
- **Security**: Security review for all changes
- **Documentation**: Update documentation for user-facing changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenTelemetry Community** for observability standards
- **JSON Schema Organization** for schema specifications
- **MCP Protocol** contributors for agent communication standards
- **TypeScript Team** for the excellent type system

---

**Built with ❤️ using 2025 industry best practices**

*For detailed information about the project, see our [Project Initiation Document](PID.md).*