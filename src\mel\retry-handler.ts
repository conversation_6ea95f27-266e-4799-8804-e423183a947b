import { EventEmitter } from 'events';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface RetryConfig {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffStrategy: BackoffStrategy;
  jitterEnabled: boolean;
  jitterMax: number;
  timeout: number;
  retryableErrors: string[];
  retryableStatusCodes: number[];
  circuitBreakerEnabled: boolean;
  circuitBreakerThreshold: number;
  circuitBreakerResetTimeout: number;
}

export enum BackoffStrategy {
  FIXED = 'fixed',
  LINEAR = 'linear',
  EXPONENTIAL = 'exponential',
  CUSTOM = 'custom',
}

export interface RetryContext {
  operationId: string;
  attempt: number;
  maxAttempts: number;
  startTime: number;
  lastAttemptTime: number;
  totalDelay: number;
  errors: RetryError[];
  metadata?: Record<string, any>;
}

export interface RetryError {
  attempt: number;
  error: Error;
  timestamp: number;
  delay?: number;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  context: RetryContext;
  finalAttempt: boolean;
}

export interface CircuitBreakerState {
  state: CircuitState;
  failureCount: number;
  lastFailureTime?: number;
  nextRetryTime?: number;
  totalRequests: number;
  successCount: number;
}

export enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open',
}

export interface TimeoutConfig {
  operation: number; // Operation timeout
  connection: number; // Connection timeout
  read: number; // Read timeout
  total: number; // Total request timeout
}

export interface TimeoutContext {
  operationId: string;
  startTime: number;
  timeouts: {
    operation?: NodeJS.Timeout;
    connection?: NodeJS.Timeout;
    read?: NodeJS.Timeout;
    total?: NodeJS.Timeout;
  };
  abortController?: AbortController;
}

export type RetryableOperation<T> = (context: RetryContext) => Promise<T>;
export type BackoffCalculator = (attempt: number, baseDelay: number) => number;

export class RetryHandler extends EventEmitter {
  private config: RetryConfig;
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private activeOperations: Map<string, RetryContext> = new Map();
  private timeoutContexts: Map<string, TimeoutContext> = new Map();
  private customBackoffCalculator?: BackoffCalculator;

  constructor(config?: Partial<RetryConfig>) {
    super();
    
    this.config = {
      maxAttempts: 3,
      initialDelay: 1000,
      maxDelay: 30000,
      backoffStrategy: BackoffStrategy.EXPONENTIAL,
      jitterEnabled: true,
      jitterMax: 0.1,
      timeout: 30000,
      retryableErrors: [
        'ECONNRESET',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'ENOTFOUND',
        'ENETUNREACH',
        'NETWORK_ERROR',
        'TIMEOUT_ERROR',
        'SERVICE_UNAVAILABLE',
      ],
      retryableStatusCodes: [429, 500, 502, 503, 504],
      circuitBreakerEnabled: true,
      circuitBreakerThreshold: 5,
      circuitBreakerResetTimeout: 60000,
      ...config,
    };

    logger.info('RetryHandler initialized', { config: this.config });
  }

  async executeWithRetry<T>(
    operationId: string,
    operation: RetryableOperation<T>,
    customConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.config, ...customConfig };
    const startTime = Date.now();

    // Check circuit breaker
    if (config.circuitBreakerEnabled && this.isCircuitOpen(operationId)) {
      const error = new Error(`Circuit breaker open for operation: ${operationId}`);
      this.recordMetrics(operationId, false, 0, 'circuit_open');
      throw error;
    }

    const context: RetryContext = {
      operationId,
      attempt: 0,
      maxAttempts: config.maxAttempts,
      startTime,
      lastAttemptTime: startTime,
      totalDelay: 0,
      errors: [],
    };

    this.activeOperations.set(operationId, context);

    try {
      const result = await this.performRetryLoop(operation, context, config);
      
      // Update circuit breaker on success
      if (config.circuitBreakerEnabled) {
        this.recordSuccess(operationId);
      }

      this.recordMetrics(operationId, true, context.attempt, 'success');
      
      logger.debug('Retry operation completed successfully', {
        operationId,
        attempts: context.attempt,
        totalTime: Date.now() - startTime,
      });

      return result;

    } catch (error) {
      // Update circuit breaker on failure
      if (config.circuitBreakerEnabled) {
        this.recordFailure(operationId);
      }

      this.recordMetrics(operationId, false, context.attempt, error instanceof Error ? error.name : 'unknown_error');
      
      logger.error('Retry operation failed after all attempts', {
        operationId,
        attempts: context.attempt,
        totalTime: Date.now() - startTime,
        errors: context.errors.map(e => e.error.message),
      });

      throw error;
      
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  private async performRetryLoop<T>(
    operation: RetryableOperation<T>,
    context: RetryContext,
    config: RetryConfig
  ): Promise<T> {
    while (context.attempt < config.maxAttempts) {
      context.attempt++;
      context.lastAttemptTime = Date.now();

      try {
        // Execute operation with timeout
        const result = await this.executeWithTimeout(
          operation,
          context,
          config.timeout
        );

        this.emit('retry_success', {
          operationId: context.operationId,
          attempt: context.attempt,
          totalTime: Date.now() - context.startTime,
        });

        return result;

      } catch (error) {
        const retryError: RetryError = {
          attempt: context.attempt,
          error: error instanceof Error ? error : new Error(String(error)),
          timestamp: Date.now(),
        };

        context.errors.push(retryError);

        this.emit('retry_attempt_failed', {
          operationId: context.operationId,
          attempt: context.attempt,
          error: retryError.error,
          willRetry: context.attempt < config.maxAttempts && this.isRetryableError(retryError.error, config),
        });

        // Check if this is the last attempt
        if (context.attempt >= config.maxAttempts) {
          throw this.createFinalError(context);
        }

        // Check if error is retryable
        if (!this.isRetryableError(retryError.error, config)) {
          throw retryError.error;
        }

        // Calculate delay and wait
        const delay = this.calculateDelay(context.attempt, config);
        retryError.delay = delay;
        context.totalDelay += delay;

        logger.debug('Retrying operation after delay', {
          operationId: context.operationId,
          attempt: context.attempt,
          delay,
          error: retryError.error.message,
        });

        await this.delay(delay);
      }
    }

    throw this.createFinalError(context);
  }

  private async executeWithTimeout<T>(
    operation: RetryableOperation<T>,
    context: RetryContext,
    timeout: number
  ): Promise<T> {
    const timeoutContext: TimeoutContext = {
      operationId: context.operationId,
      startTime: Date.now(),
      timeouts: {},
      abortController: new AbortController(),
    };

    this.timeoutContexts.set(context.operationId, timeoutContext);

    try {
      // Set up timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        timeoutContext.timeouts.total = setTimeout(() => {
          timeoutContext.abortController?.abort();
          reject(new Error(`Operation timeout after ${timeout}ms`));
        }, timeout);
      });

      // Race between operation and timeout
      const result = await Promise.race([
        operation(context),
        timeoutPromise,
      ]);

      return result;

    } finally {
      this.clearTimeouts(timeoutContext);
      this.timeoutContexts.delete(context.operationId);
    }
  }

  private clearTimeouts(timeoutContext: TimeoutContext): void {
    Object.values(timeoutContext.timeouts).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });
  }

  private isRetryableError(error: Error, config: RetryConfig): boolean {
    // Check error name/type
    if (config.retryableErrors.includes(error.name)) {
      return true;
    }

    // Check error message patterns
    const message = error.message.toLowerCase();
    const retryablePatterns = [
      'timeout',
      'connection reset',
      'connection refused',
      'network error',
      'service unavailable',
      'temporary failure',
    ];

    if (retryablePatterns.some(pattern => message.includes(pattern))) {
      return true;
    }

    // Check status codes for HTTP errors
    if ('status' in error && typeof error.status === 'number') {
      return config.retryableStatusCodes.includes(error.status);
    }

    return false;
  }

  private calculateDelay(attempt: number, config: RetryConfig): number {
    let delay: number;

    switch (config.backoffStrategy) {
      case BackoffStrategy.FIXED:
        delay = config.initialDelay;
        break;

      case BackoffStrategy.LINEAR:
        delay = config.initialDelay * attempt;
        break;

      case BackoffStrategy.EXPONENTIAL:
        delay = config.initialDelay * Math.pow(2, attempt - 1);
        break;

      case BackoffStrategy.CUSTOM:
        if (this.customBackoffCalculator) {
          delay = this.customBackoffCalculator(attempt, config.initialDelay);
        } else {
          delay = config.initialDelay;
        }
        break;

      default:
        delay = config.initialDelay;
    }

    // Apply maximum delay constraint
    delay = Math.min(delay, config.maxDelay);

    // Apply jitter if enabled
    if (config.jitterEnabled) {
      const jitter = delay * config.jitterMax * Math.random();
      delay += jitter;
    }

    return Math.floor(delay);
  }

  private createFinalError(context: RetryContext): Error {
    const lastError = context.errors[context.errors.length - 1];
    const error = new Error(
      `Operation failed after ${context.attempt} attempts. ` +
      `Last error: ${lastError?.error.message || 'Unknown error'}`
    );

    // Attach context information
    (error as any).retryContext = context;
    (error as any).isRetryExhausted = true;

    return error;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Circuit Breaker functionality
  private isCircuitOpen(operationId: string): boolean {
    const state = this.circuitBreakers.get(operationId);
    if (!state) {
      return false;
    }

    const now = Date.now();

    switch (state.state) {
      case CircuitState.OPEN:
        if (state.nextRetryTime && now >= state.nextRetryTime) {
          // Transition to half-open
          state.state = CircuitState.HALF_OPEN;
          logger.info('Circuit breaker transitioning to half-open', { operationId });
          return false;
        }
        return true;

      case CircuitState.HALF_OPEN:
        return false;

      case CircuitState.CLOSED:
      default:
        return false;
    }
  }

  private recordSuccess(operationId: string): void {
    let state = this.circuitBreakers.get(operationId);
    if (!state) {
      state = {
        state: CircuitState.CLOSED,
        failureCount: 0,
        totalRequests: 0,
        successCount: 0,
      };
      this.circuitBreakers.set(operationId, state);
    }

    state.totalRequests++;
    state.successCount++;

    if (state.state === CircuitState.HALF_OPEN) {
      // Success in half-open state transitions to closed
      state.state = CircuitState.CLOSED;
      state.failureCount = 0;
      logger.info('Circuit breaker closed after successful half-open test', { operationId });
    }
  }

  private recordFailure(operationId: string): void {
    let state = this.circuitBreakers.get(operationId);
    if (!state) {
      state = {
        state: CircuitState.CLOSED,
        failureCount: 0,
        totalRequests: 0,
        successCount: 0,
      };
      this.circuitBreakers.set(operationId, state);
    }

    state.totalRequests++;
    state.failureCount++;
    state.lastFailureTime = Date.now();

    if (state.state === CircuitState.HALF_OPEN) {
      // Failure in half-open state goes back to open
      state.state = CircuitState.OPEN;
      state.nextRetryTime = Date.now() + this.config.circuitBreakerResetTimeout;
      logger.warn('Circuit breaker opened after half-open test failure', { operationId });
    } else if (state.failureCount >= this.config.circuitBreakerThreshold) {
      // Threshold exceeded, open the circuit
      state.state = CircuitState.OPEN;
      state.nextRetryTime = Date.now() + this.config.circuitBreakerResetTimeout;
      logger.warn('Circuit breaker opened due to failure threshold', {
        operationId,
        failureCount: state.failureCount,
        threshold: this.config.circuitBreakerThreshold,
      });
    }
  }

  private recordMetrics(operationId: string, success: boolean, attempts: number, errorType: string): void {
    applicationMetrics.recordCounter('retry_operations_total', 1, {
      operation_id: operationId,
      success: success.toString(),
      error_type: errorType,
    });

    applicationMetrics.recordHistogram('retry_attempts', attempts, {
      operation_id: operationId,
      success: success.toString(),
    });

    if (!success) {
      applicationMetrics.recordCounter('retry_failures_total', 1, {
        operation_id: operationId,
        error_type: errorType,
      });
    }
  }

  // Public management methods
  setCustomBackoffCalculator(calculator: BackoffCalculator): void {
    this.customBackoffCalculator = calculator;
    logger.info('Custom backoff calculator set');
  }

  getCircuitBreakerState(operationId: string): CircuitBreakerState | undefined {
    return this.circuitBreakers.get(operationId);
  }

  resetCircuitBreaker(operationId: string): void {
    this.circuitBreakers.delete(operationId);
    logger.info('Circuit breaker reset', { operationId });
  }

  getActiveOperations(): RetryContext[] {
    return Array.from(this.activeOperations.values());
  }

  cancelOperation(operationId: string): boolean {
    const timeoutContext = this.timeoutContexts.get(operationId);
    if (timeoutContext) {
      timeoutContext.abortController?.abort();
      this.clearTimeouts(timeoutContext);
      this.timeoutContexts.delete(operationId);
      
      logger.info('Operation cancelled', { operationId });
      return true;
    }
    return false;
  }

  updateConfig(newConfig: Partial<RetryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('RetryHandler config updated', { config: this.config });
  }

  getStats(): {
    activeOperations: number;
    circuitBreakers: number;
    timeouts: number;
  } {
    return {
      activeOperations: this.activeOperations.size,
      circuitBreakers: this.circuitBreakers.size,
      timeouts: this.timeoutContexts.size,
    };
  }
}

// Utility class for timeout management
export class TimeoutManager {
  private timeouts: Map<string, TimeoutConfig> = new Map();

  setTimeouts(operationId: string, config: TimeoutConfig): void {
    this.timeouts.set(operationId, config);
  }

  getTimeouts(operationId: string): TimeoutConfig | undefined {
    return this.timeouts.get(operationId);
  }

  clearTimeouts(operationId: string): void {
    this.timeouts.delete(operationId);
  }

  async withTimeout<T>(
    operationId: string,
    operation: () => Promise<T>,
    timeoutMs?: number
  ): Promise<T> {
    const config = this.timeouts.get(operationId);
    const timeout = timeoutMs || config?.total || 30000;

    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation ${operationId} timed out after ${timeout}ms`));
      }, timeout);
    });

    return Promise.race([operation(), timeoutPromise]);
  }
}

// Global instances
export const retryHandler = new RetryHandler();
export const timeoutManager = new TimeoutManager();