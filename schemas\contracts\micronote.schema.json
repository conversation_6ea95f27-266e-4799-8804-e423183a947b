{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://custom-agents.dev/schemas/micronote.schema.json", "title": "MicroNote Schema", "description": "Schema for micro-notes in the Custom Agent System with semantic versioning support", "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the micro-note"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(?:-[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?(?:\\+[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?$", "description": "Semantic version following semver.org specification"}, "content": {"type": "object", "properties": {"text": {"type": "string", "minLength": 1, "maxLength": 5000, "description": "Primary text content of the micro-note"}, "format": {"type": "string", "enum": ["plain", "markdown", "html", "json"], "default": "markdown", "description": "Content format type"}, "language": {"type": "string", "pattern": "^[a-z]{2}(?:-[A-Z]{2})?$", "default": "en", "description": "ISO 639-1 language code with optional ISO 3166-1 country code"}, "attachments": {"type": "array", "items": {"$ref": "#/$defs/attachment"}, "maxItems": 10, "description": "File attachments associated with the note"}}, "required": ["text", "format"], "additionalProperties": false}, "metadata": {"type": "object", "properties": {"title": {"type": "string", "minLength": 1, "maxLength": 200, "description": "Optional title for the micro-note"}, "tags": {"type": "array", "items": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 50}, "uniqueItems": true, "maxItems": 20, "description": "Tags for categorization and search"}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "default": "normal", "description": "Priority level of the micro-note"}, "category": {"type": "string", "enum": ["idea", "task", "reminder", "reference", "question", "decision"], "description": "Category classification of the micro-note"}, "context": {"type": "object", "properties": {"project_id": {"type": "string", "format": "uuid", "description": "Associated project identifier"}, "agent_id": {"type": "string", "format": "uuid", "description": "Agent that created or last modified the note"}, "session_id": {"type": "string", "format": "uuid", "description": "Session context identifier"}, "location": {"$ref": "#/$defs/location_context", "description": "Location context where note was created"}}, "additionalProperties": false}}, "additionalProperties": false}, "timestamps": {"type": "object", "properties": {"created": {"type": "string", "format": "date-time", "description": "Creation timestamp in ISO 8601 format"}, "updated": {"type": "string", "format": "date-time", "description": "Last update timestamp in ISO 8601 format"}, "expires": {"type": "string", "format": "date-time", "description": "Optional expiration timestamp"}, "accessed": {"type": "string", "format": "date-time", "description": "Last access timestamp"}}, "required": ["created", "updated"], "additionalProperties": false}, "privacy": {"type": "object", "properties": {"classification": {"type": "string", "enum": ["public", "internal", "confidential", "restricted"], "default": "internal", "description": "Data classification level"}, "encryption": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "algorithm": {"type": "string", "enum": ["aes-256-gcm", "chacha20-poly1305"], "default": "aes-256-gcm"}, "key_id": {"type": "string", "description": "Reference to encryption key"}}, "required": ["enabled"], "additionalProperties": false}, "access_control": {"type": "array", "items": {"$ref": "#/$defs/access_permission"}, "description": "Access control permissions"}}, "required": ["classification"], "additionalProperties": false}, "sync": {"type": "object", "properties": {"vector_clock": {"type": "object", "patternProperties": {"^[a-f0-9-]+$": {"type": "integer", "minimum": 0}}, "additionalProperties": false, "description": "Vector clock for CRDT synchronization"}, "hash": {"type": "string", "pattern": "^[a-f0-9]{64}$", "description": "SHA-256 hash of content for integrity verification"}, "replicas": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of replica identifiers"}, "conflicts": {"type": "array", "items": {"$ref": "#/$defs/conflict_resolution"}, "description": "Conflict resolution history"}}, "required": ["vector_clock", "hash"], "additionalProperties": false}}, "required": ["id", "version", "content", "timestamps", "privacy", "sync"], "additionalProperties": false, "$defs": {"attachment": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "filename": {"type": "string", "minLength": 1, "maxLength": 255}, "mime_type": {"type": "string", "pattern": "^[a-zA-Z0-9][a-zA-Z0-9!#$&\\-\\^_]*\\/[a-zA-Z0-9][a-zA-Z0-9!#$&\\-\\^_.]*$"}, "size": {"type": "integer", "minimum": 0, "maximum": 104857600, "description": "File size in bytes (max 100MB)"}, "checksum": {"type": "string", "pattern": "^[a-f0-9]{64}$", "description": "SHA-256 checksum"}, "url": {"type": "string", "format": "uri", "description": "Storage URL or reference"}}, "required": ["id", "filename", "mime_type", "size", "checksum"], "additionalProperties": false}, "location_context": {"type": "object", "properties": {"file_path": {"type": "string", "description": "File system path context"}, "line_number": {"type": "integer", "minimum": 1, "description": "Line number in file"}, "url": {"type": "string", "format": "uri", "description": "Web URL context"}, "coordinates": {"type": "object", "properties": {"latitude": {"type": "number", "minimum": -90, "maximum": 90}, "longitude": {"type": "number", "minimum": -180, "maximum": 180}}, "required": ["latitude", "longitude"], "additionalProperties": false}}, "additionalProperties": false}, "access_permission": {"type": "object", "properties": {"principal": {"type": "string", "description": "User, group, or service identifier"}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["read", "write", "delete", "share"]}, "uniqueItems": true}, "granted": {"type": "string", "format": "date-time"}, "expires": {"type": "string", "format": "date-time"}}, "required": ["principal", "permissions", "granted"], "additionalProperties": false}, "conflict_resolution": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "strategy": {"type": "string", "enum": ["last_write_wins", "manual", "merge", "reject"]}, "resolved_by": {"type": "string", "format": "uuid", "description": "Agent or user that resolved the conflict"}, "conflicting_versions": {"type": "array", "items": {"type": "string"}, "minItems": 2}}, "required": ["timestamp", "strategy", "conflicting_versions"], "additionalProperties": false}}}