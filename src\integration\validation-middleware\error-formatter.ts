import { ErrorObject } from 'ajv';
import { ValidationError, ValidationContext, ErrorFormatter } from './types';
import { logger } from '@/utils/logger';

export class DetailedErrorFormatter implements ErrorFormatter {
  private errorCodeMap: Map<string, string> = new Map([
    ['required', 'MISSING_REQUIRED_FIELD'],
    ['type', 'INVALID_TYPE'],
    ['format', 'INVALID_FORMAT'],
    ['pattern', 'PATTERN_MISMATCH'],
    ['minimum', 'VALUE_TOO_SMALL'],
    ['maximum', 'VALUE_TOO_LARGE'],
    ['minLength', 'STRING_TOO_SHORT'],
    ['maxLength', 'STRING_TOO_LONG'],
    ['enum', 'INVALID_ENUM_VALUE'],
    ['const', 'INVALID_CONSTANT_VALUE'],
    ['uniqueItems', 'DUPLICATE_ITEMS'],
    ['additionalProperties', 'UNEXPECTED_PROPERTY'],
  ]);

  formatError(error: ErrorObject, data: any, context: ValidationContext): ValidationError {
    const field = this.getFieldPath(error);
    const code = this.errorCodeMap.get(error.keyword) || `VALIDATION_${error.keyword.toUpperCase()}`;
    
    const validationError: ValidationError = {
      field,
      message: this.generateDetailedMessage(error, data),
      value: this.getFieldValue(data, error.instancePath),
      constraint: this.getConstraintDescription(error),
      code,
      severity: this.determineSeverity(error),
      suggestions: this.generateSuggestions(error, data),
      context: {
        keyword: error.keyword,
        schemaPath: error.schemaPath,
        instancePath: error.instancePath,
        params: error.params,
        attempt: context.attempt,
        schemaId: context.schemaId,
        version: context.version,
      },
    };

    logger.debug('Validation error formatted', {
      field,
      code,
      message: validationError.message,
      attempt: context.attempt,
    });

    return validationError;
  }

  formatSummary(errors: ValidationError[]): string {
    if (errors.length === 0) return 'No validation errors';

    const errorsByField = this.groupErrorsByField(errors);
    const summary: string[] = [];

    summary.push(`Found ${errors.length} validation error(s):`);

    Object.entries(errorsByField).forEach(([field, fieldErrors]) => {
      summary.push(`\n📍 Field: ${field || 'root'}`);
      fieldErrors.forEach((error, index) => {
        const icon = error.severity === 'error' ? '❌' : error.severity === 'warning' ? '⚠️' : 'ℹ️';
        summary.push(`  ${icon} ${error.message}`);
        
        if (error.suggestions && error.suggestions.length > 0) {
          summary.push(`     💡 Suggestions: ${error.suggestions.join(', ')}`);
        }
        
        if (error.value !== undefined) {
          const valueStr = typeof error.value === 'string' 
            ? `"${error.value}"` 
            : JSON.stringify(error.value);
          summary.push(`     📄 Current value: ${valueStr}`);
        }
      });
    });

    return summary.join('\n');
  }

  generateSuggestions(error: ValidationError, data: any): string[] {
    const suggestions: string[] = [];

    switch (error.code) {
      case 'MISSING_REQUIRED_FIELD':
        suggestions.push(`Add the required field '${error.field}'`);
        suggestions.push('Check the schema documentation for required properties');
        break;

      case 'INVALID_TYPE':
        const expectedType = error.constraint;
        suggestions.push(`Convert value to type: ${expectedType}`);
        if (expectedType === 'string' && typeof error.value === 'number') {
          suggestions.push(`Use "${error.value}" instead of ${error.value}`);
        }
        break;

      case 'INVALID_FORMAT':
        if (error.constraint === 'uuid') {
          suggestions.push('Use a valid UUID format (e.g., 550e8400-e29b-41d4-a716-************)');
        } else if (error.constraint === 'date-time') {
          suggestions.push('Use ISO 8601 format (e.g., 2024-01-15T10:30:00Z)');
        } else if (error.constraint === 'email') {
          suggestions.push('Use a valid email format (e.g., <EMAIL>)');
        } else if (error.constraint === 'uri') {
          suggestions.push('Use a valid URI format (e.g., https://example.com)');
        }
        break;

      case 'PATTERN_MISMATCH':
        suggestions.push('Check the required pattern in the schema documentation');
        if (error.field.includes('version')) {
          suggestions.push('Use semantic versioning format (e.g., 1.0.0)');
        }
        break;

      case 'VALUE_TOO_SMALL':
      case 'VALUE_TOO_LARGE':
        suggestions.push(`Adjust the value to meet the constraint: ${error.constraint}`);
        break;

      case 'STRING_TOO_SHORT':
      case 'STRING_TOO_LONG':
        suggestions.push(`Adjust string length to meet the constraint: ${error.constraint}`);
        break;

      case 'INVALID_ENUM_VALUE':
        const allowedValues = this.extractEnumValues(error);
        if (allowedValues.length > 0) {
          suggestions.push(`Use one of: ${allowedValues.join(', ')}`);
        }
        break;

      case 'UNEXPECTED_PROPERTY':
        suggestions.push(`Remove the property '${error.field}' or check if it's misspelled`);
        suggestions.push('Review the schema for allowed properties');
        break;

      default:
        suggestions.push('Check the schema documentation for this field');
        suggestions.push('Verify the data format and constraints');
    }

    return suggestions;
  }

  private getFieldPath(error: ErrorObject): string {
    if (error.instancePath) {
      return error.instancePath.replace(/^\//, '').replace(/\//g, '.');
    }
    
    if (error.keyword === 'required' && error.params?.missingProperty) {
      const basePath = error.instancePath ? error.instancePath.replace(/^\//, '').replace(/\//g, '.') : '';
      return basePath ? `${basePath}.${error.params.missingProperty}` : error.params.missingProperty;
    }

    return 'root';
  }

  private getFieldValue(data: any, path: string): any {
    if (!path) return data;
    
    const pathParts = path.replace(/^\//, '').split('/');
    let current = data;
    
    for (const part of pathParts) {
      if (current === null || current === undefined) return undefined;
      current = current[part];
    }
    
    return current;
  }

  private getConstraintDescription(error: ErrorObject): string {
    switch (error.keyword) {
      case 'type':
        return error.params?.type || 'unknown type';
      case 'format':
        return error.params?.format || 'unknown format';
      case 'pattern':
        return error.params?.pattern || 'unknown pattern';
      case 'minimum':
        return `minimum: ${error.params?.limit}`;
      case 'maximum':
        return `maximum: ${error.params?.limit}`;
      case 'minLength':
        return `minimum length: ${error.params?.limit}`;
      case 'maxLength':
        return `maximum length: ${error.params?.limit}`;
      case 'enum':
        return `allowed values: ${error.params?.allowedValues?.join(', ') || 'see schema'}`;
      case 'const':
        return `must equal: ${error.params?.allowedValue}`;
      default:
        return error.message || 'validation constraint';
    }
  }

  private determineSeverity(error: ErrorObject): 'error' | 'warning' | 'info' {
    // Critical validation failures
    if (['required', 'type', 'format'].includes(error.keyword)) {
      return 'error';
    }
    
    // Constraint violations that might be recoverable
    if (['pattern', 'enum', 'const'].includes(error.keyword)) {
      return 'error';
    }
    
    // Size/length constraints
    if (['minimum', 'maximum', 'minLength', 'maxLength'].includes(error.keyword)) {
      return 'warning';
    }
    
    // Additional properties and other issues
    if (['additionalProperties', 'uniqueItems'].includes(error.keyword)) {
      return 'warning';
    }
    
    return 'error';
  }

  private generateDetailedMessage(error: ErrorObject, data: any): string {
    const field = this.getFieldPath(error);
    const value = this.getFieldValue(data, error.instancePath);
    
    switch (error.keyword) {
      case 'required':
        return `Missing required field '${error.params?.missingProperty}'`;
      
      case 'type':
        const expectedType = error.params?.type;
        const actualType = typeof value;
        return `Expected type '${expectedType}', but got '${actualType}'`;
      
      case 'format':
        const format = error.params?.format;
        return `Invalid ${format} format${value ? `: "${value}"` : ''}`;
      
      case 'pattern':
        return `Value does not match required pattern${value ? `: "${value}"` : ''}`;
      
      case 'enum':
        const allowed = error.params?.allowedValues;
        return `Invalid value${value ? ` "${value}"` : ''}. Allowed: ${allowed?.join(', ') || 'see schema'}`;
      
      case 'minimum':
        return `Value ${value} is below minimum ${error.params?.limit}`;
      
      case 'maximum':
        return `Value ${value} exceeds maximum ${error.params?.limit}`;
      
      case 'minLength':
        return `String is too short (${value?.length || 0} chars). Minimum: ${error.params?.limit}`;
      
      case 'maxLength':
        return `String is too long (${value?.length || 0} chars). Maximum: ${error.params?.limit}`;
      
      case 'additionalProperties':
        return `Unexpected property '${error.params?.additionalProperty}'`;
      
      case 'uniqueItems':
        return `Array contains duplicate items at index ${error.params?.i} and ${error.params?.j}`;
      
      default:
        return error.message || `Validation failed for keyword '${error.keyword}'`;
    }
  }

  private groupErrorsByField(errors: ValidationError[]): Record<string, ValidationError[]> {
    return errors.reduce((groups, error) => {
      const field = error.field || 'root';
      if (!groups[field]) {
        groups[field] = [];
      }
      groups[field].push(error);
      return groups;
    }, {} as Record<string, ValidationError[]>);
  }

  private extractEnumValues(error: ValidationError): string[] {
    if (error.context?.params?.allowedValues) {
      return error.context.params.allowedValues;
    }
    return [];
  }
}

export const detailedErrorFormatter = new DetailedErrorFormatter();