{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://custom-agents.dev/schemas/tool-io.schema.json", "title": "Tool I/O <PERSON>", "description": "Schema for tool input/output definitions in MCP integration with semantic versioning", "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the tool I/O definition"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(?:-[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?(?:\\+[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?$", "description": "Semantic version following semver.org specification"}, "tool_name": {"type": "string", "minLength": 1, "maxLength": 100, "pattern": "^[a-zA-Z][a-zA-Z0-9_-]*$", "description": "Name of the tool"}, "mcp_version": {"type": "string", "enum": ["1.0", "2.0"], "default": "2.0", "description": "MCP protocol version"}, "input_schema": {"type": "object", "properties": {"schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "JSON Schema definition for input validation"}, "examples": {"type": "array", "items": {"$ref": "#/$defs/io_example"}, "maxItems": 10, "description": "Example inputs for documentation and testing"}, "format": {"type": "string", "enum": ["json", "text", "binary", "multipart", "stream"], "default": "json", "description": "Input format type"}, "encoding": {"type": "string", "enum": ["utf-8", "utf-16", "ascii", "base64", "binary"], "default": "utf-8", "description": "Input encoding"}, "max_size": {"type": "integer", "minimum": 1, "maximum": 1073741824, "description": "Maximum input size in bytes (up to 1GB)"}, "compression": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "algorithm": {"type": "string", "enum": ["gzip", "deflate", "brotli", "lz4"]}, "level": {"type": "integer", "minimum": 1, "maximum": 9}}, "additionalProperties": false}}, "required": ["schema"], "additionalProperties": false}, "output_schema": {"type": "object", "properties": {"schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "JSON Schema definition for output validation"}, "examples": {"type": "array", "items": {"$ref": "#/$defs/io_example"}, "maxItems": 10, "description": "Example outputs for documentation and testing"}, "format": {"type": "string", "enum": ["json", "text", "binary", "multipart", "stream"], "default": "json", "description": "Output format type"}, "encoding": {"type": "string", "enum": ["utf-8", "utf-16", "ascii", "base64", "binary"], "default": "utf-8", "description": "Output encoding"}, "streaming": {"type": "object", "properties": {"supported": {"type": "boolean", "default": false}, "chunk_size": {"type": "integer", "minimum": 1}, "delimiter": {"type": "string"}, "content_type": {"type": "string"}}, "additionalProperties": false}}, "required": ["schema"], "additionalProperties": false}, "error_schema": {"type": "object", "properties": {"schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "JSON Schema definition for error responses"}, "error_codes": {"type": "array", "items": {"$ref": "#/$defs/error_code"}, "description": "Possible error codes and descriptions"}, "format": {"type": "string", "enum": ["json", "text"], "default": "json"}}, "required": ["schema"], "additionalProperties": false}, "capabilities": {"type": "object", "properties": {"async_execution": {"type": "boolean", "default": false, "description": "Whether tool supports asynchronous execution"}, "cancellation": {"type": "boolean", "default": false, "description": "Whether tool supports cancellation"}, "progress_reporting": {"type": "boolean", "default": false, "description": "Whether tool can report execution progress"}, "partial_results": {"type": "boolean", "default": false, "description": "Whether tool can return partial results"}, "caching": {"type": "object", "properties": {"supported": {"type": "boolean", "default": false}, "ttl_seconds": {"type": "integer", "minimum": 1}, "cache_key_fields": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "rate_limiting": {"type": "object", "properties": {"requests_per_minute": {"type": "integer", "minimum": 1}, "burst_limit": {"type": "integer", "minimum": 1}, "concurrent_limit": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}, "security": {"type": "object", "properties": {"authentication": {"type": "object", "properties": {"required": {"type": "boolean", "default": true}, "methods": {"type": "array", "items": {"type": "string", "enum": ["bearer", "api_key", "oauth2", "mtls", "hmac"]}, "uniqueItems": true}, "scopes": {"type": "array", "items": {"type": "string"}, "description": "Required OAuth2 scopes"}}, "additionalProperties": false}, "authorization": {"type": "object", "properties": {"required_permissions": {"type": "array", "items": {"type": "string"}}, "resource_access": {"type": "array", "items": {"$ref": "#/$defs/resource_permission"}}}, "additionalProperties": false}, "data_classification": {"type": "string", "enum": ["public", "internal", "confidential", "restricted"], "default": "internal"}, "encryption": {"type": "object", "properties": {"in_transit": {"type": "boolean", "default": true}, "at_rest": {"type": "boolean", "default": false}, "end_to_end": {"type": "boolean", "default": false}}, "additionalProperties": false}, "audit_logging": {"type": "boolean", "default": true, "description": "Whether tool calls should be audit logged"}}, "additionalProperties": false}, "performance": {"type": "object", "properties": {"expected_duration": {"type": "object", "properties": {"min_seconds": {"type": "number", "minimum": 0}, "max_seconds": {"type": "number", "minimum": 0}, "typical_seconds": {"type": "number", "minimum": 0}}, "additionalProperties": false}, "resource_requirements": {"type": "object", "properties": {"cpu_cores": {"type": "number", "minimum": 0.1}, "memory_mb": {"type": "integer", "minimum": 1}, "disk_mb": {"type": "integer", "minimum": 0}, "network_mbps": {"type": "number", "minimum": 0}}, "additionalProperties": false}, "scaling": {"type": "object", "properties": {"horizontal": {"type": "boolean", "default": false}, "vertical": {"type": "boolean", "default": true}, "max_instances": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "additionalProperties": false}, "metadata": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 200, "description": "Human-readable tool title"}, "description": {"type": "string", "maxLength": 2000, "description": "Detailed tool description"}, "category": {"type": "string", "enum": ["file_system", "network", "database", "ai_model", "code_analysis", "data_processing", "communication", "monitoring", "security", "integration", "utility", "custom"]}, "tags": {"type": "array", "items": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"}, "uniqueItems": true, "maxItems": 20}, "author": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "organization": {"type": "string"}, "url": {"type": "string", "format": "uri"}}, "additionalProperties": false}, "license": {"type": "string", "description": "License identifier (SPDX format preferred)"}, "documentation_url": {"type": "string", "format": "uri", "description": "URL to detailed documentation"}, "source_url": {"type": "string", "format": "uri", "description": "URL to source code repository"}}, "additionalProperties": false}, "testing": {"type": "object", "properties": {"test_cases": {"type": "array", "items": {"$ref": "#/$defs/test_case"}, "description": "Predefined test cases for validation"}, "mock_responses": {"type": "array", "items": {"$ref": "#/$defs/mock_response"}, "description": "Mock responses for testing"}, "performance_benchmarks": {"type": "array", "items": {"$ref": "#/$defs/performance_benchmark"}}}, "additionalProperties": false}, "versioning": {"type": "object", "properties": {"compatibility": {"type": "object", "properties": {"backward_compatible": {"type": "boolean"}, "forward_compatible": {"type": "boolean"}, "breaking_changes": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "deprecation": {"type": "object", "properties": {"deprecated": {"type": "boolean", "default": false}, "deprecation_date": {"type": "string", "format": "date"}, "removal_date": {"type": "string", "format": "date"}, "replacement": {"type": "string"}, "reason": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "timestamps": {"type": "object", "properties": {"created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "published": {"type": "string", "format": "date-time"}}, "required": ["created", "updated"], "additionalProperties": false}}, "required": ["id", "version", "tool_name", "input_schema", "output_schema", "timestamps"], "additionalProperties": false, "$defs": {"io_example": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "data": {"description": "Example data matching the schema"}, "expected_output": {"description": "Expected output for this input"}}, "required": ["name", "data"], "additionalProperties": false}, "error_code": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "description": {"type": "string"}, "http_status": {"type": "integer", "minimum": 100, "maximum": 599}, "recoverable": {"type": "boolean", "default": false}, "retry_after": {"type": "integer", "minimum": 0}}, "required": ["code", "message"], "additionalProperties": false}, "resource_permission": {"type": "object", "properties": {"resource_type": {"type": "string"}, "resource_id": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["read", "write", "delete", "execute", "admin"]}}}, "required": ["resource_type", "permissions"], "additionalProperties": false}, "test_case": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "input": {"description": "Test input data"}, "expected_output": {"description": "Expected output data"}, "expected_error": {"type": "string", "description": "Expected error code if test should fail"}, "setup": {"type": "string"}, "teardown": {"type": "string"}, "timeout_seconds": {"type": "integer", "minimum": 1}}, "required": ["name", "input"], "additionalProperties": false}, "mock_response": {"type": "object", "properties": {"condition": {"type": "string"}, "response": {"description": "Mock response data"}, "delay_ms": {"type": "integer", "minimum": 0}, "error": {"type": "string"}}, "required": ["condition"], "additionalProperties": false}, "performance_benchmark": {"type": "object", "properties": {"name": {"type": "string"}, "input_size": {"type": "string"}, "expected_duration_ms": {"type": "integer", "minimum": 0}, "max_memory_mb": {"type": "integer", "minimum": 0}, "throughput_ops_per_sec": {"type": "number", "minimum": 0}}, "required": ["name", "expected_duration_ms"], "additionalProperties": false}}}