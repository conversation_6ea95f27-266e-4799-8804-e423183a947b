#!/usr/bin/env tsx

import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';
import { advancedContractValidator, SemanticVersionManager } from '../src/integration/contract-validator';
import { demonstrateSchemaValidation } from '../examples/schema-validation-examples';

interface SchemaFile {
  name: string;
  path: string;
  content: any;
  valid: boolean;
  errors?: string[];
}

async function validateAllSchemas(): Promise<void> {
  console.log('🔍 Validating all JSON Schema files...\n');

  const schemaDir = join(process.cwd(), 'schemas', 'contracts');
  const schemaFiles: SchemaFile[] = [];

  try {
    const files = readdirSync(schemaDir).filter(file => file.endsWith('.schema.json'));
    
    for (const file of files) {
      const filePath = join(schemaDir, file);
      const content = JSON.parse(readFileSync(filePath, 'utf-8'));
      
      console.log(`📄 Validating ${file}...`);
      
      // Validate that it's a valid JSON Schema
      const isValidSchema = validateJsonSchema(content);
      
      // Check semantic version if present
      const hasValidVersion = content.version ? 
        SemanticVersionManager.parseVersion(content.version) !== null : true;
      
      // Check schema structure
      const structureValid = validateSchemaStructure(content);
      
      const schemaFile: SchemaFile = {
        name: file,
        path: filePath,
        content,
        valid: isValidSchema && hasValidVersion && structureValid,
        errors: []
      };

      if (!isValidSchema) {
        schemaFile.errors!.push('Invalid JSON Schema format');
      }
      if (!hasValidVersion) {
        schemaFile.errors!.push('Invalid semantic version');
      }
      if (!structureValid) {
        schemaFile.errors!.push('Invalid schema structure');
      }

      schemaFiles.push(schemaFile);
      
      if (schemaFile.valid) {
        console.log(`  ✅ ${file} is valid`);
      } else {
        console.log(`  ❌ ${file} has errors:`, schemaFile.errors);
      }
    }

    // Summary
    console.log('\n📊 Validation Summary:');
    console.log(`Total schemas: ${schemaFiles.length}`);
    console.log(`Valid schemas: ${schemaFiles.filter(s => s.valid).length}`);
    console.log(`Invalid schemas: ${schemaFiles.filter(s => !s.valid).length}`);

    // Schema compatibility checks
    console.log('\n🔄 Checking schema compatibility...');
    await checkSchemaCompatibility(schemaFiles);

    // Run examples
    console.log('\n🧪 Running validation examples...');
    await demonstrateSchemaValidation();

  } catch (error) {
    console.error('❌ Error validating schemas:', error);
    process.exit(1);
  }
}

function validateJsonSchema(schema: any): boolean {
  try {
    // Basic JSON Schema validation
    if (!schema.$schema) return false;
    if (!schema.$id) return false;
    if (!schema.title) return false;
    if (!schema.type) return false;
    
    // Check for required draft 2020-12 format
    if (!schema.$schema.includes('2020-12')) {
      console.warn('  ⚠️  Schema should use JSON Schema Draft 2020-12');
    }

    return true;
  } catch (error) {
    return false;
  }
}

function validateSchemaStructure(schema: any): boolean {
  try {
    // Check for semantic versioning in content if schema has version property
    if (schema.properties && schema.properties.version) {
      const versionProperty = schema.properties.version;
      if (!versionProperty.pattern || !versionProperty.pattern.includes('\\d+\\.\\d+\\.\\d+')) {
        console.warn('  ⚠️  Version property should enforce semantic versioning');
      }
    }

    // Check for proper UUID format validation if ID fields exist
    if (schema.properties && schema.properties.id) {
      const idProperty = schema.properties.id;
      if (idProperty.format !== 'uuid') {
        console.warn('  ⚠️  ID property should use UUID format');
      }
    }

    // Check for timestamp properties
    const hasTimestamps = schema.properties && 
      (schema.properties.timestamps || 
       (schema.properties.created && schema.properties.updated));
    
    if (!hasTimestamps) {
      console.warn('  ⚠️  Schema should include timestamp properties');
    }

    return true;
  } catch (error) {
    return false;
  }
}

async function checkSchemaCompatibility(schemas: SchemaFile[]): Promise<void> {
  // Check for circular references
  schemas.forEach(schema => {
    if (hasCircularReferences(schema.content)) {
      console.warn(`  ⚠️  ${schema.name} may have circular references`);
    }
  });

  // Check for consistent property naming
  const propertyNames: Set<string> = new Set();
  schemas.forEach(schema => {
    if (schema.content.properties) {
      Object.keys(schema.content.properties).forEach(prop => {
        propertyNames.add(prop);
      });
    }
  });

  // Look for potential naming inconsistencies
  const namingIssues = findNamingInconsistencies(Array.from(propertyNames));
  if (namingIssues.length > 0) {
    console.warn('  ⚠️  Potential naming inconsistencies found:');
    namingIssues.forEach(issue => console.warn(`    - ${issue}`));
  }

  console.log('  ✅ Compatibility checks completed');
}

function hasCircularReferences(schema: any, visited: Set<string> = new Set()): boolean {
  if (typeof schema !== 'object' || schema === null) {
    return false;
  }

  if (schema.$ref) {
    if (visited.has(schema.$ref)) {
      return true;
    }
    visited.add(schema.$ref);
  }

  for (const value of Object.values(schema)) {
    if (hasCircularReferences(value, new Set(visited))) {
      return true;
    }
  }

  return false;
}

function findNamingInconsistencies(propertyNames: string[]): string[] {
  const issues: string[] = [];
  
  // Check for snake_case vs camelCase inconsistencies
  const snakeCase = propertyNames.filter(name => name.includes('_'));
  const camelCase = propertyNames.filter(name => /[a-z][A-Z]/.test(name));
  
  if (snakeCase.length > 0 && camelCase.length > 0) {
    issues.push('Mixed naming conventions: snake_case and camelCase');
  }

  // Check for similar names that might be typos
  for (let i = 0; i < propertyNames.length; i++) {
    for (let j = i + 1; j < propertyNames.length; j++) {
      const name1 = propertyNames[i];
      const name2 = propertyNames[j];
      
      if (levenshteinDistance(name1, name2) <= 2 && name1 !== name2) {
        issues.push(`Similar property names: '${name1}' and '${name2}'`);
      }
    }
  }

  return issues;
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

// Run the validation if this script is executed directly
if (require.main === module) {
  validateAllSchemas().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { validateAllSchemas };