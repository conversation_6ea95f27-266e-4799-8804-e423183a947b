{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://custom-agents.dev/schemas/mcp.schema.json", "title": "MCP Protocol Contract Schema", "description": "Schema for Model Context Protocol contracts", "type": "object", "properties": {"protocol_version": {"type": "string", "enum": ["2.0"], "description": "MCP protocol version"}, "server": {"$ref": "#/$defs/server_info", "description": "MCP server information"}, "tools": {"type": "array", "items": {"$ref": "#/$defs/tool"}, "description": "Available tools"}, "resources": {"type": "array", "items": {"$ref": "#/$defs/resource"}, "description": "Available resources"}, "prompts": {"type": "array", "items": {"$ref": "#/$defs/prompt"}, "description": "Available prompts"}, "authentication": {"$ref": "#/$defs/authentication", "description": "Authentication configuration"}}, "required": ["protocol_version", "server"], "additionalProperties": false, "$defs": {"server_info": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "description": "Server name"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Server version"}, "description": {"type": "string", "description": "Server description"}, "endpoint": {"type": "string", "format": "uri", "description": "Server endpoint URL"}, "health_check": {"type": "string", "format": "uri", "description": "Health check endpoint"}}, "required": ["name", "version", "endpoint"], "additionalProperties": false}, "tool": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "description": "Tool name"}, "description": {"type": "string", "description": "Tool description"}, "input_schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "Input schema"}, "output_schema": {"$ref": "https://json-schema.org/draft/2020-12/schema", "description": "Output schema"}, "rate_limit": {"type": "object", "properties": {"requests_per_minute": {"type": "integer", "minimum": 1}, "burst_limit": {"type": "integer", "minimum": 1}}, "additionalProperties": false}}, "required": ["name", "description", "input_schema"], "additionalProperties": false}, "resource": {"type": "object", "properties": {"uri": {"type": "string", "format": "uri", "description": "Resource URI"}, "name": {"type": "string", "description": "Resource name"}, "description": {"type": "string", "description": "Resource description"}, "mime_type": {"type": "string", "description": "MIME type of the resource"}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["read", "write", "delete"]}}}, "required": ["uri", "name"], "additionalProperties": false}, "prompt": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "description": "Prompt name"}, "description": {"type": "string", "description": "Prompt description"}, "arguments": {"type": "array", "items": {"$ref": "#/$defs/prompt_argument"}}}, "required": ["name"], "additionalProperties": false}, "prompt_argument": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}, "required": {"type": "boolean", "default": false}}, "required": ["name"], "additionalProperties": false}, "authentication": {"type": "object", "properties": {"type": {"type": "string", "enum": ["none", "bearer", "oauth2", "mtls"], "default": "none"}, "oauth2": {"type": "object", "properties": {"authorization_url": {"type": "string", "format": "uri"}, "token_url": {"type": "string", "format": "uri"}, "scopes": {"type": "array", "items": {"type": "string"}}}, "required": ["authorization_url", "token_url"], "additionalProperties": false}}, "required": ["type"], "additionalProperties": false}}}