import pino from 'pino';

export interface LoggerConfig {
  level?: string;
  pretty?: boolean;
  service?: string;
}

function createLogger(config: LoggerConfig = {}) {
  const logLevel = config.level || process.env.LOG_LEVEL || 'info';
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const baseConfig: pino.LoggerOptions = {
    level: logLevel,
    name: config.service || 'custom-agent-system',
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level(label) {
        return { level: label };
      },
    },
    serializers: {
      err: pino.stdSerializers.err,
      req: pino.stdSerializers.req,
      res: pino.stdSerializers.res,
    },
  };

  if (isDevelopment || config.pretty) {
    return pino(baseConfig, pino.destination({
      sync: false,
    }));
  }

  return pino(baseConfig);
}

export const logger = createLogger();