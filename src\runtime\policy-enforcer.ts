import { FastifyRequest } from 'fastify';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface PolicyRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number; // Lower number = higher priority
  conditions: PolicyCondition[];
  action: PolicyAction;
  metadata?: Record<string, any>;
}

export interface PolicyCondition {
  type: ConditionType;
  field: string;
  operator: ConditionOperator;
  value: any;
  caseSensitive?: boolean;
  negate?: boolean;
}

export enum ConditionType {
  REQUEST_HEADER = 'request_header',
  REQUEST_PATH = 'request_path',
  REQUEST_METHOD = 'request_method',
  REQUEST_BODY = 'request_body',
  REQUEST_QUERY = 'request_query',
  SESSION_ATTRIBUTE = 'session_attribute',
  USER_ATTRIBUTE = 'user_attribute',
  TIME_OF_DAY = 'time_of_day',
  DAY_OF_WEEK = 'day_of_week',
  RATE_LIMIT = 'rate_limit',
  RESOURCE_QUOTA = 'resource_quota',
  CUSTOM = 'custom',
}

export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  STARTS_WITH = 'starts_with',
  ENDS_WITH = 'ends_with',
  REGEX_MATCH = 'regex_match',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  GREATER_EQUAL = 'greater_equal',
  LESS_EQUAL = 'less_equal',
  IN_LIST = 'in_list',
  NOT_IN_LIST = 'not_in_list',
  EXISTS = 'exists',
  NOT_EXISTS = 'not_exists',
}

export interface PolicyAction {
  type: ActionType;
  allow: boolean;
  reason?: string;
  metadata?: Record<string, any>;
  throttle?: {
    delay: number; // milliseconds
    maxRequests: number;
    windowMs: number;
  };
  redirect?: {
    url: string;
    statusCode: number;
  };
  customResponse?: {
    statusCode: number;
    body: any;
    headers?: Record<string, string>;
  };
}

export enum ActionType {
  ALLOW = 'allow',
  DENY = 'deny',
  WARN = 'warn',
  THROTTLE = 'throttle',
  REDIRECT = 'redirect',
  CUSTOM_RESPONSE = 'custom_response',
  LOG_ONLY = 'log_only',
}

export interface PolicyEvaluationResult {
  allowed: boolean;
  action: ActionType;
  reason: string;
  appliedRules: string[];
  warnings: string[];
  metadata?: Record<string, any>;
  executionTime: number;
}

export interface PolicyContext {
  request: FastifyRequest;
  sessionId?: string;
  userId?: string;
  userAttributes?: Record<string, any>;
  sessionAttributes?: Record<string, any>;
  timestamp: Date;
}

export interface RateLimitState {
  requests: number;
  firstRequest: Date;
  lastRequest: Date;
}

export interface PolicyEnforcerConfig {
  enabled: boolean;
  defaultAction: ActionType;
  logViolations: boolean;
  metricsEnabled: boolean;
  cacheResults: boolean;
  cacheTtlMs: number;
  maxExecutionTimeMs: number;
}

export class PolicyEnforcer {
  private rules: Map<string, PolicyRule> = new Map();
  private rateLimitStates: Map<string, RateLimitState> = new Map();
  private resultCache: Map<string, { result: PolicyEvaluationResult; timestamp: number }> = new Map();
  private config: PolicyEnforcerConfig;
  private customEvaluators: Map<string, (context: PolicyContext, condition: PolicyCondition) => boolean> = new Map();

  constructor(config?: Partial<PolicyEnforcerConfig>) {
    this.config = {
      enabled: true,
      defaultAction: ActionType.ALLOW,
      logViolations: true,
      metricsEnabled: true,
      cacheResults: true,
      cacheTtlMs: 60000, // 1 minute
      maxExecutionTimeMs: 1000, // 1 second
      ...config,
    };

    this.initializeDefaultRules();
    this.registerDefaultEvaluators();
    
    logger.info('PolicyEnforcer initialized', { config: this.config });
  }

  private initializeDefaultRules(): void {
    // Security headers rule
    this.addRule({
      id: 'security-headers',
      name: 'Require Security Headers',
      description: 'Ensure required security headers are present',
      enabled: true,
      priority: 100,
      conditions: [
        {
          type: ConditionType.REQUEST_PATH,
          field: 'path',
          operator: ConditionOperator.STARTS_WITH,
          value: '/api/',
        },
        {
          type: ConditionType.REQUEST_HEADER,
          field: 'x-api-key',
          operator: ConditionOperator.NOT_EXISTS,
          value: null,
        },
      ],
      action: {
        type: ActionType.DENY,
        allow: false,
        reason: 'API key required for API endpoints',
      },
    });

    // Rate limiting rule
    this.addRule({
      id: 'global-rate-limit',
      name: 'Global Rate Limit',
      description: 'Limit requests per client',
      enabled: true,
      priority: 50,
      conditions: [
        {
          type: ConditionType.RATE_LIMIT,
          field: 'client_ip',
          operator: ConditionOperator.GREATER_THAN,
          value: 100, // requests per minute
        },
      ],
      action: {
        type: ActionType.THROTTLE,
        allow: false,
        reason: 'Rate limit exceeded',
        throttle: {
          delay: 1000,
          maxRequests: 100,
          windowMs: 60000,
        },
      },
    });

    // Method restriction rule
    this.addRule({
      id: 'method-restrictions',
      name: 'HTTP Method Restrictions',
      description: 'Restrict certain HTTP methods',
      enabled: true,
      priority: 75,
      conditions: [
        {
          type: ConditionType.REQUEST_METHOD,
          field: 'method',
          operator: ConditionOperator.IN_LIST,
          value: ['TRACE', 'CONNECT', 'OPTIONS'],
        },
      ],
      action: {
        type: ActionType.DENY,
        allow: false,
        reason: 'HTTP method not allowed',
      },
    });

    // Resource quota rule
    this.addRule({
      id: 'resource-quota',
      name: 'Resource Quota Enforcement',
      description: 'Enforce resource usage quotas',
      enabled: true,
      priority: 25,
      conditions: [
        {
          type: ConditionType.RESOURCE_QUOTA,
          field: 'daily_tokens',
          operator: ConditionOperator.GREATER_THAN,
          value: 100000,
        },
      ],
      action: {
        type: ActionType.DENY,
        allow: false,
        reason: 'Daily token quota exceeded',
      },
    });
  }

  private registerDefaultEvaluators(): void {
    // Rate limit evaluator
    this.customEvaluators.set('rate_limit', (context: PolicyContext, condition: PolicyCondition) => {
      const clientKey = context.request.ip;
      const now = new Date();
      const windowMs = 60000; // 1 minute
      
      let state = this.rateLimitStates.get(clientKey);
      
      if (!state) {
        state = {
          requests: 1,
          firstRequest: now,
          lastRequest: now,
        };
        this.rateLimitStates.set(clientKey, state);
        return false; // First request, no limit exceeded
      }

      // Reset if window expired
      if (now.getTime() - state.firstRequest.getTime() > windowMs) {
        state.requests = 1;
        state.firstRequest = now;
        state.lastRequest = now;
        return false;
      }

      state.requests++;
      state.lastRequest = now;

      return state.requests > condition.value;
    });

    // Resource quota evaluator
    this.customEvaluators.set('resource_quota', (context: PolicyContext, condition: PolicyCondition) => {
      // Mock implementation - in real system, this would check actual quotas
      const sessionAttributes = context.sessionAttributes || {};
      const quotaField = condition.field;
      const currentUsage = sessionAttributes[quotaField] || 0;
      
      switch (condition.operator) {
        case ConditionOperator.GREATER_THAN:
          return currentUsage > condition.value;
        case ConditionOperator.GREATER_EQUAL:
          return currentUsage >= condition.value;
        case ConditionOperator.LESS_THAN:
          return currentUsage < condition.value;
        case ConditionOperator.LESS_EQUAL:
          return currentUsage <= condition.value;
        default:
          return false;
      }
    });

    // Time-based evaluator
    this.customEvaluators.set('time_of_day', (context: PolicyContext, condition: PolicyCondition) => {
      const currentHour = context.timestamp.getHours();
      
      switch (condition.operator) {
        case ConditionOperator.GREATER_THAN:
          return currentHour > condition.value;
        case ConditionOperator.LESS_THAN:
          return currentHour < condition.value;
        case ConditionOperator.IN_LIST:
          return Array.isArray(condition.value) && condition.value.includes(currentHour);
        default:
          return false;
      }
    });
  }

  async enforce(request: FastifyRequest): Promise<PolicyEvaluationResult> {
    const startTime = Date.now();

    if (!this.config.enabled) {
      return {
        allowed: true,
        action: ActionType.ALLOW,
        reason: 'Policy enforcement disabled',
        appliedRules: [],
        warnings: [],
        executionTime: Date.now() - startTime,
      };
    }

    const context: PolicyContext = {
      request,
      sessionId: this.extractSessionId(request),
      userId: this.extractUserId(request),
      userAttributes: this.extractUserAttributes(request),
      sessionAttributes: this.extractSessionAttributes(request),
      timestamp: new Date(),
    };

    // Check cache first
    if (this.config.cacheResults) {
      const cacheKey = this.generateCacheKey(context);
      const cached = this.resultCache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.config.cacheTtlMs) {
        cached.result.executionTime = Date.now() - startTime;
        return cached.result;
      }
    }

    try {
      const result = await this.evaluateRules(context, startTime);
      
      // Cache result
      if (this.config.cacheResults) {
        const cacheKey = this.generateCacheKey(context);
        this.resultCache.set(cacheKey, {
          result,
          timestamp: Date.now(),
        });
      }

      // Log violations
      if (this.config.logViolations && !result.allowed) {
        logger.warn('Policy violation detected', {
          reason: result.reason,
          rules: result.appliedRules,
          path: request.url,
          method: request.method,
          ip: request.ip,
          sessionId: context.sessionId,
          userId: context.userId,
        });
      }

      // Record metrics
      if (this.config.metricsEnabled) {
        applicationMetrics.recordCounter('policy_evaluations_total', 1, {
          action: result.action,
          allowed: result.allowed.toString(),
        });

        applicationMetrics.recordHistogram('policy_evaluation_duration_ms', result.executionTime, {
          action: result.action,
        });

        if (!result.allowed) {
          applicationMetrics.recordCounter('policy_violations_total', 1, {
            reason: result.reason,
          });
        }
      }

      return result;

    } catch (error) {
      logger.error('Error during policy evaluation', { error, context });
      
      return {
        allowed: this.config.defaultAction === ActionType.ALLOW,
        action: this.config.defaultAction,
        reason: 'Policy evaluation error',
        appliedRules: [],
        warnings: [`Evaluation error: ${error instanceof Error ? error.message : 'Unknown error'}`],
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async evaluateRules(context: PolicyContext, startTime: number): Promise<PolicyEvaluationResult> {
    const appliedRules: string[] = [];
    const warnings: string[] = [];
    let finalAction = ActionType.ALLOW;
    let finalReason = 'All policies passed';
    let allowed = true;

    // Sort rules by priority (lower number = higher priority)
    const sortedRules = Array.from(this.rules.values())
      .filter(rule => rule.enabled)
      .sort((a, b) => a.priority - b.priority);

    for (const rule of sortedRules) {
      // Check execution timeout
      if (Date.now() - startTime > this.config.maxExecutionTimeMs) {
        warnings.push('Policy evaluation timeout exceeded');
        break;
      }

      try {
        const ruleResult = await this.evaluateRule(rule, context);
        
        if (ruleResult.matches) {
          appliedRules.push(rule.id);
          
          switch (rule.action.type) {
            case ActionType.DENY:
              allowed = false;
              finalAction = ActionType.DENY;
              finalReason = rule.action.reason || `Rule ${rule.name} violated`;
              // Stop processing on deny (highest priority action)
              return {
                allowed,
                action: finalAction,
                reason: finalReason,
                appliedRules,
                warnings,
                executionTime: Date.now() - startTime,
                metadata: rule.action.metadata,
              };

            case ActionType.WARN:
              warnings.push(rule.action.reason || `Warning from rule ${rule.name}`);
              break;

            case ActionType.THROTTLE:
              if (allowed) { // Only apply if not already denied
                allowed = false;
                finalAction = ActionType.THROTTLE;
                finalReason = rule.action.reason || `Throttled by rule ${rule.name}`;
              }
              break;

            case ActionType.REDIRECT:
            case ActionType.CUSTOM_RESPONSE:
              if (allowed) { // Only apply if not already denied/throttled
                allowed = false;
                finalAction = rule.action.type;
                finalReason = rule.action.reason || `Action ${rule.action.type} from rule ${rule.name}`;
              }
              break;

            case ActionType.LOG_ONLY:
              logger.info('Policy rule matched (log only)', {
                rule: rule.name,
                context: {
                  path: context.request.url,
                  method: context.request.method,
                  sessionId: context.sessionId,
                },
              });
              break;

            case ActionType.ALLOW:
            default:
              // Continue processing
              break;
          }
        }
      } catch (error) {
        warnings.push(`Error evaluating rule ${rule.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        logger.error('Rule evaluation error', { rule: rule.name, error });
      }
    }

    return {
      allowed,
      action: finalAction,
      reason: finalReason,
      appliedRules,
      warnings,
      executionTime: Date.now() - startTime,
    };
  }

  private async evaluateRule(rule: PolicyRule, context: PolicyContext): Promise<{ matches: boolean }> {
    // All conditions must be true for rule to match (AND logic)
    for (const condition of rule.conditions) {
      const conditionResult = await this.evaluateCondition(condition, context);
      
      if (!conditionResult) {
        return { matches: false };
      }
    }

    return { matches: true };
  }

  private async evaluateCondition(condition: PolicyCondition, context: PolicyContext): Promise<boolean> {
    let value: any;
    let result: boolean;

    // Extract value based on condition type
    switch (condition.type) {
      case ConditionType.REQUEST_HEADER:
        value = context.request.headers[condition.field.toLowerCase()];
        break;
      case ConditionType.REQUEST_PATH:
        value = context.request.url;
        break;
      case ConditionType.REQUEST_METHOD:
        value = context.request.method;
        break;
      case ConditionType.REQUEST_BODY:
        value = (context.request.body as any)?.[condition.field];
        break;
      case ConditionType.REQUEST_QUERY:
        value = (context.request.query as any)?.[condition.field];
        break;
      case ConditionType.SESSION_ATTRIBUTE:
        value = context.sessionAttributes?.[condition.field];
        break;
      case ConditionType.USER_ATTRIBUTE:
        value = context.userAttributes?.[condition.field];
        break;
      case ConditionType.TIME_OF_DAY:
      case ConditionType.DAY_OF_WEEK:
      case ConditionType.RATE_LIMIT:
      case ConditionType.RESOURCE_QUOTA:
      case ConditionType.CUSTOM:
        const evaluator = this.customEvaluators.get(condition.type);
        if (evaluator) {
          result = evaluator(context, condition);
          return condition.negate ? !result : result;
        }
        return false;
      default:
        return false;
    }

    // Apply operator
    result = this.applyOperator(value, condition.operator, condition.value, condition.caseSensitive);
    
    // Apply negation if specified
    return condition.negate ? !result : result;
  }

  private applyOperator(value: any, operator: ConditionOperator, expectedValue: any, caseSensitive = true): boolean {
    // Handle case sensitivity for string operations
    const normalizeString = (str: any): string => {
      if (typeof str !== 'string') return String(str || '');
      return caseSensitive ? str : str.toLowerCase();
    };

    const normalizedValue = normalizeString(value);
    const normalizedExpected = normalizeString(expectedValue);

    switch (operator) {
      case ConditionOperator.EQUALS:
        return normalizedValue === normalizedExpected;
      case ConditionOperator.NOT_EQUALS:
        return normalizedValue !== normalizedExpected;
      case ConditionOperator.CONTAINS:
        return normalizedValue.includes(normalizedExpected);
      case ConditionOperator.NOT_CONTAINS:
        return !normalizedValue.includes(normalizedExpected);
      case ConditionOperator.STARTS_WITH:
        return normalizedValue.startsWith(normalizedExpected);
      case ConditionOperator.ENDS_WITH:
        return normalizedValue.endsWith(normalizedExpected);
      case ConditionOperator.REGEX_MATCH:
        try {
          const regex = new RegExp(expectedValue, caseSensitive ? 'g' : 'gi');
          return regex.test(normalizedValue);
        } catch {
          return false;
        }
      case ConditionOperator.GREATER_THAN:
        return Number(value) > Number(expectedValue);
      case ConditionOperator.LESS_THAN:
        return Number(value) < Number(expectedValue);
      case ConditionOperator.GREATER_EQUAL:
        return Number(value) >= Number(expectedValue);
      case ConditionOperator.LESS_EQUAL:
        return Number(value) <= Number(expectedValue);
      case ConditionOperator.IN_LIST:
        return Array.isArray(expectedValue) && expectedValue.includes(value);
      case ConditionOperator.NOT_IN_LIST:
        return Array.isArray(expectedValue) && !expectedValue.includes(value);
      case ConditionOperator.EXISTS:
        return value !== undefined && value !== null;
      case ConditionOperator.NOT_EXISTS:
        return value === undefined || value === null;
      default:
        return false;
    }
  }

  addRule(rule: PolicyRule): void {
    this.rules.set(rule.id, rule);
    logger.info('Policy rule added', { ruleId: rule.id, ruleName: rule.name });
  }

  removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      logger.info('Policy rule removed', { ruleId });
    }
    return removed;
  }

  updateRule(ruleId: string, updates: Partial<PolicyRule>): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return false;
    }

    const updatedRule = { ...rule, ...updates };
    this.rules.set(ruleId, updatedRule);
    logger.info('Policy rule updated', { ruleId, updates });
    return true;
  }

  getRule(ruleId: string): PolicyRule | undefined {
    return this.rules.get(ruleId);
  }

  getAllRules(): PolicyRule[] {
    return Array.from(this.rules.values());
  }

  registerCustomEvaluator(type: string, evaluator: (context: PolicyContext, condition: PolicyCondition) => boolean): void {
    this.customEvaluators.set(type, evaluator);
    logger.info('Custom policy evaluator registered', { type });
  }

  private extractSessionId(request: FastifyRequest): string | undefined {
    return request.headers['x-session-id'] as string ||
           (request.query as any)?.sessionId;
  }

  private extractUserId(request: FastifyRequest): string | undefined {
    return request.headers['x-user-id'] as string ||
           (request.query as any)?.userId;
  }

  private extractUserAttributes(request: FastifyRequest): Record<string, any> | undefined {
    // In a real implementation, this would fetch user attributes from a database or cache
    return {
      ip: request.ip,
      userAgent: request.headers['user-agent'],
    };
  }

  private extractSessionAttributes(request: FastifyRequest): Record<string, any> | undefined {
    // In a real implementation, this would fetch session attributes from session storage
    return {};
  }

  private generateCacheKey(context: PolicyContext): string {
    return `${context.request.method}:${context.request.url}:${context.userId || 'anonymous'}:${context.sessionId || 'no-session'}`;
  }

  clearCache(): void {
    this.resultCache.clear();
    logger.info('Policy result cache cleared');
  }

  getRuleStats(): Record<string, { evaluations: number; violations: number }> {
    // In a real implementation, this would return actual statistics
    const stats: Record<string, { evaluations: number; violations: number }> = {};
    
    this.rules.forEach((rule) => {
      stats[rule.id] = {
        evaluations: 0,
        violations: 0,
      };
    });

    return stats;
  }
}