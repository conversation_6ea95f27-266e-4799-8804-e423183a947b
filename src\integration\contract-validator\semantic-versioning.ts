import { logger } from '@/utils/logger';

export interface SemanticVersion {
  major: number;
  minor: number;
  patch: number;
  prerelease?: string;
  build?: string;
}

export interface VersionCompatibility {
  isCompatible: boolean;
  reason?: string;
  migrationRequired: boolean;
  breakingChanges: string[];
}

export class SemanticVersionManager {
  private static readonly SEMVER_REGEX = /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*))?(?:\+([a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*))?$/;

  static parseVersion(versionString: string): SemanticVersion | null {
    const match = versionString.match(this.SEMVER_REGEX);
    if (!match) {
      logger.warn('Invalid semantic version format', { version: versionString });
      return null;
    }

    return {
      major: parseInt(match[1], 10),
      minor: parseInt(match[2], 10),
      patch: parseInt(match[3], 10),
      prerelease: match[4],
      build: match[5],
    };
  }

  static compareVersions(version1: string, version2: string): number {
    const v1 = this.parseVersion(version1);
    const v2 = this.parseVersion(version2);

    if (!v1 || !v2) {
      throw new Error('Invalid version format for comparison');
    }

    // Compare major.minor.patch
    if (v1.major !== v2.major) return v1.major - v2.major;
    if (v1.minor !== v2.minor) return v1.minor - v2.minor;
    if (v1.patch !== v2.patch) return v1.patch - v2.patch;

    // Handle prerelease versions
    if (v1.prerelease && !v2.prerelease) return -1;
    if (!v1.prerelease && v2.prerelease) return 1;
    if (v1.prerelease && v2.prerelease) {
      return v1.prerelease.localeCompare(v2.prerelease);
    }

    return 0;
  }

  static isCompatible(currentVersion: string, requiredVersion: string): VersionCompatibility {
    const current = this.parseVersion(currentVersion);
    const required = this.parseVersion(requiredVersion);

    if (!current || !required) {
      return {
        isCompatible: false,
        reason: 'Invalid version format',
        migrationRequired: false,
        breakingChanges: [],
      };
    }

    const breakingChanges: string[] = [];
    let migrationRequired = false;

    // Major version differences indicate breaking changes
    if (current.major !== required.major) {
      breakingChanges.push(`Major version mismatch: ${current.major} vs ${required.major}`);
      migrationRequired = true;
    }

    // Minor version backwards compatibility
    if (current.major === required.major && current.minor < required.minor) {
      migrationRequired = true;
    }

    // Patch version compatibility
    const isCompatible = current.major === required.major && 
                        current.minor >= required.minor &&
                        (current.minor > required.minor || current.patch >= required.patch);

    return {
      isCompatible,
      reason: isCompatible ? undefined : `Version ${currentVersion} is not compatible with ${requiredVersion}`,
      migrationRequired,
      breakingChanges,
    };
  }

  static getNextVersion(currentVersion: string, changeType: 'major' | 'minor' | 'patch'): string {
    const version = this.parseVersion(currentVersion);
    if (!version) {
      throw new Error('Invalid current version format');
    }

    switch (changeType) {
      case 'major':
        return `${version.major + 1}.0.0`;
      case 'minor':
        return `${version.major}.${version.minor + 1}.0`;
      case 'patch':
        return `${version.major}.${version.minor}.${version.patch + 1}`;
      default:
        throw new Error(`Invalid change type: ${changeType}`);
    }
  }

  static isBreakingChange(oldSchema: any, newSchema: any): boolean {
    // Simplified breaking change detection
    // In a real implementation, this would be more sophisticated
    return this.hasStructuralChanges(oldSchema, newSchema) ||
           this.hasRequiredFieldChanges(oldSchema, newSchema) ||
           this.hasTypeChanges(oldSchema, newSchema);
  }

  private static hasStructuralChanges(oldSchema: any, newSchema: any): boolean {
    const oldProperties = Object.keys(oldSchema.properties || {});
    const newProperties = Object.keys(newSchema.properties || {});
    
    // Removing properties is a breaking change
    return oldProperties.some(prop => !newProperties.includes(prop));
  }

  private static hasRequiredFieldChanges(oldSchema: any, newSchema: any): boolean {
    const oldRequired = oldSchema.required || [];
    const newRequired = newSchema.required || [];
    
    // Adding required fields is a breaking change
    return newRequired.some((field: string) => !oldRequired.includes(field));
  }

  private static hasTypeChanges(oldSchema: any, newSchema: any): boolean {
    // Check if fundamental types have changed
    if (oldSchema.type !== newSchema.type) {
      return true;
    }
    
    // Check property type changes
    const oldProps = oldSchema.properties || {};
    const newProps = newSchema.properties || {};
    
    return Object.keys(oldProps).some(prop => {
      if (newProps[prop] && oldProps[prop].type !== newProps[prop].type) {
        return true;
      }
      return false;
    });
  }
}

export interface SchemaVersionInfo {
  schemaId: string;
  version: string;
  checksum: string;
  createdAt: Date;
  deprecated?: boolean;
  deprecationDate?: Date;
  migrationPath?: string[];
}

export class SchemaVersionRegistry {
  private versions: Map<string, SchemaVersionInfo[]> = new Map();

  registerSchemaVersion(info: SchemaVersionInfo): void {
    const existing = this.versions.get(info.schemaId) || [];
    
    // Check for duplicate versions
    if (existing.some(v => v.version === info.version)) {
      throw new Error(`Schema version ${info.version} already exists for ${info.schemaId}`);
    }

    existing.push(info);
    existing.sort((a, b) => SemanticVersionManager.compareVersions(a.version, b.version));
    
    this.versions.set(info.schemaId, existing);
    
    logger.info('Schema version registered', {
      schemaId: info.schemaId,
      version: info.version,
    });
  }

  getLatestVersion(schemaId: string): SchemaVersionInfo | undefined {
    const versions = this.versions.get(schemaId);
    if (!versions || versions.length === 0) {
      return undefined;
    }

    // Return the latest non-deprecated version, or the latest if all are deprecated
    const nonDeprecated = versions.filter(v => !v.deprecated);
    return nonDeprecated.length > 0 ? nonDeprecated[nonDeprecated.length - 1] : versions[versions.length - 1];
  }

  getVersion(schemaId: string, version: string): SchemaVersionInfo | undefined {
    const versions = this.versions.get(schemaId);
    return versions?.find(v => v.version === version);
  }

  getAllVersions(schemaId: string): SchemaVersionInfo[] {
    return this.versions.get(schemaId) || [];
  }

  checkCompatibility(schemaId: string, fromVersion: string, toVersion: string): VersionCompatibility {
    const versions = this.versions.get(schemaId);
    if (!versions) {
      return {
        isCompatible: false,
        reason: `Schema ${schemaId} not found`,
        migrationRequired: false,
        breakingChanges: [],
      };
    }

    const from = versions.find(v => v.version === fromVersion);
    const to = versions.find(v => v.version === toVersion);

    if (!from || !to) {
      return {
        isCompatible: false,
        reason: 'One or both versions not found',
        migrationRequired: false,
        breakingChanges: [],
      };
    }

    return SemanticVersionManager.isCompatible(fromVersion, toVersion);
  }

  deprecateVersion(schemaId: string, version: string, deprecationDate?: Date): void {
    const versions = this.versions.get(schemaId);
    const versionInfo = versions?.find(v => v.version === version);
    
    if (versionInfo) {
      versionInfo.deprecated = true;
      versionInfo.deprecationDate = deprecationDate || new Date();
      
      logger.info('Schema version deprecated', {
        schemaId,
        version,
        deprecationDate: versionInfo.deprecationDate,
      });
    }
  }

  getMigrationPath(schemaId: string, fromVersion: string, toVersion: string): string[] {
    const versions = this.versions.get(schemaId);
    if (!versions) {
      return [];
    }

    const fromIndex = versions.findIndex(v => v.version === fromVersion);
    const toIndex = versions.findIndex(v => v.version === toVersion);

    if (fromIndex === -1 || toIndex === -1 || fromIndex >= toIndex) {
      return [];
    }

    // Return the versions between from and to as migration steps
    return versions.slice(fromIndex + 1, toIndex + 1).map(v => v.version);
  }
}

export const schemaVersionRegistry = new SchemaVersionRegistry();