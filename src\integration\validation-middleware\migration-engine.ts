import { MigrationRule, ValidationError } from './types';
import { SemanticVersionManager } from '../contract-validator/semantic-versioning';
import { logger } from '@/utils/logger';

export interface MigrationResult {
  success: boolean;
  data?: any;
  appliedMigrations: string[];
  errors: ValidationError[];
  warnings: string[];
}

export interface MigrationContext {
  fromVersion: string;
  toVersion: string;
  schemaId: string;
  data: any;
  metadata?: Record<string, any>;
}

export class SchemaMigrationEngine {
  private migrations: Map<string, MigrationRule[]> = new Map();
  private migrationHistory: Map<string, string[]> = new Map();

  constructor() {
    this.initializeBuiltInMigrations();
  }

  private initializeBuiltInMigrations(): void {
    // MicroNote migrations
    this.registerMigration('micronote', {
      fromVersion: '1.0.0',
      toVersion: '1.1.0',
      description: 'Add location context support',
      reversible: true,
      transform: (data: any) => {
        if (!data.metadata?.context) {
          data.metadata = data.metadata || {};
          data.metadata.context = data.metadata.context || {};
        }
        return data;
      },
      validate: (data: any) => {
        return data.metadata?.context !== undefined;
      },
    });

    // UIElement migrations
    this.registerMigration('uielement', {
      fromVersion: '1.0.0',
      toVersion: '1.1.0',
      description: 'Add accessibility properties',
      reversible: false,
      transform: (data: any) => {
        if (!data.behavior?.accessibility) {
          data.behavior = data.behavior || {};
          data.behavior.accessibility = {
            keyboard_navigation: true,
          };
        }
        return data;
      },
    });

    // CodePatch migrations
    this.registerMigration('codepatch', {
      fromVersion: '1.0.0',
      toVersion: '1.1.0',
      description: 'Add security scanning support',
      reversible: true,
      transform: (data: any) => {
        if (!data.validation?.security_scan) {
          data.validation = data.validation || {};
          data.validation.security_scan = {
            enabled: true,
            status: 'pending',
            vulnerabilities: [],
          };
        }
        return data;
      },
    });

    // Tool I/O migrations
    this.registerMigration('tool-io', {
      fromVersion: '1.0.0',
      toVersion: '1.1.0',
      description: 'Add performance benchmarks',
      reversible: true,
      transform: (data: any) => {
        if (!data.testing?.performance_benchmarks) {
          data.testing = data.testing || {};
          data.testing.performance_benchmarks = [];
        }
        return data;
      },
    });

    logger.info('Built-in schema migrations initialized');
  }

  registerMigration(schemaId: string, migration: MigrationRule): void {
    const existing = this.migrations.get(schemaId) || [];
    
    // Check for duplicate migrations
    const duplicate = existing.find(m => 
      m.fromVersion === migration.fromVersion && m.toVersion === migration.toVersion
    );
    
    if (duplicate) {
      throw new Error(
        `Migration already exists for ${schemaId} from ${migration.fromVersion} to ${migration.toVersion}`
      );
    }

    // Validate migration versions
    const fromParsed = SemanticVersionManager.parseVersion(migration.fromVersion);
    const toParsed = SemanticVersionManager.parseVersion(migration.toVersion);
    
    if (!fromParsed || !toParsed) {
      throw new Error('Invalid semantic versions in migration rule');
    }

    if (SemanticVersionManager.compareVersions(migration.fromVersion, migration.toVersion) >= 0) {
      throw new Error('Migration toVersion must be greater than fromVersion');
    }

    existing.push(migration);
    existing.sort((a, b) => SemanticVersionManager.compareVersions(a.fromVersion, b.fromVersion));
    
    this.migrations.set(schemaId, existing);
    
    logger.debug('Migration registered', {
      schemaId,
      fromVersion: migration.fromVersion,
      toVersion: migration.toVersion,
      description: migration.description,
    });
  }

  async migrateData(context: MigrationContext): Promise<MigrationResult> {
    const { fromVersion, toVersion, schemaId, data } = context;
    
    logger.info('Starting schema migration', {
      schemaId,
      fromVersion,
      toVersion,
    });

    try {
      // Check if migration is needed
      const comparison = SemanticVersionManager.compareVersions(fromVersion, toVersion);
      
      if (comparison === 0) {
        return {
          success: true,
          data,
          appliedMigrations: [],
          errors: [],
          warnings: [],
        };
      }

      if (comparison > 0) {
        return {
          success: false,
          data,
          appliedMigrations: [],
          errors: [{
            field: 'version',
            message: `Cannot migrate from higher version ${fromVersion} to lower version ${toVersion}`,
            code: 'INVALID_MIGRATION_DIRECTION',
            severity: 'error',
          }],
          warnings: [],
        };
      }

      // Find migration path
      const migrationPath = this.findMigrationPath(schemaId, fromVersion, toVersion);
      
      if (migrationPath.length === 0) {
        return {
          success: false,
          data,
          appliedMigrations: [],
          errors: [{
            field: 'version',
            message: `No migration path found from ${fromVersion} to ${toVersion}`,
            code: 'NO_MIGRATION_PATH',
            severity: 'error',
          }],
          warnings: [],
        };
      }

      // Apply migrations in sequence
      let currentData = JSON.parse(JSON.stringify(data)); // Deep clone
      const appliedMigrations: string[] = [];
      const warnings: string[] = [];
      const errors: ValidationError[] = [];

      for (const migration of migrationPath) {
        try {
          logger.debug('Applying migration', {
            schemaId,
            fromVersion: migration.fromVersion,
            toVersion: migration.toVersion,
            description: migration.description,
          });

          // Create backup before transformation
          const backup = JSON.parse(JSON.stringify(currentData));

          // Apply transformation
          currentData = migration.transform(currentData);
          
          // Validate transformation if validator is provided
          if (migration.validate && !migration.validate(currentData)) {
            errors.push({
              field: 'migration',
              message: `Migration validation failed: ${migration.description}`,
              code: 'MIGRATION_VALIDATION_FAILED',
              severity: 'error',
              context: {
                migrationVersion: migration.toVersion,
                description: migration.description,
              },
            });
            
            // Restore backup on validation failure
            currentData = backup;
            break;
          }

          appliedMigrations.push(`${migration.fromVersion} → ${migration.toVersion}`);
          
          // Record migration in history
          this.recordMigration(schemaId, migration.fromVersion, migration.toVersion);

          if (!migration.reversible) {
            warnings.push(`Migration to ${migration.toVersion} is not reversible`);
          }

        } catch (error) {
          errors.push({
            field: 'migration',
            message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            code: 'MIGRATION_TRANSFORM_FAILED',
            severity: 'error',
            context: {
              migrationVersion: migration.toVersion,
              description: migration.description,
              error: error instanceof Error ? error.stack : String(error),
            },
          });
          break;
        }
      }

      const success = errors.length === 0;
      
      logger.info('Schema migration completed', {
        schemaId,
        fromVersion,
        toVersion,
        success,
        appliedMigrations: appliedMigrations.length,
        errors: errors.length,
        warnings: warnings.length,
      });

      return {
        success,
        data: success ? currentData : data,
        appliedMigrations,
        errors,
        warnings,
      };

    } catch (error) {
      logger.error('Schema migration failed', {
        schemaId,
        fromVersion,
        toVersion,
        error,
      });

      return {
        success: false,
        data,
        appliedMigrations: [],
        errors: [{
          field: 'migration',
          message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          code: 'MIGRATION_SYSTEM_ERROR',
          severity: 'error',
          context: {
            error: error instanceof Error ? error.stack : String(error),
          },
        }],
        warnings: [],
      };
    }
  }

  private findMigrationPath(schemaId: string, fromVersion: string, toVersion: string): MigrationRule[] {
    const migrations = this.migrations.get(schemaId) || [];
    const path: MigrationRule[] = [];
    
    // Simple path finding - in a real implementation, this could use Dijkstra's algorithm
    // for complex migration graphs
    let currentVersion = fromVersion;
    
    while (SemanticVersionManager.compareVersions(currentVersion, toVersion) < 0) {
      const nextMigration = migrations.find(m => m.fromVersion === currentVersion);
      
      if (!nextMigration) {
        // No direct migration found, try to find intermediate steps
        const availableMigrations = migrations.filter(m => 
          SemanticVersionManager.compareVersions(m.fromVersion, currentVersion) >= 0 &&
          SemanticVersionManager.compareVersions(m.toVersion, toVersion) <= 0
        );
        
        if (availableMigrations.length === 0) {
          break; // No path found
        }
        
        // Choose the migration that gets us closest to the target
        const bestMigration = availableMigrations.reduce((best, current) => {
          const bestDistance = this.versionDistance(best.toVersion, toVersion);
          const currentDistance = this.versionDistance(current.toVersion, toVersion);
          return currentDistance < bestDistance ? current : best;
        });
        
        path.push(bestMigration);
        currentVersion = bestMigration.toVersion;
      } else {
        path.push(nextMigration);
        currentVersion = nextMigration.toVersion;
      }
      
      // Prevent infinite loops
      if (path.length > 10) {
        logger.warn('Migration path too long, aborting', { schemaId, fromVersion, toVersion });
        break;
      }
    }
    
    return path;
  }

  private versionDistance(version1: string, version2: string): number {
    const v1 = SemanticVersionManager.parseVersion(version1);
    const v2 = SemanticVersionManager.parseVersion(version2);
    
    if (!v1 || !v2) return Infinity;
    
    return Math.abs(v1.major - v2.major) * 10000 + 
           Math.abs(v1.minor - v2.minor) * 100 + 
           Math.abs(v1.patch - v2.patch);
  }

  private recordMigration(schemaId: string, fromVersion: string, toVersion: string): void {
    const key = `${schemaId}:${fromVersion}:${toVersion}`;
    const history = this.migrationHistory.get(schemaId) || [];
    history.push(key);
    this.migrationHistory.set(schemaId, history);
  }

  getMigrationHistory(schemaId: string): string[] {
    return this.migrationHistory.get(schemaId) || [];
  }

  getAvailableMigrations(schemaId: string): MigrationRule[] {
    return this.migrations.get(schemaId) || [];
  }

  canMigrate(schemaId: string, fromVersion: string, toVersion: string): boolean {
    const path = this.findMigrationPath(schemaId, fromVersion, toVersion);
    return path.length > 0 && path[path.length - 1].toVersion === toVersion;
  }

  createReverseMigration(schemaId: string, migration: MigrationRule): MigrationRule | null {
    if (!migration.reversible) {
      return null;
    }

    // This is a simplified reverse migration - in practice, you'd need
    // to implement proper reverse transformations
    return {
      fromVersion: migration.toVersion,
      toVersion: migration.fromVersion,
      description: `Reverse: ${migration.description}`,
      reversible: true,
      transform: (data: any) => {
        // Simplified reverse - in practice, this would need proper implementation
        logger.warn('Using simplified reverse migration', {
          schemaId,
          fromVersion: migration.toVersion,
          toVersion: migration.fromVersion,
        });
        return data;
      },
    };
  }
}

export const schemaMigrationEngine = new SchemaMigrationEngine();