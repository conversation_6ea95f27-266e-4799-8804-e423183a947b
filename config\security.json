{"security_policies": {"authentication": {"jwt_expiry": "1h", "refresh_token_expiry": "30d", "max_login_attempts": 3, "lockout_duration": "15m", "require_mfa": true}, "authorization": {"rbac_enabled": true, "default_role": "user", "permission_inheritance": true, "session_timeout": "8h"}, "encryption": {"algorithm": "aes-256-gcm", "key_rotation_days": 90, "encrypt_at_rest": true, "encrypt_in_transit": true}, "rate_limiting": {"global_limit": 1000, "per_user_limit": 100, "window_minutes": 15, "block_duration": "1h"}, "data_protection": {"pii_encryption": true, "data_retention_days": 365, "anonymization_enabled": true, "audit_all_access": true}}, "compliance": {"frameworks": ["GDPR", "CCPA", "SOC2", "ISO27001"], "data_classification": {"public": 0, "internal": 1, "confidential": 2, "restricted": 3}, "required_headers": ["X-Content-Type-Options", "X-Frame-Options", "X-XSS-Protection", "Strict-Transport-Security"]}, "monitoring": {"security_events": true, "anomaly_detection": true, "threat_intelligence": true, "incident_response": true}}