import 'dotenv/config';
import { createObservabilityHub } from '@/integration/observability-hub';
import { logger } from '@/utils/logger';

async function bootstrap(): Promise<void> {
  try {
    // Initialize observability first
    const observabilityHub = createObservabilityHub();
    await observabilityHub.start();

    logger.info('Custom Agent System v2.0 initializing...', {
      environment: process.env.NODE_ENV,
      version: '2.0.0',
    });

    // Initialize other components here
    // - Contract validator
    // - MCP client
    // - Security gateway
    // - Agent orchestration
    
    logger.info('Custom Agent System started successfully', {
      metricsEndpoint: observabilityHub.getMetricsEndpoint(),
    });

    // Graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await observabilityHub.shutdown();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await observabilityHub.shutdown();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start Custom Agent System', { error });
    process.exit(1);
  }
}

// Start the application
bootstrap().catch((error) => {
  logger.error('Unhandled error during bootstrap', { error });
  process.exit(1);
});