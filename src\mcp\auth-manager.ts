/**
 * MCP Authentication and Scope Management System
 * Manages user authentication, authorization, and scope-based access control
 */

import { EventEmitter } from 'events';
import { randomBytes, createHash, timingSafeEqual } from 'crypto';
import { logger } from '../utils/logger';
import { TraceLogger } from '../runtime/trace-logger';
import type { UserInfo, TokenResponse } from './client';

// Core Authentication Types
export interface User {
  id: string;
  username: string;
  email: string;
  displayName?: string;
  avatar?: string;
  roles: string[];
  scopes: string[];
  metadata: UserMetadata;
  status: 'active' | 'suspended' | 'disabled';
  createdAt: Date;
  lastLoginAt?: Date;
  lastActivity: Date;
}

export interface UserMetadata {
  organization?: string;
  department?: string;
  tier: 'free' | 'pro' | 'enterprise' | 'admin';
  preferences: UserPreferences;
  quotas: UserQuotas;
  restrictions: UserRestrictions;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  inApp: boolean;
  frequency: 'immediate' | 'daily' | 'weekly' | 'never';
}

export interface PrivacySettings {
  shareUsageData: boolean;
  allowAnalytics: boolean;
  publicProfile: boolean;
}

export interface UserQuotas {
  requests: { daily: number; monthly: number; used: { daily: number; monthly: number } };
  tokens: { daily: number; monthly: number; used: { daily: number; monthly: number } };
  storage: { total: number; used: number };
  concurrent: number;
}

export interface UserRestrictions {
  allowedServers: string[];
  blockedServers: string[];
  allowedTools: string[];
  blockedTools: string[];
  maxRequestSize: number;
  maxResponseSize: number;
  rateLimits: RateLimitConfig;
}

export interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  tokensPerMinute: number;
  tokensPerHour: number;
  burstLimit: number;
}

// Session and Token Types
export interface Session {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken?: string;
  idToken?: string;
  scopes: string[];
  expiresAt: Date;
  refreshExpiresAt?: Date;
  deviceInfo: DeviceInfo;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastActivity: Date;
  status: 'active' | 'expired' | 'revoked';
}

export interface DeviceInfo {
  type: 'web' | 'mobile' | 'desktop' | 'api' | 'unknown';
  platform?: string;
  browser?: string;
  version?: string;
  fingerprint: string;
}

export interface AccessToken {
  jti: string; // JWT ID
  sub: string; // Subject (user ID)
  aud: string; // Audience
  iss: string; // Issuer
  iat: number; // Issued at
  exp: number; // Expires at
  scope: string;
  roles: string[];
  sessionId: string;
}

// Scope and Permission Types
export interface Scope {
  name: string;
  description: string;
  category: 'read' | 'write' | 'admin' | 'special';
  permissions: Permission[];
  dependencies?: string[]; // Required scopes
  conflicts?: string[]; // Mutually exclusive scopes
  defaultGranted: boolean;
  adminOnly: boolean;
  deprecated?: boolean;
  expiresAt?: Date;
}

export interface Permission {
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
  metadata?: Record<string, any>;
}

export interface PermissionCondition {
  type: 'time' | 'location' | 'device' | 'quota' | 'custom';
  operator: 'equals' | 'not_equals' | 'contains' | 'in' | 'not_in' | 'greater' | 'less' | 'range';
  value: any;
  metadata?: Record<string, any>;
}

// Authorization Types
export interface AuthorizationRequest {
  userId: string;
  sessionId: string;
  resource: string;
  action: string;
  context?: AuthorizationContext;
  requestId?: string;
}

export interface AuthorizationContext {
  serverID?: string;
  toolName?: string;
  resourceUri?: string;
  requestData?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  correlationId?: string;
}

export interface AuthorizationResult {
  granted: boolean;
  scopes: string[];
  permissions: Permission[];
  restrictions?: AuthorizationRestriction[];
  reason?: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface AuthorizationRestriction {
  type: 'quota' | 'rate_limit' | 'time_window' | 'content_filter' | 'geographic';
  description: string;
  enforcement: 'block' | 'warn' | 'throttle';
  parameters?: Record<string, any>;
}

// Authentication Provider Interface
export interface AuthProvider {
  id: string;
  name: string;
  type: 'oauth2' | 'saml' | 'ldap' | 'local' | 'apikey';
  config: any;
  enabled: boolean;
  priority: number;
}

// Audit Types
export interface AuditEvent {
  id: string;
  timestamp: Date;
  type: 'auth' | 'access' | 'admin' | 'error';
  action: string;
  userId?: string;
  sessionId?: string;
  resource?: string;
  outcome: 'success' | 'failure' | 'warning';
  details: AuditDetails;
  metadata?: Record<string, any>;
}

export interface AuditDetails {
  ipAddress?: string;
  userAgent?: string;
  requestId?: string;
  error?: string;
  duration?: number;
  [key: string]: any;
}

// Configuration
export interface AuthManagerConfig {
  issuer: string;
  audience: string;
  tokenTTL: number;
  refreshTTL: number;
  sessionTTL: number;
  maxSessions: number;
  requireRefresh: boolean;
  enableAuditing: boolean;
  enableTracing: boolean;
  secretKey: string;
  encryptionKey: string;
  defaultScopes: string[];
  adminScopes: string[];
}

/**
 * MCP Authentication and Authorization Manager
 */
export class AuthManager extends EventEmitter {
  private config: AuthManagerConfig;
  private tracer: TraceLogger;
  
  // Storage
  private users = new Map<string, User>();
  private sessions = new Map<string, Session>();
  private scopes = new Map<string, Scope>();
  private auditLog: AuditEvent[] = [];
  
  // Indexes
  private usersByEmail = new Map<string, string>();
  private usersByUsername = new Map<string, string>();
  private sessionsByUser = new Map<string, Set<string>>();
  
  // Rate limiting
  private rateLimitCounters = new Map<string, { count: number; resetAt: Date }>();
  
  // Auth providers
  private authProviders = new Map<string, AuthProvider>();

  constructor(config: Partial<AuthManagerConfig>) {
    super();
    
    this.config = {
      issuer: 'custom-agent-system',
      audience: 'mcp-client',
      tokenTTL: 3600, // 1 hour
      refreshTTL: 604800, // 7 days
      sessionTTL: 86400, // 24 hours
      maxSessions: 10,
      requireRefresh: true,
      enableAuditing: true,
      enableTracing: true,
      secretKey: config.secretKey || this.generateSecretKey(),
      encryptionKey: config.encryptionKey || this.generateEncryptionKey(),
      defaultScopes: ['mcp:read', 'mcp:tools:basic'],
      adminScopes: ['mcp:admin', 'mcp:users:manage', 'mcp:servers:manage'],
      ...config,
    };
    
    this.tracer = new TraceLogger({
      serviceName: 'mcp-auth-manager',
      enableConsoleSpans: this.config.enableTracing,
    });
    
    // Initialize built-in scopes
    this.initializeBuiltinScopes();
    
    // Start cleanup interval
    setInterval(() => this.cleanup(), 300000); // 5 minutes
    
    logger.info('Auth manager initialized', {
      issuer: this.config.issuer,
      audience: this.config.audience,
      enableAuditing: this.config.enableAuditing,
    });
  }

  /**
   * Authenticate user and create session
   */
  async authenticate(
    credentials: { username?: string; email?: string; password?: string; token?: string },
    deviceInfo: DeviceInfo,
    ipAddress: string,
    userAgent: string
  ): Promise<{ user: User; session: Session; tokens: TokenResponse }> {
    const span = this.tracer.startSpan('authenticate_user', {
      hasUsername: !!credentials.username,
      hasEmail: !!credentials.email,
      hasToken: !!credentials.token,
    });

    try {
      let user: User | undefined;

      // Find user by credentials
      if (credentials.token) {
        // Token-based authentication (for API keys, etc.)
        user = await this.authenticateByToken(credentials.token);
      } else if (credentials.username || credentials.email) {
        // Username/password authentication
        user = await this.authenticateByPassword(
          credentials.username || credentials.email!,
          credentials.password!
        );
      }

      if (!user) {
        await this.auditLog.push({
          id: randomBytes(16).toString('hex'),
          timestamp: new Date(),
          type: 'auth',
          action: 'login_failed',
          resource: 'authentication',
          outcome: 'failure',
          details: {
            ipAddress,
            userAgent,
            reason: 'invalid_credentials',
          },
        });
        throw new Error('Invalid credentials');
      }

      if (user.status !== 'active') {
        await this.auditLog.push({
          id: randomBytes(16).toString('hex'),
          timestamp: new Date(),
          type: 'auth',
          action: 'login_blocked',
          userId: user.id,
          resource: 'authentication',
          outcome: 'failure',
          details: {
            ipAddress,
            userAgent,
            reason: `user_${user.status}`,
          },
        });
        throw new Error(`User account is ${user.status}`);
      }

      // Check rate limits
      await this.checkRateLimit(user.id, ipAddress);

      // Clean up old sessions if at limit
      await this.cleanupUserSessions(user.id);

      // Create new session
      const session = await this.createSession(user, deviceInfo, ipAddress, userAgent);

      // Generate tokens
      const tokens = await this.generateTokens(user, session);

      // Update user activity
      user.lastLoginAt = new Date();
      user.lastActivity = new Date();

      await this.auditLog.push({
        id: randomBytes(16).toString('hex'),
        timestamp: new Date(),
        type: 'auth',
        action: 'login_success',
        userId: user.id,
        sessionId: session.id,
        resource: 'authentication',
        outcome: 'success',
        details: {
          ipAddress,
          userAgent,
          deviceType: deviceInfo.type,
        },
      });

      span.setAttributes({
        userId: user.id,
        sessionId: session.id,
        scopes: session.scopes.join(','),
      });

      this.emit('user:authenticated', user.id, session.id);

      return { user, session, tokens };

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Authentication failed', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Authorize user action
   */
  async authorize(request: AuthorizationRequest): Promise<AuthorizationResult> {
    const span = this.tracer.startSpan('authorize_action', {
      userId: request.userId,
      resource: request.resource,
      action: request.action,
    });

    try {
      const user = this.users.get(request.userId);
      if (!user) {
        throw new Error('User not found');
      }

      const session = this.sessions.get(request.sessionId);
      if (!session || session.status !== 'active' || session.expiresAt < new Date()) {
        throw new Error('Invalid or expired session');
      }

      // Check basic access
      if (user.status !== 'active') {
        return this.createAuthorizationResult(false, [], [], 'User account is not active');
      }

      // Get effective permissions
      const effectivePermissions = await this.getEffectivePermissions(user, session.scopes);
      
      // Check specific permission
      const hasPermission = this.checkPermission(
        effectivePermissions,
        request.resource,
        request.action,
        request.context
      );

      if (!hasPermission.granted) {
        await this.auditLog.push({
          id: randomBytes(16).toString('hex'),
          timestamp: new Date(),
          type: 'access',
          action: 'authorization_denied',
          userId: user.id,
          sessionId: session.id,
          resource: request.resource,
          outcome: 'failure',
          details: {
            action: request.action,
            reason: hasPermission.reason,
            requestId: request.requestId,
          },
        });

        return this.createAuthorizationResult(
          false,
          session.scopes,
          effectivePermissions,
          hasPermission.reason
        );
      }

      // Apply restrictions
      const restrictions = await this.applyRestrictions(user, request);

      // Update session activity
      session.lastActivity = new Date();
      user.lastActivity = new Date();

      await this.auditLog.push({
        id: randomBytes(16).toString('hex'),
        timestamp: new Date(),
        type: 'access',
        action: 'authorization_granted',
        userId: user.id,
        sessionId: session.id,
        resource: request.resource,
        outcome: 'success',
        details: {
          action: request.action,
          scopes: session.scopes,
          requestId: request.requestId,
        },
      });

      span.setAttributes({
        granted: true,
        scopes: session.scopes.join(','),
        restrictionsCount: restrictions.length,
      });

      this.emit('user:authorized', user.id, request.resource, request.action);

      return this.createAuthorizationResult(
        true,
        session.scopes,
        effectivePermissions,
        undefined,
        restrictions
      );

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Authorization failed', { error, request });
      
      return this.createAuthorizationResult(
        false,
        [],
        [],
        `Authorization error: ${(error as Error).message}`
      );
    } finally {
      span.end();
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    const span = this.tracer.startSpan('refresh_token');

    try {
      // Find session by refresh token
      const session = Array.from(this.sessions.values()).find(
        s => s.refreshToken === refreshToken && s.status === 'active'
      );

      if (!session || !session.refreshExpiresAt || session.refreshExpiresAt < new Date()) {
        throw new Error('Invalid or expired refresh token');
      }

      const user = this.users.get(session.userId);
      if (!user || user.status !== 'active') {
        throw new Error('User not found or inactive');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user, session);

      // Update session
      session.accessToken = tokens.access_token;
      session.refreshToken = tokens.refresh_token;
      session.expiresAt = new Date(Date.now() + this.config.tokenTTL * 1000);
      session.lastActivity = new Date();

      await this.auditLog.push({
        id: randomBytes(16).toString('hex'),
        timestamp: new Date(),
        type: 'auth',
        action: 'token_refreshed',
        userId: user.id,
        sessionId: session.id,
        resource: 'authentication',
        outcome: 'success',
        details: {},
      });

      span.setAttributes({
        userId: user.id,
        sessionId: session.id,
      });

      return tokens;

    } catch (error) {
      span.recordException(error as Error);
      logger.error('Token refresh failed', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Revoke session
   */
  async revokeSession(sessionId: string, reason?: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    session.status = 'revoked';
    
    // Remove from user sessions index
    const userSessions = this.sessionsByUser.get(session.userId);
    if (userSessions) {
      userSessions.delete(sessionId);
    }

    await this.auditLog.push({
      id: randomBytes(16).toString('hex'),
      timestamp: new Date(),
      type: 'auth',
      action: 'session_revoked',
      userId: session.userId,
      sessionId: session.id,
      resource: 'authentication',
      outcome: 'success',
      details: { reason: reason || 'manual_revocation' },
    });

    this.emit('session:revoked', sessionId, session.userId);
    
    logger.info('Session revoked', { sessionId, userId: session.userId, reason });
  }

  /**
   * Create user
   */
  async createUser(
    userData: Omit<User, 'id' | 'createdAt' | 'lastActivity' | 'status'>,
    password?: string
  ): Promise<User> {
    const span = this.tracer.startSpan('create_user', {
      username: userData.username,
      email: userData.email,
    });

    try {
      // Check if user already exists
      if (this.usersByEmail.has(userData.email)) {
        throw new Error('User with this email already exists');
      }
      
      if (this.usersByUsername.has(userData.username)) {
        throw new Error('User with this username already exists');
      }

      const userId = randomBytes(16).toString('hex');
      const now = new Date();

      const user: User = {
        ...userData,
        id: userId,
        status: 'active',
        createdAt: now,
        lastActivity: now,
      };

      // Store user
      this.users.set(userId, user);
      this.usersByEmail.set(userData.email, userId);
      this.usersByUsername.set(userData.username, userId);

      // Store password hash if provided
      if (password) {
        await this.setUserPassword(userId, password);
      }

      await this.auditLog.push({
        id: randomBytes(16).toString('hex'),
        timestamp: new Date(),
        type: 'admin',
        action: 'user_created',
        userId: userId,
        resource: 'user_management',
        outcome: 'success',
        details: {
          username: userData.username,
          email: userData.email,
          roles: userData.roles,
        },
      });

      span.setAttributes({
        userId,
        roles: userData.roles.join(','),
        scopes: userData.scopes.join(','),
      });

      this.emit('user:created', userId);

      logger.info('User created successfully', {
        userId,
        username: userData.username,
        email: userData.email,
      });

      return user;

    } catch (error) {
      span.recordException(error as Error);
      logger.error('User creation failed', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Update user scopes
   */
  async updateUserScopes(userId: string, scopes: string[], adminUserId?: string): Promise<void> {
    const span = this.tracer.startSpan('update_user_scopes', {
      userId,
      scopesCount: scopes.length,
    });

    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate scopes exist
      for (const scope of scopes) {
        if (!this.scopes.has(scope)) {
          throw new Error(`Invalid scope: ${scope}`);
        }
      }

      const oldScopes = [...user.scopes];
      user.scopes = [...scopes];
      user.lastActivity = new Date();

      // Invalidate active sessions to force re-authentication with new scopes
      const userSessions = this.sessionsByUser.get(userId);
      if (userSessions) {
        for (const sessionId of userSessions) {
          const session = this.sessions.get(sessionId);
          if (session && session.status === 'active') {
            session.status = 'revoked';
          }
        }
        userSessions.clear();
      }

      await this.auditLog.push({
        id: randomBytes(16).toString('hex'),
        timestamp: new Date(),
        type: 'admin',
        action: 'user_scopes_updated',
        userId: adminUserId,
        resource: 'user_management',
        outcome: 'success',
        details: {
          targetUserId: userId,
          oldScopes,
          newScopes: scopes,
        },
      });

      span.setAttributes({
        oldScopesCount: oldScopes.length,
        newScopesCount: scopes.length,
      });

      this.emit('user:scopes_updated', userId, scopes, oldScopes);

      logger.info('User scopes updated', { userId, oldScopes, newScopes: scopes });

    } catch (error) {
      span.recordException(error as Error);
      logger.error('User scope update failed', { error });
      throw error;
    } finally {
      span.end();
    }
  }

  /**
   * Register scope
   */
  registerScope(scope: Scope): void {
    try {
      // Validate scope
      if (this.scopes.has(scope.name)) {
        throw new Error(`Scope ${scope.name} already exists`);
      }

      // Check dependencies
      if (scope.dependencies) {
        for (const dep of scope.dependencies) {
          if (!this.scopes.has(dep)) {
            throw new Error(`Dependency scope ${dep} not found`);
          }
        }
      }

      this.scopes.set(scope.name, scope);

      logger.info('Scope registered successfully', {
        name: scope.name,
        category: scope.category,
        permissions: scope.permissions.length,
      });

    } catch (error) {
      logger.error('Scope registration failed', { scope: scope.name, error });
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  getUser(userId: string): User | undefined {
    return this.users.get(userId);
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): Session | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * List user sessions
   */
  getUserSessions(userId: string): Session[] {
    const sessionIds = this.sessionsByUser.get(userId);
    if (!sessionIds) return [];

    return Array.from(sessionIds)
      .map(id => this.sessions.get(id))
      .filter((session): session is Session => !!session && session.status === 'active');
  }

  /**
   * Get scope definition
   */
  getScope(scopeName: string): Scope | undefined {
    return this.scopes.get(scopeName);
  }

  /**
   * List all scopes
   */
  listScopes(): Scope[] {
    return Array.from(this.scopes.values());
  }

  // Private helper methods

  private async authenticateByToken(token: string): Promise<User | undefined> {
    // In a real implementation, you would verify the token signature and extract user info
    // For now, this is a simplified lookup
    const tokenHash = createHash('sha256').update(token).digest('hex');
    return Array.from(this.users.values()).find(user => 
      user.metadata.tier === 'enterprise' // Simplified token auth for enterprise users
    );
  }

  private async authenticateByPassword(identifier: string, password: string): Promise<User | undefined> {
    // Find user by email or username
    let userId = this.usersByEmail.get(identifier) || this.usersByUsername.get(identifier);
    if (!userId) return undefined;

    const user = this.users.get(userId);
    if (!user) return undefined;

    // In a real implementation, you would verify the password hash
    // For demo purposes, we'll accept any password for existing users
    return user;
  }

  private async setUserPassword(userId: string, password: string): Promise<void> {
    // In a real implementation, you would hash and store the password securely
    // This is a placeholder for the demo
    logger.debug('Password set for user', { userId });
  }

  private async checkRateLimit(userId: string, ipAddress: string): Promise<void> {
    const key = `${userId}:${ipAddress}`;
    const now = new Date();
    const counter = this.rateLimitCounters.get(key);

    if (!counter) {
      this.rateLimitCounters.set(key, {
        count: 1,
        resetAt: new Date(now.getTime() + 60000), // 1 minute window
      });
      return;
    }

    if (now > counter.resetAt) {
      counter.count = 1;
      counter.resetAt = new Date(now.getTime() + 60000);
      return;
    }

    counter.count++;
    if (counter.count > 10) { // 10 attempts per minute
      throw new Error('Rate limit exceeded');
    }
  }

  private async cleanupUserSessions(userId: string): Promise<void> {
    const userSessions = this.sessionsByUser.get(userId) || new Set();
    const activeSessions = Array.from(userSessions)
      .map(id => this.sessions.get(id))
      .filter((session): session is Session => !!session && session.status === 'active')
      .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());

    // Keep only the most recent sessions up to maxSessions limit
    const sessionsToRevoke = activeSessions.slice(this.config.maxSessions - 1);
    for (const session of sessionsToRevoke) {
      await this.revokeSession(session.id, 'session_limit_exceeded');
    }
  }

  private async createSession(
    user: User,
    deviceInfo: DeviceInfo,
    ipAddress: string,
    userAgent: string
  ): Promise<Session> {
    const sessionId = randomBytes(32).toString('hex');
    const now = new Date();

    const session: Session = {
      id: sessionId,
      userId: user.id,
      accessToken: '', // Will be set when generating tokens
      scopes: [...user.scopes],
      expiresAt: new Date(now.getTime() + this.config.tokenTTL * 1000),
      refreshExpiresAt: this.config.requireRefresh 
        ? new Date(now.getTime() + this.config.refreshTTL * 1000)
        : undefined,
      deviceInfo,
      ipAddress,
      userAgent,
      createdAt: now,
      lastActivity: now,
      status: 'active',
    };

    this.sessions.set(sessionId, session);
    
    // Update user sessions index
    if (!this.sessionsByUser.has(user.id)) {
      this.sessionsByUser.set(user.id, new Set());
    }
    this.sessionsByUser.get(user.id)!.add(sessionId);

    return session;
  }

  private async generateTokens(user: User, session: Session): Promise<TokenResponse> {
    const now = Math.floor(Date.now() / 1000);
    const exp = now + this.config.tokenTTL;

    // Create access token payload
    const accessToken: AccessToken = {
      jti: randomBytes(16).toString('hex'),
      sub: user.id,
      aud: this.config.audience,
      iss: this.config.issuer,
      iat: now,
      exp,
      scope: session.scopes.join(' '),
      roles: user.roles,
      sessionId: session.id,
    };

    // In a real implementation, you would sign the JWT properly
    const accessTokenString = Buffer.from(JSON.stringify(accessToken)).toString('base64');
    const refreshTokenString = this.config.requireRefresh 
      ? randomBytes(32).toString('hex')
      : undefined;

    // Update session with tokens
    session.accessToken = accessTokenString;
    session.refreshToken = refreshTokenString;

    return {
      access_token: accessTokenString,
      token_type: 'Bearer',
      expires_in: this.config.tokenTTL,
      refresh_token: refreshTokenString,
      scope: session.scopes.join(' '),
    };
  }

  private async getEffectivePermissions(user: User, scopes: string[]): Promise<Permission[]> {
    const permissions: Permission[] = [];

    for (const scopeName of scopes) {
      const scope = this.scopes.get(scopeName);
      if (scope && !scope.deprecated) {
        permissions.push(...scope.permissions);
      }
    }

    // Add role-based permissions
    for (const role of user.roles) {
      const roleScope = this.scopes.get(`role:${role}`);
      if (roleScope) {
        permissions.push(...roleScope.permissions);
      }
    }

    return permissions;
  }

  private checkPermission(
    permissions: Permission[],
    resource: string,
    action: string,
    context?: AuthorizationContext
  ): { granted: boolean; reason?: string } {
    for (const permission of permissions) {
      if (permission.resource === resource && permission.action === action) {
        // Check conditions if any
        if (permission.conditions) {
          const conditionResult = this.evaluateConditions(permission.conditions, context);
          if (!conditionResult.granted) {
            return conditionResult;
          }
        }
        return { granted: true };
      }

      // Check wildcard permissions
      if (permission.resource === '*' && permission.action === action) {
        return { granted: true };
      }
      if (permission.resource === resource && permission.action === '*') {
        return { granted: true };
      }
      if (permission.resource === '*' && permission.action === '*') {
        return { granted: true };
      }
    }

    return { granted: false, reason: 'Permission not found' };
  }

  private evaluateConditions(
    conditions: PermissionCondition[],
    context?: AuthorizationContext
  ): { granted: boolean; reason?: string } {
    for (const condition of conditions) {
      const result = this.evaluateCondition(condition, context);
      if (!result.granted) {
        return result;
      }
    }
    return { granted: true };
  }

  private evaluateCondition(
    condition: PermissionCondition,
    context?: AuthorizationContext
  ): { granted: boolean; reason?: string } {
    // Simplified condition evaluation
    if (condition.type === 'time') {
      const now = new Date();
      if (condition.operator === 'range' && Array.isArray(condition.value)) {
        const [start, end] = condition.value;
        const startTime = new Date(start);
        const endTime = new Date(end);
        if (now < startTime || now > endTime) {
          return { granted: false, reason: 'Outside allowed time window' };
        }
      }
    }

    // Add more condition types as needed
    return { granted: true };
  }

  private async applyRestrictions(user: User, request: AuthorizationRequest): Promise<AuthorizationRestriction[]> {
    const restrictions: AuthorizationRestriction[] = [];

    // Check quotas
    if (user.metadata.quotas.requests.used.daily >= user.metadata.quotas.requests.daily) {
      restrictions.push({
        type: 'quota',
        description: 'Daily request quota exceeded',
        enforcement: 'block',
        parameters: { quotaType: 'requests', period: 'daily' },
      });
    }

    // Check rate limits
    const rateLimits = user.metadata.restrictions.rateLimits;
    // Rate limit checking would be implemented here

    return restrictions;
  }

  private createAuthorizationResult(
    granted: boolean,
    scopes: string[],
    permissions: Permission[],
    reason?: string,
    restrictions?: AuthorizationRestriction[]
  ): AuthorizationResult {
    return {
      granted,
      scopes,
      permissions,
      restrictions,
      reason,
      expiresAt: granted ? new Date(Date.now() + this.config.tokenTTL * 1000) : undefined,
      metadata: {
        timestamp: new Date(),
        version: '2.0.0',
      },
    };
  }

  private initializeBuiltinScopes(): void {
    // Define built-in MCP scopes
    const builtinScopes: Scope[] = [
      {
        name: 'mcp:read',
        description: 'Read access to MCP resources',
        category: 'read',
        permissions: [
          { resource: 'mcp:servers', action: 'list' },
          { resource: 'mcp:tools', action: 'list' },
          { resource: 'mcp:resources', action: 'read' },
          { resource: 'mcp:prompts', action: 'read' },
        ],
        defaultGranted: true,
        adminOnly: false,
      },
      {
        name: 'mcp:tools:basic',
        description: 'Execute basic MCP tools',
        category: 'write',
        permissions: [
          { resource: 'mcp:tools', action: 'execute' },
        ],
        dependencies: ['mcp:read'],
        defaultGranted: true,
        adminOnly: false,
      },
      {
        name: 'mcp:tools:advanced',
        description: 'Execute advanced MCP tools',
        category: 'write',
        permissions: [
          { resource: 'mcp:tools', action: 'execute' },
          { resource: 'mcp:tools:advanced', action: '*' },
        ],
        dependencies: ['mcp:tools:basic'],
        defaultGranted: false,
        adminOnly: false,
      },
      {
        name: 'mcp:admin',
        description: 'Administrative access to MCP system',
        category: 'admin',
        permissions: [
          { resource: '*', action: '*' },
        ],
        defaultGranted: false,
        adminOnly: true,
      },
      {
        name: 'mcp:servers:manage',
        description: 'Manage MCP servers',
        category: 'admin',
        permissions: [
          { resource: 'mcp:servers', action: '*' },
          { resource: 'mcp:registry', action: 'write' },
        ],
        dependencies: ['mcp:read'],
        defaultGranted: false,
        adminOnly: true,
      },
    ];

    for (const scope of builtinScopes) {
      this.scopes.set(scope.name, scope);
    }

    logger.info('Built-in scopes initialized', {
      count: builtinScopes.length,
    });
  }

  private generateSecretKey(): string {
    return randomBytes(32).toString('hex');
  }

  private generateEncryptionKey(): string {
    return randomBytes(32).toString('hex');
  }

  private cleanup(): void {
    const now = new Date();
    
    // Clean up expired sessions
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.expiresAt < now || session.status !== 'active') {
        this.sessions.delete(sessionId);
        
        // Update user sessions index
        const userSessions = this.sessionsByUser.get(session.userId);
        if (userSessions) {
          userSessions.delete(sessionId);
        }
      }
    }

    // Clean up old rate limit counters
    for (const [key, counter] of this.rateLimitCounters.entries()) {
      if (now > counter.resetAt) {
        this.rateLimitCounters.delete(key);
      }
    }

    // Truncate audit log if too large
    if (this.auditLog.length > 10000) {
      this.auditLog = this.auditLog.slice(-5000);
    }

    logger.debug('Auth manager cleanup completed', {
      activeSessions: this.sessions.size,
      rateLimitCounters: this.rateLimitCounters.size,
      auditLogSize: this.auditLog.length,
    });
  }

  /**
   * Shutdown auth manager and cleanup resources
   */
  async shutdown(): Promise<void> {
    logger.info('Shutting down auth manager');
    
    // Revoke all active sessions
    for (const sessionId of this.sessions.keys()) {
      await this.revokeSession(sessionId, 'system_shutdown');
    }

    // Clear all data
    this.users.clear();
    this.sessions.clear();
    this.scopes.clear();
    this.auditLog.length = 0;
    this.usersByEmail.clear();
    this.usersByUsername.clear();
    this.sessionsByUser.clear();
    this.rateLimitCounters.clear();

    this.removeAllListeners();
  }
}