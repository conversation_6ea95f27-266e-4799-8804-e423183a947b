{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://custom-agents.dev/schemas/uielement.schema.json", "title": "<PERSON><PERSON><PERSON>", "description": "Schema for minimal UI elements in the Custom Agent System with semantic versioning", "type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the UI element"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+(?:-[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?(?:\\+[a-zA-Z0-9]+(?:\\.[a-zA-Z0-9]+)*)?$", "description": "Semantic version following semver.org specification"}, "type": {"type": "string", "enum": ["button", "input", "select", "checkbox", "radio", "textarea", "label", "card", "modal", "toast", "progress", "spinner", "table", "list", "tree", "chart", "diff_viewer", "code_editor", "capability_explorer", "retrieval_tuner"], "description": "Type of UI element"}, "properties": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Element name/identifier"}, "label": {"type": "string", "maxLength": 200, "description": "Display label for the element"}, "placeholder": {"type": "string", "maxLength": 200, "description": "Placeholder text for input elements"}, "value": {"description": "Current value of the element", "oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "object"}]}, "default_value": {"description": "Default value of the element", "oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "object"}]}, "options": {"type": "array", "items": {"$ref": "#/$defs/option"}, "description": "Available options for select/radio elements"}, "validation": {"$ref": "#/$defs/validation_rules", "description": "Validation rules for the element"}}, "required": ["name"], "additionalProperties": false}, "layout": {"type": "object", "properties": {"position": {"type": "object", "properties": {"x": {"type": "number"}, "y": {"type": "number"}, "z": {"type": "integer", "minimum": 0}}, "additionalProperties": false}, "size": {"type": "object", "properties": {"width": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "pattern": "^\\d+(\\.\\d+)?(px|%|em|rem|vw|vh)$"}]}, "height": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "pattern": "^\\d+(\\.\\d+)?(px|%|em|rem|vw|vh)$"}]}, "min_width": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "pattern": "^\\d+(\\.\\d+)?(px|%|em|rem|vw|vh)$"}]}, "min_height": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "string", "pattern": "^\\d+(\\.\\d+)?(px|%|em|rem|vw|vh)$"}]}}, "additionalProperties": false}, "margin": {"$ref": "#/$defs/spacing"}, "padding": {"$ref": "#/$defs/spacing"}, "flex": {"type": "object", "properties": {"direction": {"type": "string", "enum": ["row", "column", "row-reverse", "column-reverse"]}, "wrap": {"type": "string", "enum": ["nowrap", "wrap", "wrap-reverse"]}, "justify": {"type": "string", "enum": ["flex-start", "flex-end", "center", "space-between", "space-around", "space-evenly"]}, "align": {"type": "string", "enum": ["stretch", "flex-start", "flex-end", "center", "baseline"]}, "grow": {"type": "number", "minimum": 0}, "shrink": {"type": "number", "minimum": 0}, "basis": {"oneOf": [{"type": "string", "enum": ["auto", "content"]}, {"type": "string", "pattern": "^\\d+(\\.\\d+)?(px|%|em|rem|vw|vh)$"}]}}, "additionalProperties": false}}, "additionalProperties": false}, "styling": {"type": "object", "properties": {"theme": {"type": "string", "enum": ["light", "dark", "auto", "high_contrast"], "default": "auto"}, "variant": {"type": "string", "enum": ["primary", "secondary", "success", "warning", "error", "info"], "default": "primary"}, "size": {"type": "string", "enum": ["xs", "sm", "md", "lg", "xl"], "default": "md"}, "color": {"type": "object", "properties": {"background": {"$ref": "#/$defs/color"}, "foreground": {"$ref": "#/$defs/color"}, "border": {"$ref": "#/$defs/color"}, "accent": {"$ref": "#/$defs/color"}}, "additionalProperties": false}, "border": {"type": "object", "properties": {"width": {"type": "number", "minimum": 0}, "style": {"type": "string", "enum": ["none", "solid", "dashed", "dotted", "double"]}, "radius": {"type": "number", "minimum": 0}}, "additionalProperties": false}, "shadow": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "blur": {"type": "number", "minimum": 0}, "spread": {"type": "number"}, "color": {"$ref": "#/$defs/color"}}, "additionalProperties": false}, "animation": {"type": "object", "properties": {"type": {"type": "string", "enum": ["none", "fade", "slide", "scale", "rotate", "bounce", "pulse"]}, "duration": {"type": "number", "minimum": 0}, "easing": {"type": "string", "enum": ["linear", "ease", "ease-in", "ease-out", "ease-in-out", "cubic-bezier"]}, "delay": {"type": "number", "minimum": 0}}, "additionalProperties": false}}, "additionalProperties": false}, "behavior": {"type": "object", "properties": {"events": {"type": "object", "properties": {"onClick": {"$ref": "#/$defs/event_handler"}, "onChange": {"$ref": "#/$defs/event_handler"}, "onFocus": {"$ref": "#/$defs/event_handler"}, "onBlur": {"$ref": "#/$defs/event_handler"}, "onHover": {"$ref": "#/$defs/event_handler"}, "onKeyPress": {"$ref": "#/$defs/event_handler"}, "onSubmit": {"$ref": "#/$defs/event_handler"}}, "additionalProperties": false}, "state": {"type": "object", "properties": {"disabled": {"type": "boolean", "default": false}, "readonly": {"type": "boolean", "default": false}, "hidden": {"type": "boolean", "default": false}, "loading": {"type": "boolean", "default": false}, "error": {"type": "boolean", "default": false}, "focus": {"type": "boolean", "default": false}}, "additionalProperties": false}, "accessibility": {"type": "object", "properties": {"aria_label": {"type": "string"}, "aria_describedby": {"type": "string"}, "role": {"type": "string"}, "tabindex": {"type": "integer"}, "keyboard_navigation": {"type": "boolean", "default": true}, "screen_reader_text": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "data_binding": {"type": "object", "properties": {"source": {"type": "string", "description": "Data source identifier or path"}, "transform": {"type": "string", "description": "JavaScript expression for data transformation"}, "refresh_interval": {"type": "integer", "minimum": 0, "description": "Auto-refresh interval in milliseconds"}, "filters": {"type": "array", "items": {"$ref": "#/$defs/data_filter"}}}, "additionalProperties": false}, "metadata": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "category": {"type": "string"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "author": {"type": "string"}, "parent_id": {"type": "string", "format": "uuid", "description": "Parent element ID for hierarchical structures"}, "children": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "Child element IDs"}}, "additionalProperties": false}}, "required": ["id", "version", "type", "properties"], "additionalProperties": false, "$defs": {"option": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}, "disabled": {"type": "boolean", "default": false}, "group": {"type": "string"}}, "required": ["label", "value"], "additionalProperties": false}, "validation_rules": {"type": "object", "properties": {"required": {"type": "boolean", "default": false}, "min_length": {"type": "integer", "minimum": 0}, "max_length": {"type": "integer", "minimum": 0}, "pattern": {"type": "string"}, "min_value": {"type": "number"}, "max_value": {"type": "number"}, "custom_validator": {"type": "string"}, "error_message": {"type": "string"}}, "additionalProperties": false}, "spacing": {"oneOf": [{"type": "number", "minimum": 0}, {"type": "object", "properties": {"top": {"type": "number", "minimum": 0}, "right": {"type": "number", "minimum": 0}, "bottom": {"type": "number", "minimum": 0}, "left": {"type": "number", "minimum": 0}}, "additionalProperties": false}]}, "color": {"oneOf": [{"type": "string", "pattern": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "description": "Hex color code"}, {"type": "string", "pattern": "^rgb\\(\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\s*\\)$", "description": "RGB color"}, {"type": "string", "pattern": "^rgba\\(\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([01](\\.\\d+)?)\\s*\\)$", "description": "RGBA color"}, {"type": "string", "enum": ["transparent", "currentColor", "inherit"]}]}, "event_handler": {"type": "object", "properties": {"action": {"type": "string", "enum": ["navigate", "submit", "validate", "transform", "custom"]}, "target": {"type": "string"}, "parameters": {"type": "object"}, "condition": {"type": "string"}}, "required": ["action"], "additionalProperties": false}, "data_filter": {"type": "object", "properties": {"field": {"type": "string"}, "operator": {"type": "string", "enum": ["eq", "ne", "gt", "gte", "lt", "lte", "contains", "startsWith", "endsWith", "in", "nin"]}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}]}}, "required": ["field", "operator", "value"], "additionalProperties": false}}}