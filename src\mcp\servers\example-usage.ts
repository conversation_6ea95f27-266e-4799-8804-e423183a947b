/**
 * Example Usage of MCP Servers
 * Demonstrates how to start and use the three example MCP servers
 */

import { EmbeddingsServer } from './embeddings-server';
import { SummarizeServer } from './summarize-server';
import { CodePatchServer } from './code-patch-server';
import { logger } from '../../utils/logger';

/**
 * Example usage of all three MCP servers
 */
export class MCPServerExample {
  private embeddingsServer: EmbeddingsServer;
  private summarizeServer: SummarizeServer;
  private codePatchServer: CodePatchServer;

  constructor() {
    // Initialize servers with custom configurations
    this.embeddingsServer = new EmbeddingsServer({
      port: 8081,
      enableTracing: true,
      rateLimits: {
        requestsPerMinute: 100,
        tokensPerMinute: 10000,
      },
    });

    this.summarizeServer = new SummarizeServer({
      port: 8082,
      enableTracing: true,
      rateLimits: {
        requestsPerMinute: 50,
        charactersPerMinute: 100000,
      },
    });

    this.codePatchServer = new CodePatchServer({
      port: 8083,
      enableTracing: true,
      supportedLanguages: ['javascript', 'typescript', 'python', 'java'],
      rateLimits: {
        requestsPerMinute: 30,
        linesPerMinute: 5000,
      },
    });
  }

  /**
   * Start all MCP servers
   */
  async startServers(): Promise<void> {
    try {
      logger.info('Starting MCP servers...');

      // Start servers in parallel
      await Promise.all([
        this.embeddingsServer.start(),
        this.summarizeServer.start(),
        this.codePatchServer.start(),
      ]);

      logger.info('All MCP servers started successfully');
      this.logServerInfo();

    } catch (error) {
      logger.error('Failed to start MCP servers', { error });
      throw error;
    }
  }

  /**
   * Stop all MCP servers
   */
  async stopServers(): Promise<void> {
    try {
      logger.info('Stopping MCP servers...');

      // Stop servers in parallel
      await Promise.all([
        this.embeddingsServer.stop(),
        this.summarizeServer.stop(),
        this.codePatchServer.stop(),
      ]);

      logger.info('All MCP servers stopped successfully');

    } catch (error) {
      logger.error('Failed to stop MCP servers', { error });
      throw error;
    }
  }

  /**
   * Log information about all running servers
   */
  private logServerInfo(): void {
    const embeddingsInfo = this.embeddingsServer.getServerInfo();
    const summarizeInfo = this.summarizeServer.getServerInfo();
    const codePatchInfo = this.codePatchServer.getServerInfo();

    logger.info('MCP Server Status', {
      embeddings: {
        port: 8081,
        status: embeddingsInfo.status,
        clients: embeddingsInfo.clients,
        models: embeddingsInfo.models,
        cacheSize: embeddingsInfo.cacheSize,
      },
      summarize: {
        port: 8082,
        status: summarizeInfo.status,
        clients: summarizeInfo.clients,
        models: summarizeInfo.models,
        cacheSize: summarizeInfo.cacheSize,
      },
      codePatch: {
        port: 8083,
        status: codePatchInfo.status,
        clients: codePatchInfo.clients,
        supportedLanguages: codePatchInfo.supportedLanguages,
        cacheSize: codePatchInfo.cacheSize,
      },
    });
  }

  /**
   * Example client interactions with the servers
   */
  async demonstrateUsage(): Promise<void> {
    logger.info('Demonstrating MCP server capabilities...');

    // Example 1: Embeddings Server
    logger.info('=== Embeddings Server Example ===');
    logger.info('Available tools:');
    logger.info('- generate_embeddings: Generate embeddings for text(s)');
    logger.info('- calculate_similarity: Calculate similarity between query and documents');
    logger.info('- cluster_texts: Cluster texts using embeddings');
    logger.info('');
    logger.info('Example WebSocket message to generate embeddings:');
    logger.info(JSON.stringify({
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: 'generate_embeddings',
        arguments: {
          text: ['Hello world', 'Machine learning is fascinating'],
          model: 'text-embedding-3-small',
          encoding_format: 'float'
        }
      }
    }, null, 2));

    // Example 2: Summarize Server
    logger.info('=== Summarize Server Example ===');
    logger.info('Available tools:');
    logger.info('- summarize_text: Summarize text using various strategies');
    logger.info('- batch_summarize: Summarize multiple documents');
    logger.info('- analyze_text: Analyze text for sentiment, topics, entities, and readability');
    logger.info('');
    logger.info('Example WebSocket message to summarize text:');
    logger.info(JSON.stringify({
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'summarize_text',
        arguments: {
          text: 'This is a long document that needs to be summarized. It contains important information about various topics including technology, business, and innovation. The document discusses multiple strategies for growth and development.',
          strategy: 'abstractive',
          max_length: 100,
          focus: 'key_points',
          format: 'bullet_points'
        }
      }
    }, null, 2));

    // Example 3: Code Patch Server
    logger.info('=== Code Patch Server Example ===');
    logger.info('Available tools:');
    logger.info('- analyze_code: Analyze code for syntax, complexity, security, performance, and style');
    logger.info('- generate_patch: Generate a patch between original and target code');
    logger.info('- apply_patch: Apply a patch to original code');
    logger.info('');
    logger.info('Example WebSocket message to analyze code:');
    logger.info(JSON.stringify({
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'analyze_code',
        arguments: {
          code: 'function calculateSum(a, b) {\n  if (a == null || b == null) {\n    return 0;\n  }\n  return a + b;\n}',
          language: 'javascript',
          analysis_types: ['syntax', 'complexity', 'security', 'style'],
          include_suggestions: true
        }
      }
    }, null, 2));

    logger.info('');
    logger.info('=== Connection Information ===');
    logger.info('Embeddings Server: ws://localhost:8081');
    logger.info('Summarize Server: ws://localhost:8082');
    logger.info('Code Patch Server: ws://localhost:8083');
    logger.info('');
    logger.info('Each server supports the MCP protocol with:');
    logger.info('- JSON-RPC 2.0 messaging');
    logger.info('- Tool invocation capabilities');
    logger.info('- Resource access');
    logger.info('- Rate limiting and caching');
    logger.info('- Comprehensive tracing and logging');
  }
}

/**
 * Main function to run the example
 */
export async function runMCPServerExample(): Promise<void> {
  const example = new MCPServerExample();

  try {
    // Start all servers
    await example.startServers();

    // Demonstrate usage
    await example.demonstrateUsage();

    // Keep servers running for demonstration
    logger.info('MCP servers are running. Press Ctrl+C to stop.');

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await example.stopServers();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await example.stopServers();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to run MCP server example', { error });
    await example.stopServers();
    process.exit(1);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  runMCPServerExample().catch((error) => {
    logger.error('Unhandled error in MCP server example', { error });
    process.exit(1);
  });
}
