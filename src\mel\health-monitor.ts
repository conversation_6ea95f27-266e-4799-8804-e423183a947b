import { EventEmitter } from 'events';
import { ModelProvider, ProviderHealth, HealthStatus } from './provider-interface';
import { logger } from '@/utils/logger';
import { applicationMetrics } from '@/integration/observability-hub';

export interface HealthCheck {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  interval: number; // milliseconds
  timeout: number; // milliseconds
  retries: number;
  critical: boolean; // If true, failure affects overall system health
  dependencies: string[]; // Other health checks this depends on
  checker: HealthChecker;
  metadata?: Record<string, any>;
}

export interface HealthChecker {
  check(): Promise<HealthCheckResult>;
}

export interface HealthCheckResult {
  status: HealthStatus;
  latency: number;
  details?: Record<string, any>;
  error?: string;
  timestamp: Date;
}

export interface SystemHealth {
  status: HealthStatus;
  checks: Record<string, HealthCheckResult>;
  uptime: number;
  timestamp: Date;
  summary: {
    total: number;
    healthy: number;
    degraded: number;
    unhealthy: number;
    critical_failures: number;
  };
}

export interface HealthMonitorConfig {
  enabled: boolean;
  globalInterval: number;
  globalTimeout: number;
  retryDelay: number;
  alertThresholds: {
    degraded: number; // percentage of unhealthy checks
    critical: number; // percentage of critical failures
  };
  persistence: {
    enabled: boolean;
    retention: number; // days
  };
}

export interface HealthAlert {
  id: string;
  severity: AlertSeverity;
  checkId: string;
  checkName: string;
  message: string;
  timestamp: Date;
  resolved?: Date;
  metadata?: Record<string, any>;
}

export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export class HealthMonitor extends EventEmitter {
  private checks: Map<string, HealthCheck> = new Map();
  private checkResults: Map<string, HealthCheckResult> = new Map();
  private checkTimers: Map<string, NodeJS.Timeout> = new Map();
  private providers: Map<string, ModelProvider> = new Map();
  private alerts: Map<string, HealthAlert> = new Map();
  private config: HealthMonitorConfig;
  private systemStartTime: Date;

  constructor(config?: Partial<HealthMonitorConfig>) {
    super();
    
    this.config = {
      enabled: true,
      globalInterval: 30000, // 30 seconds
      globalTimeout: 5000, // 5 seconds
      retryDelay: 1000, // 1 second
      alertThresholds: {
        degraded: 25, // 25% unhealthy = degraded
        critical: 50, // 50% unhealthy = critical
      },
      persistence: {
        enabled: false,
        retention: 7, // 7 days
      },
      ...config,
    };

    this.systemStartTime = new Date();
    
    this.initializeSystemChecks();
    
    if (this.config.enabled) {
      this.startMonitoring();
    }

    logger.info('HealthMonitor initialized', { config: this.config });
  }

  private initializeSystemChecks(): void {
    // System resource health check
    this.addCheck({
      id: 'system-resources',
      name: 'System Resources',
      description: 'Check CPU, memory, and disk usage',
      enabled: true,
      interval: this.config.globalInterval,
      timeout: this.config.globalTimeout,
      retries: 2,
      critical: true,
      dependencies: [],
      checker: new SystemResourceChecker(),
    });

    // Database connectivity check
    this.addCheck({
      id: 'database-connectivity',
      name: 'Database Connectivity',
      description: 'Check database connection and response time',
      enabled: true,
      interval: this.config.globalInterval,
      timeout: this.config.globalTimeout,
      retries: 3,
      critical: true,
      dependencies: [],
      checker: new DatabaseChecker(),
    });

    // External service connectivity
    this.addCheck({
      id: 'external-services',
      name: 'External Services',
      description: 'Check connectivity to external dependencies',
      enabled: true,
      interval: this.config.globalInterval * 2, // Less frequent
      timeout: this.config.globalTimeout,
      retries: 2,
      critical: false,
      dependencies: [],
      checker: new ExternalServiceChecker(),
    });
  }

  private startMonitoring(): void {
    // Start health checks
    for (const [checkId, check] of this.checks.entries()) {
      if (check.enabled) {
        this.startHealthCheck(checkId);
      }
    }

    // Start provider monitoring
    this.startProviderMonitoring();

    logger.info('Health monitoring started');
  }

  private startHealthCheck(checkId: string): void {
    const check = this.checks.get(checkId);
    if (!check) return;

    // Initial check
    this.performHealthCheck(checkId);

    // Schedule recurring checks
    const timer = setInterval(() => {
      this.performHealthCheck(checkId);
    }, check.interval);

    this.checkTimers.set(checkId, timer);
  }

  private async performHealthCheck(checkId: string): Promise<void> {
    const check = this.checks.get(checkId);
    if (!check || !check.enabled) return;

    const startTime = Date.now();
    let result: HealthCheckResult;

    try {
      // Check dependencies first
      if (check.dependencies.length > 0) {
        const dependencyFailure = this.checkDependencies(check.dependencies);
        if (dependencyFailure) {
          result = {
            status: HealthStatus.UNHEALTHY,
            latency: Date.now() - startTime,
            error: `Dependency failure: ${dependencyFailure}`,
            timestamp: new Date(),
          };
          this.processHealthCheckResult(checkId, result);
          return;
        }
      }

      // Perform the actual health check with timeout
      result = await this.executeWithTimeout(
        check.checker.check(),
        check.timeout
      );

    } catch (error) {
      result = {
        status: HealthStatus.UNHEALTHY,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      };

      // Retry if configured
      if (check.retries > 0) {
        logger.debug('Health check failed, retrying', {
          checkId,
          error: result.error,
          retriesLeft: check.retries,
        });

        await this.delay(this.config.retryDelay);
        
        // Recursive retry (decreasing retries)
        const retryCheck = { ...check, retries: check.retries - 1 };
        this.checks.set(checkId, retryCheck);
        
        return this.performHealthCheck(checkId);
      }
    }

    this.processHealthCheckResult(checkId, result);
  }

  private processHealthCheckResult(checkId: string, result: HealthCheckResult): void {
    const previousResult = this.checkResults.get(checkId);
    this.checkResults.set(checkId, result);

    // Record metrics
    applicationMetrics.recordCounter('health_checks_total', 1, {
      check_id: checkId,
      status: result.status,
    });

    applicationMetrics.recordHistogram('health_check_duration_ms', result.latency, {
      check_id: checkId,
    });

    // Check for status changes
    if (previousResult && previousResult.status !== result.status) {
      this.handleStatusChange(checkId, previousResult.status, result.status);
    }

    // Generate alerts if needed
    this.evaluateAlerts(checkId, result);

    this.emit('health_check_completed', {
      checkId,
      result,
      statusChanged: previousResult?.status !== result.status,
    });
  }

  private handleStatusChange(checkId: string, previousStatus: HealthStatus, newStatus: HealthStatus): void {
    const check = this.checks.get(checkId);
    if (!check) return;

    logger.info('Health check status changed', {
      checkId,
      checkName: check.name,
      previousStatus,
      newStatus,
      critical: check.critical,
    });

    // Generate status change alert
    const severity = this.getAlertSeverity(newStatus, check.critical);
    const alert: HealthAlert = {
      id: crypto.randomUUID(),
      severity,
      checkId,
      checkName: check.name,
      message: `Health check status changed from ${previousStatus} to ${newStatus}`,
      timestamp: new Date(),
    };

    this.alerts.set(alert.id, alert);

    applicationMetrics.recordCounter('health_status_changes_total', 1, {
      check_id: checkId,
      previous_status: previousStatus,
      new_status: newStatus,
    });

    this.emit('status_change', {
      checkId,
      checkName: check.name,
      previousStatus,
      newStatus,
      alert,
    });
  }

  private evaluateAlerts(checkId: string, result: HealthCheckResult): void {
    const check = this.checks.get(checkId);
    if (!check) return;

    if (result.status === HealthStatus.UNHEALTHY && check.critical) {
      const alertId = `${checkId}-critical-failure`;
      
      if (!this.alerts.has(alertId)) {
        const alert: HealthAlert = {
          id: alertId,
          severity: AlertSeverity.CRITICAL,
          checkId,
          checkName: check.name,
          message: `Critical health check failure: ${result.error || 'Unknown error'}`,
          timestamp: new Date(),
        };

        this.alerts.set(alertId, alert);
        this.emit('alert', alert);
      }
    }
  }

  private getAlertSeverity(status: HealthStatus, critical: boolean): AlertSeverity {
    switch (status) {
      case HealthStatus.HEALTHY:
        return AlertSeverity.INFO;
      case HealthStatus.DEGRADED:
        return critical ? AlertSeverity.WARNING : AlertSeverity.INFO;
      case HealthStatus.UNHEALTHY:
        return critical ? AlertSeverity.CRITICAL : AlertSeverity.ERROR;
      default:
        return AlertSeverity.WARNING;
    }
  }

  private checkDependencies(dependencies: string[]): string | null {
    for (const depId of dependencies) {
      const result = this.checkResults.get(depId);
      if (!result || result.status === HealthStatus.UNHEALTHY) {
        return depId;
      }
    }
    return null;
  }

  private async executeWithTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Health check timeout')), timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private startProviderMonitoring(): void {
    // Monitor all registered providers
    setInterval(async () => {
      for (const [providerId, provider] of this.providers.entries()) {
        try {
          const isHealthy = await provider.isHealthy();
          const health = provider.getHealth();
          
          applicationMetrics.recordCounter('provider_health_checks_total', 1, {
            provider_id: providerId,
            status: health.status,
          });

          if (health.latency !== undefined) {
            applicationMetrics.recordHistogram('provider_health_check_duration_ms', health.latency, {
              provider_id: providerId,
            });
          }

        } catch (error) {
          logger.warn('Provider health check failed', {
            providerId,
            error,
          });
        }
      }
    }, this.config.globalInterval);
  }

  // Public API methods
  addCheck(check: HealthCheck): void {
    this.checks.set(check.id, check);
    
    if (check.enabled && this.config.enabled) {
      this.startHealthCheck(check.id);
    }

    logger.info('Health check added', {
      checkId: check.id,
      checkName: check.name,
      enabled: check.enabled,
    });
  }

  removeCheck(checkId: string): boolean {
    const timer = this.checkTimers.get(checkId);
    if (timer) {
      clearInterval(timer);
      this.checkTimers.delete(checkId);
    }

    const removed = this.checks.delete(checkId);
    this.checkResults.delete(checkId);

    if (removed) {
      logger.info('Health check removed', { checkId });
    }

    return removed;
  }

  enableCheck(checkId: string): boolean {
    const check = this.checks.get(checkId);
    if (!check) return false;

    check.enabled = true;
    this.startHealthCheck(checkId);

    logger.info('Health check enabled', { checkId });
    return true;
  }

  disableCheck(checkId: string): boolean {
    const check = this.checks.get(checkId);
    if (!check) return false;

    check.enabled = false;
    
    const timer = this.checkTimers.get(checkId);
    if (timer) {
      clearInterval(timer);
      this.checkTimers.delete(checkId);
    }

    logger.info('Health check disabled', { checkId });
    return true;
  }

  addProvider(providerId: string, provider: ModelProvider): void {
    this.providers.set(providerId, provider);
    logger.info('Provider added to health monitoring', { providerId });
  }

  removeProvider(providerId: string): boolean {
    const removed = this.providers.delete(providerId);
    if (removed) {
      logger.info('Provider removed from health monitoring', { providerId });
    }
    return removed;
  }

  async getSystemHealth(): Promise<SystemHealth> {
    const now = new Date();
    const checks: Record<string, HealthCheckResult> = {};
    let healthy = 0;
    let degraded = 0;
    let unhealthy = 0;
    let criticalFailures = 0;

    for (const [checkId, result] of this.checkResults.entries()) {
      checks[checkId] = result;
      
      switch (result.status) {
        case HealthStatus.HEALTHY:
          healthy++;
          break;
        case HealthStatus.DEGRADED:
          degraded++;
          break;
        case HealthStatus.UNHEALTHY:
          unhealthy++;
          const check = this.checks.get(checkId);
          if (check?.critical) {
            criticalFailures++;
          }
          break;
      }
    }

    const total = healthy + degraded + unhealthy;
    let overallStatus = HealthStatus.HEALTHY;

    if (criticalFailures > 0) {
      overallStatus = HealthStatus.UNHEALTHY;
    } else if (unhealthy / total > this.config.alertThresholds.critical / 100) {
      overallStatus = HealthStatus.UNHEALTHY;
    } else if (unhealthy / total > this.config.alertThresholds.degraded / 100) {
      overallStatus = HealthStatus.DEGRADED;
    }

    return {
      status: overallStatus,
      checks,
      uptime: now.getTime() - this.systemStartTime.getTime(),
      timestamp: now,
      summary: {
        total,
        healthy,
        degraded,
        unhealthy,
        critical_failures: criticalFailures,
      },
    };
  }

  getCheckResult(checkId: string): HealthCheckResult | undefined {
    return this.checkResults.get(checkId);
  }

  getAllChecks(): HealthCheck[] {
    return Array.from(this.checks.values());
  }

  getActiveAlerts(): HealthAlert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert) return false;

    alert.resolved = new Date();
    logger.info('Alert resolved', { alertId, checkId: alert.checkId });
    
    this.emit('alert_resolved', alert);
    return true;
  }

  async shutdown(): Promise<void> {
    // Clear all timers
    for (const timer of this.checkTimers.values()) {
      clearInterval(timer);
    }
    this.checkTimers.clear();

    logger.info('HealthMonitor shutdown complete');
  }
}

// Built-in health checkers
class SystemResourceChecker implements HealthChecker {
  async check(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Check memory usage (threshold: 90%)
      const memPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      const status = memPercentage > 90 ? HealthStatus.UNHEALTHY : 
                    memPercentage > 75 ? HealthStatus.DEGRADED : HealthStatus.HEALTHY;

      return {
        status,
        latency: Date.now() - startTime,
        details: {
          memory: {
            used: memUsage.heapUsed,
            total: memUsage.heapTotal,
            percentage: memPercentage,
          },
          cpu: cpuUsage,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      };
    }
  }
}

class DatabaseChecker implements HealthChecker {
  async check(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Mock database check - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 10)); // Simulate DB query
      
      return {
        status: HealthStatus.HEALTHY,
        latency: Date.now() - startTime,
        details: {
          connection: 'active',
          responseTime: Date.now() - startTime,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Database connection failed',
        timestamp: new Date(),
      };
    }
  }
}

class ExternalServiceChecker implements HealthChecker {
  async check(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Mock external service check - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 50)); // Simulate API call
      
      return {
        status: HealthStatus.HEALTHY,
        latency: Date.now() - startTime,
        details: {
          services: ['service1', 'service2'],
          allHealthy: true,
        },
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        status: HealthStatus.DEGRADED, // External services are not critical
        latency: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'External service check failed',
        timestamp: new Date(),
      };
    }
  }
}

export const healthMonitor = new HealthMonitor();