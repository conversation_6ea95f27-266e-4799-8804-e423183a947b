import { metrics } from '@opentelemetry/api';
import { logger } from '@/utils/logger';

export interface MetricsCollector {
  recordCounter(name: string, value: number, labels?: Record<string, string>): void;
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void;
  recordGauge(name: string, value: number, labels?: Record<string, string>): void;
}

export class ApplicationMetrics implements MetricsCollector {
  private meter = metrics.getMeter('custom-agent-system', '2.0.0');
  
  // Counters
  private requestCounter = this.meter.createCounter('http_requests_total', {
    description: 'Total number of HTTP requests',
  });
  
  private errorCounter = this.meter.createCounter('errors_total', {
    description: 'Total number of errors',
  });
  
  private agentExecutionCounter = this.meter.createCounter('agent_executions_total', {
    description: 'Total number of agent executions',
  });

  // Histograms
  private requestDuration = this.meter.createHistogram('http_request_duration_seconds', {
    description: 'HTTP request duration in seconds',
    boundaries: [0.001, 0.01, 0.1, 0.5, 1, 2, 5, 10],
  });
  
  private agentExecutionDuration = this.meter.createHistogram('agent_execution_duration_seconds', {
    description: 'Agent execution duration in seconds',
    boundaries: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
  });

  // Gauges
  private activeConnections = this.meter.createUpDownCounter('active_connections', {
    description: 'Number of active connections',
  });
  
  private memoryUsage = this.meter.createObservableGauge('memory_usage_bytes', {
    description: 'Memory usage in bytes',
  });

  constructor() {
    this.setupSystemMetrics();
  }

  private setupSystemMetrics(): void {
    // Register callback for memory usage
    this.memoryUsage.addCallback((result) => {
      const memUsage = process.memoryUsage();
      result.observe(memUsage.heapUsed, { type: 'heap_used' });
      result.observe(memUsage.heapTotal, { type: 'heap_total' });
      result.observe(memUsage.rss, { type: 'rss' });
      result.observe(memUsage.external, { type: 'external' });
    });
  }

  recordCounter(name: string, value: number, labels: Record<string, string> = {}): void {
    try {
      const counter = this.meter.createCounter(name);
      counter.add(value, labels);
    } catch (error) {
      logger.error('Failed to record counter metric', { name, value, labels, error });
    }
  }

  recordHistogram(name: string, value: number, labels: Record<string, string> = {}): void {
    try {
      const histogram = this.meter.createHistogram(name);
      histogram.record(value, labels);
    } catch (error) {
      logger.error('Failed to record histogram metric', { name, value, labels, error });
    }
  }

  recordGauge(name: string, value: number, labels: Record<string, string> = {}): void {
    try {
      const gauge = this.meter.createUpDownCounter(name);
      gauge.add(value, labels);
    } catch (error) {
      logger.error('Failed to record gauge metric', { name, value, labels, error });
    }
  }

  // HTTP Metrics
  recordHttpRequest(method: string, route: string, statusCode: number): void {
    this.requestCounter.add(1, {
      method,
      route,
      status_code: statusCode.toString(),
    });
  }

  recordHttpRequestDuration(duration: number, method: string, route: string, statusCode: number): void {
    this.requestDuration.record(duration, {
      method,
      route,
      status_code: statusCode.toString(),
    });
  }

  // Error Metrics
  recordError(type: string, component: string): void {
    this.errorCounter.add(1, { type, component });
  }

  // Agent Metrics
  recordAgentExecution(agentType: string, operation: string, success: boolean): void {
    this.agentExecutionCounter.add(1, {
      agent_type: agentType,
      operation,
      success: success.toString(),
    });
  }

  recordAgentExecutionDuration(duration: number, agentType: string, operation: string): void {
    this.agentExecutionDuration.record(duration, {
      agent_type: agentType,
      operation,
    });
  }

  // Connection Metrics
  incrementActiveConnections(): void {
    this.activeConnections.add(1);
  }

  decrementActiveConnections(): void {
    this.activeConnections.add(-1);
  }
}

export const applicationMetrics = new ApplicationMetrics();